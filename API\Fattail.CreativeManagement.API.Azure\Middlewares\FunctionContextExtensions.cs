﻿using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares.Errors;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using System.Net;
using System.Reflection;

namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal static class FunctionContextExtensions
{
    internal static bool TryGetAttribute<T> (this FunctionContext context, out T? attribute)
        where T : Attribute
    {
        attribute = default;
        string functionEntryPoint = context.FunctionDefinition.EntryPoint;
        var assemblyType = Type.GetType(functionEntryPoint[..functionEntryPoint.LastIndexOf('.')])!;
        MethodInfo methodInfo = assemblyType.GetMethod(functionEntryPoint[(functionEntryPoint.LastIndexOf('.') + 1)..])!;
        if (methodInfo.GetCustomAttribute(typeof(T), false) is T customAttribute)
        {
            attribute = customAttribute;
            return true;
        }

        return false;
    }

    internal static Result<string?> GetHeaderValue (this HttpRequestData request, string headerName)
    {
        return request.Headers.TryGetValues(headerName, out IEnumerable<string> values)
            ? values.FirstOrDefault()
            : Result.Fail(new HttpHeaderError(headerName));
    }

    internal static async Task AssignErrorResponse (this HttpRequestData request, ErrorInformation errorInformation, HttpStatusCode statusCode)
    {
        HttpResponseData response = request.CreateResponse(statusCode);

        await response.WriteAsJsonAsync(errorInformation);

        request.FunctionContext.GetInvocationResult().Value = response;
    }
}