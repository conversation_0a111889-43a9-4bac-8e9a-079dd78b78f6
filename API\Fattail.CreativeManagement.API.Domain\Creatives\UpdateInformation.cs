﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Common;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives;

public class UpdateInformation : ValueObject
{
    private UpdateInformation(string updaterName, DateTime updateDateTime)
    {
        UpdaterName = updaterName;
        UpdateDateTime = updateDateTime;
    }

    public string UpdaterName { get; }

    public DateTime UpdateDateTime { get; }

    public static Result<UpdateInformation> Create(string? updatedBy, DateTime? updateDateTime)
    {
        var result = new Result();

        if (string.IsNullOrWhiteSpace(updatedBy))
        {
            result.WithError(new RequiredValueMissingError(nameof(updatedBy), nameof(Creative)));
        }

        if (updateDateTime == null)
        {
            result.WithError(new RequiredValueMissingError(nameof(updateDateTime), nameof(Creative)));
        }

        if(result.IsFailed)
        {
            return result;
        }

        return new UpdateInformation(updatedBy!, updateDateTime!.Value);
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return UpdaterName;
        yield return UpdateDateTime;
    }
}