﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Options;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class PredefinedCreativeFieldRepository (
    ICosmosDbContainerFactory cosmosDbContainerFactory,
    IMapper mapper,
    IOrganizationContext organizationContext,
    IOptions<ParallelExecutionSettings> parallelExecutionSettings)
    :
        CosmosDbRepository<CreativeField, CreativeFieldId, Entities.CreativeField>(cosmosDbContainerFactory, mapper,
            organizationContext, parallelExecutionSettings),
        IPredefinedCreativeFieldRepository
{
    public override string ContainerName => "CreativeFields";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey(PredefinedPartitions.Shared.ToString());
    }

    protected override async Task<ItemResponse<Entities.CreativeField>> CreateEntityAsync (CreativeField entity)
    {
        Entities.CreativeField cosmosEntity = PrepareCosmosEntity(entity, ActionType.Create);
        cosmosEntity.OrgId = PredefinedPartitions.Shared.ToString();
        return await _container.CreateItemAsync(cosmosEntity, ResolvePartitionKey());
    }
}