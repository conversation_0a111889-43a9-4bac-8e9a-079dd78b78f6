﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;

internal static class FieldTypeIsGuard
{
    public static ref readonly Guard.ArgumentInfo<CreativeFieldIdentifier> FieldTypeIs (
        in this Guard.ArgumentInfo<CreativeFieldIdentifier> argument, CreativeFieldType shouldBe)
    {
        if (argument.Value.Type != shouldBe)
        {
            throw Guard.Fail(new ArgumentException(
                $"Creative field must be of type {shouldBe}",
                argument.Name));
        }

        return ref argument;
    }
}