﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Factory;

public class CreativeFactory (
    ICreativeFilesManager creativeFilesManager,
    ISanitizerStrategyProvider sanitizerStrategyProvider)
    : ICreativeFactory
{
    public async Task<Result<Creative>> Create (CreateCreativeRequest createCreativeRequest)
    {
        var result = new Result();

        if (string.IsNullOrWhiteSpace(createCreativeRequest.Name))
        {
            result.WithError(new RequiredValueMissingError(nameof(createCreativeRequest.Name), nameof(Creative)));
        }

        if (createCreativeRequest.AdBookClientId is <= 0)
        {
            result.WithError(new RequiredValueMissingError("AdBook client", nameof(Creative)));
        }

        if (createCreativeRequest.CampaignId <= 0)
        {
            result.WithError(new RequiredValueMissingError("campaign", nameof(Creative)));
        }

        if (string.IsNullOrWhiteSpace(createCreativeRequest.UpdatedBy))
        {
            result.WithError(new RequiredValueMissingError(nameof(createCreativeRequest.UpdatedBy), nameof(Creative)));
        }

        if (createCreativeRequest.CreativeTemplate is null)
        {
            result.WithError(new InvalidValueError("creative template", nameof(Creative)));
            return result;
        }

        var creativeFields = new Dictionary<CreativeFieldIdentifier, CreativeFieldValue>();

        foreach (CreativeTemplateCreativeField creativeTemplateField in createCreativeRequest.CreativeTemplate!
                     .CreativeFields)
        {
            if (creativeTemplateField.Type == CreativeFieldType.SectionDivider)
            {
                continue;
            }

            var creativeFieldIdentifier =
                CreativeFieldIdentifier.Create(creativeTemplateField.Id, creativeTemplateField.Type);
            Result<CreativeFieldValue> creativeField =
                createCreativeRequest.InitialFieldValues.TryGetValue(creativeFieldIdentifier.Id, out object? fieldValue)
                    ? await CreativeFieldValue.Create(creativeFieldIdentifier, fieldValue,
                        creativeTemplateField.ValidationRules, sanitizerStrategyProvider, creativeFilesManager)
                    : await CreativeFieldValue.CreateWithoutValue(creativeFieldIdentifier,
                        creativeTemplateField.ValidationRules);

            if (creativeField.IsFailed)
            {
                result.WithErrors(creativeField.Errors);
                continue;
            }

            creativeFields.Add(creativeFieldIdentifier, creativeField.Value);
        }

        if (result.IsFailed)
        {
            return result;
        }
        //TODO: Once POST creative v3 is deleted, constructor will receive an empty list of line item ids
        return new Creative(createCreativeRequest.Id, createCreativeRequest.Name!, createCreativeRequest.AdBookClientId, createCreativeRequest.AdBookAdId, createCreativeRequest.CampaignId,
            createCreativeRequest.LineItemIds ?? new HashSet<long>(), createCreativeRequest.CreativeTemplate.Id,
            creativeFields, CreativeStatus.PendingApproval, createCreativeRequest.UpdatedBy!, createCreativeRequest.CreatedDate, null);
    }
}