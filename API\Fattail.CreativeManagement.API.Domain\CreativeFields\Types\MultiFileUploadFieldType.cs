using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class MultiFileUploadFieldType : CreativeFieldType
{
    internal MultiFileUploadFieldType () : base(nameof(CreativeFieldTypeEnum.MultiFileUpload), (int)CreativeFieldTypeEnum.MultiFileUpload)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return DefaultCreativeFieldSettings.Instance;
    }
}
