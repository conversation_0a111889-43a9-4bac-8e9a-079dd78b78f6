﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares.Errors;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using System.Net;
using Fattail.CreativeManagement.API.Application;

namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal sealed class OrganizationContextHttpTriggerMiddleware : IFunctionsWorkerMiddleware
{
    public const string OrganizationIdHeaderName = "Org-Id";

    private readonly IMapper _mapper;
    private readonly IReadOnlyList<OrganizationSettings> _organizationsSettings;

    public OrganizationContextHttpTriggerMiddleware (
        IMapper mapper,
        IOptions<List<OrganizationSettings>> organizationsSettings
        )
    {
        _mapper = mapper;
        _organizationsSettings = organizationsSettings.Value;
    }

    public async Task Invoke (FunctionContext context, FunctionExecutionDelegate next)
    {
        HttpRequestData request = await context.GetHttpRequestDataAsync();

        Result<string> getOrganizationHeaderResult = request.GetHeaderValue(OrganizationIdHeaderName);

        if (getOrganizationHeaderResult.IsFailed)
        {
            await request.AssignErrorResponse(_mapper.Map<ErrorInformation>(getOrganizationHeaderResult), HttpStatusCode.BadRequest);
            return;
        }

        string organizationId = getOrganizationHeaderResult.Value;

        if (organizationId == "0" || _organizationsSettings.All(organizationSettings => organizationSettings.OrgId.ToString() != organizationId))
        {
            await request.AssignErrorResponse(new ErrorInformation(
                "Organization id passed in Org-Id header does not exist.", 
                (int)ApiErrorType.InvalidHeaderOrgIdValue, 
                ApiErrorType.InvalidHeaderOrgIdValue.ToString(), 
                new Dictionary<string, string>()), HttpStatusCode.BadRequest);
            return;
        }

        IOrganizationContext organizationContext = context.InstanceServices.GetRequiredService<IOrganizationContext>();

        organizationContext.SetOrganizationId(long.Parse(organizationId));

        await next(context);
    }
}