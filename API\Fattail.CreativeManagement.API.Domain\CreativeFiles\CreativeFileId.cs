﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;

public class CreativeFileId : ValueObject
{
    private readonly long _value;

    public CreativeFileId (long creativeFileId)
    {
        Guard.Argument(creativeFileId, nameof(creativeFileId)).Positive();

        _value = creativeFileId;
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return _value;
    }

    public override string ToString ()
    {
        return _value.ToString();
    }

    public static implicit operator long (CreativeFileId creativeFileId)
    {
        return creativeFileId._value;
    }

    public static explicit operator CreativeFileId (long creativeFileId)
    {
        return new CreativeFileId(creativeFileId);
    }

    public static explicit operator CreativeFileId (string creativeFileId)
    {
        return new CreativeFileId(long.Parse(creativeFileId));
    }
}