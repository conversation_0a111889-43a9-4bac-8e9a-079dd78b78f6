﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;
using NUnit.Framework;
using FluentAssertions;
using FluentResults.Extensions.FluentAssertions;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields
{
    [TestFixture]
    public class CreateValidationRuleTests
    {
        private readonly CreativeFieldValidationRuleType _creativeFieldValidationRuleType = CreativeFieldValidationRuleType.FileUploadExtensions;
        private readonly List<string> _options = new()
        {
            ".jpg",
            ".png"
        };

        [Test]
        public void Validation_rule_can_be_created ()
        {
            Result<ValidationRule> validationRule = ValidationRule.Create(_creativeFieldValidationRuleType, _options, new CreativeFieldId(123));

            validationRule.Should().BeSuccess();
            validationRule.Value.Should().NotBeNull();
            validationRule.Value.Type.Should().Be(_creativeFieldValidationRuleType);
            validationRule.Value.Options.Should().BeEquivalentTo(_options);
        }
    }
}
