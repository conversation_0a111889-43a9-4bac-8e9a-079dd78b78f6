﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateAdd;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2.Add;

internal sealed class CreativeTemplateAddAutoMapperProfile : Profile
{
    public CreativeTemplateAddAutoMapperProfile ()
    {
        CreateMap<CreativeTemplateAddRequestV2, CreativeTemplateAddCommand>();
        CreateMap<CreativeTemplateCreativeFieldRequest, CreativeTemplateCreativeFieldDto>();
        CreateMap<ValidationRuleRequest, ValidationRuleDto>();
        CreateMap<CreativeFieldValidationRuleType,
            Fattail.CreativeManagement.API.Domain.CreativeTemplates.CreativeFieldValidationRuleType>();
        CreateMap<long, CreativeFieldId>().ConstructUsing(src => new CreativeFieldId(src));
    }
}