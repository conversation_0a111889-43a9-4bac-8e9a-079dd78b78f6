using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

public abstract class CreativeFieldValue
{
    public string Id { get; set; }
    public string Name { get; set; }
    public CreativeFieldTypeEnum Type { get; set; }
}

public abstract class CreativeFieldValue<TValue> : CreativeFieldValue
{
    public TValue Value { get; set; }
}

public sealed class MultiFileUploadFieldValue : CreativeFieldValue<CreativeFileValue[]>
{
}

public sealed class FileUploadFieldValue : CreativeFieldValue<CreativeFileValue>
{
}

public sealed class CreativeFileValue
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Extension { get; set; }
    public CreativeFileTypeEnum Type { get; set; }
    public string Location { get; set; }
    public long Size { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new ();
}

public sealed class SingleLineTextFieldValue : CreativeFieldValue<string>
{
}

public sealed class MultiSelectOptionFieldValue : CreativeFieldValue<long[]>
{
}

public sealed class SingleSelectOptionFieldValue : CreativeFieldValue<long?>
{
}

public sealed class MultiLineTextFieldValue : CreativeFieldValue<string>
{
}