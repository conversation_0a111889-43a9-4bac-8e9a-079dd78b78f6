﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class SectionDividerFieldValueTests
{
    [Test]
    public async Task Section_divider_field_value_cant_be_created ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SectionDivider);
        
        Result<CreativeFieldValue> sectionDividerFieldValueCreateResult = await CreativeFieldValue.Create(creativeField, "abcde", new List<ValidationRule>());

        sectionDividerFieldValueCreateResult.Should().BeFailure().And.HaveReason(new CreativeFieldValueNotAdmittedError(CreativeFieldType.SectionDivider, nameof(CreativeFieldValue)));;
    }
}