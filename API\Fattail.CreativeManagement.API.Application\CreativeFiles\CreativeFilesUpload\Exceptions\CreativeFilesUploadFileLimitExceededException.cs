﻿using System.Runtime.Serialization;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload.Exceptions;

internal class CreativeFilesUploadFileLimitExceededException : Exception
{
    public CreativeFilesUploadFileLimitExceededException ()
    {
    }

    public CreativeFilesUploadFileLimitExceededException (string? message) : base(message)
    {
    }

    public CreativeFilesUploadFileLimitExceededException (string? message, Exception? innerException) : base(message,
        innerException)
    {
    }

    protected CreativeFilesUploadFileLimitExceededException (SerializationInfo info, StreamingContext context) :
        base(info, context)
    {
    }
}