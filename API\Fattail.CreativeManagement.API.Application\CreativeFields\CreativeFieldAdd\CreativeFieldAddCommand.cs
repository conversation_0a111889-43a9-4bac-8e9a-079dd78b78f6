﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;

public record CreativeFieldAddCommand (string? Name, CreativeFieldTypeEnum? Type,
    IReadOnlyList<SelectOption> Options) : IRequest<Result<CreativeFieldResult>>;

public record SelectOption (
    long Id,
    string Description);