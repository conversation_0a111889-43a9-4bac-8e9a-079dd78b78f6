using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Errors;

internal sealed class CreativeAlreadyMigratedError : ErrorBase
{
    public CreativeAlreadyMigratedError ( long adBookAdId, string entity) :
        base($"Creative with AdBookAdId {adBookAdId} is already migrated",
            ErrorType.CreativeIsMigratedError)
    {
        Metadata.Add(nameof(adBookAdId), adBookAdId);
        Metadata.Add(nameof(entity), entity);
    }
}