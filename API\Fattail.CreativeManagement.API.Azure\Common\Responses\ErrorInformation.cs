﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Common.Responses;

internal sealed record ErrorInformation(
    [property: OpenApiProperty(Description = "The error message.")]
    string Message,
    [property: OpenApiProperty(Description = "The error code.")]
    int Code,
    [property:OpenApiProperty(Description = "The error type.")]
    string Type,
    [property:
        OpenApiProperty(Description =
            "The error metadata which contains extra useful information (like the name of a file for example).")]
    Dictionary<string, string> Metadata);