using Azure.Storage.Blobs;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace Fattail.CreativeManagement.API.Azure.Triggers.BlobStorage.CreativesMigration;

public class CreativesMigrationTrigger
{
    private readonly ILogger<CreativesMigrationTrigger> _logger;
    private const string ContainerName = "creative-migration-reports";
    private const string FileExtension = ".csv";

    public CreativesMigrationTrigger (ILogger<CreativesMigrationTrigger> logger)
    {
        _logger = logger;
    }

    [Function(nameof(CreativesMigrationTrigger))]
    public async Task Run (
        [BlobTrigger($"{ContainerName}/{{orgId}}/input/{{name}}{FileExtension}", Connection = "AzureWebJobsStorage")] BlobClient blobClient,
        [BlobInput($"{ContainerName}")] BlobContainerClient blobContainerClient,
        [DurableClient] DurableTaskClient client,
        string name,
        long orgId)
    {
        _logger.LogInformation("Processing blob {BlobName} trigger for organization {OrgId}", name, orgId);
        
        await using Stream? blobStream = await blobClient.OpenReadAsync();
        using var blobStreamReader = new StreamReader(blobStream);
        using var csv = new CsvReader(blobStreamReader, new CsvConfiguration(CultureInfo.InvariantCulture));
        csv.Context.RegisterClassMap<AdBookCreativeCsvMap>();

        IEnumerable<AdBookCreative> data = csv.GetRecords<AdBookCreative>().ToHashSet();
        
        string instanceId = await client.ScheduleNewOrchestrationInstanceAsync(nameof(CreativeMigrationOrchestrator),
            new CreativeMigrationOrchestratorModel(orgId, name,
                data.Where(creative => !string.IsNullOrEmpty(creative.CreativeName) && creative.OriginatedInAbOms)));
        
        _logger.LogInformation("Instance id: {InstanceId}", instanceId);
        
        BlobClient? processedBlobClient = blobContainerClient.GetBlobClient($"/{orgId}/output/{DateTime.Now:yyyy.MM.dd}/{name}_{DateTime.Now:yyyy.MM.dd.HH.mm.ss}{FileExtension}");
        await processedBlobClient.StartCopyFromUriAsync(blobClient.Uri);
        await blobClient.DeleteIfExistsAsync();
    }
}