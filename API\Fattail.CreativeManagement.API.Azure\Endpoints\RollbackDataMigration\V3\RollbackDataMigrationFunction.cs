using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrateRollback;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrateRollback;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.RollbackDataMigration.V3;

public class RollbackDataMigrationFunction : BaseFunction
{
    private readonly IOrganizationContext _organizationContext;
    private static readonly List<long> _allowedOrganizationsToRollback = new() { 100687, 900999 };

    public RollbackDataMigrationFunction (IOrganizationContext organizationContext, IMediator mediator, IMapper mapper)
        : base(mediator, mapper)
    {
        _organizationContext = organizationContext;
    }

    [OpenApiOperation(
        "Run  rollback Data Migration Function v3",
        new[] { "v3" },
        Summary = "Run rollback Data Migration Function v3 - Internal usage, no need to version it")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter(
        "rollbackAction",
        Required = true,
        In = ParameterLocation.Path,
        Type = typeof(string))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NoContent)]
    [Function(nameof(RollbackDataMigrationFunction))]
    public async Task<HttpResponseData> Post (
        [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "v3/data-migration/rollback/{rollbackAction}")]
        HttpRequestData req, string rollbackAction)
    {
        if (!_allowedOrganizationsToRollback.Contains(_organizationContext.OrganizationId))
        {
            return await FromResult<ICollection<DataPopulationResponseModel>, ICollection<DataPopulationResponseModel>>(
                req,
                Result.Fail("Organization " + _organizationContext.OrganizationId +
                            " is not allowed to execute rollback"), HttpStatusCode.BadRequest);
        }
        
        if (!Enum.TryParse(rollbackAction, true, out RollbackAction rollbackActionEnum))
        {
            return await FromResult<ICollection<DataPopulationResponseModel>, ICollection<DataPopulationResponseModel>>(
                req, Result.Fail("Invalid rollback action, possible values are Full or Partial"), HttpStatusCode.BadRequest);
        }

        var creativeMigrateRollbackCommand = new CreativeMigrateRollbackCommand(rollbackActionEnum);
        var creativeFilesMigrateRollbackCommand = new CreativeFilesMigrateRollbackCommand(rollbackActionEnum);
        
        Task<Result> creativesRollbackTask = _mediator.Send(creativeMigrateRollbackCommand);
        Task<Result> creativeFilesRollbackTask = _mediator.Send(creativeFilesMigrateRollbackCommand);

        await Task.WhenAll(creativesRollbackTask, creativeFilesRollbackTask);

        return await
            FromResult<ICollection<DataPopulationResponseModel>,
                ICollection<DataPopulationResponseModel>>(req,
                Result.Merge(creativesRollbackTask.Result, creativeFilesRollbackTask.Result), HttpStatusCode.NoContent);
    }
}