﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Creative = Fattail.CreativeManagement.API.Domain.Creatives.Creative;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper.CustomTypeConverters;

internal sealed class CreativeTypeConverter (ICreativeTemplateRepository creativeTemplateRepository)
    : ITypeConverter<Creative, Entities.Creative>
{
    public Entities.Creative Convert (Creative source, Entities.Creative destination, ResolutionContext context)
    {
        var creative = new Entities.Creative
        {
            AdBookAdId = source.AdBookAdId?.ToString(),
            LastUpdatedOn = source.LastUpdatedOn,
            AdBookClientId = source.AdBookClientId?.ToString(),
            CampaignId = source.CampaignId.ToString(),
            LineItemIds = source.LineItemIds.Select(li => li.ToString()).ToList(),
            Id = source.Id.ToString(),
            LastUpdatedBy = source.LastUpdatedBy,
            Name = source.Name,
            Status = context.Mapper.Map<CreativeStatus>(source.Status),
            LastApproval = context.Mapper.Map<ApprovalInformation>(source.LastApproval)
        };

        Task<CreativeTemplate?> findCreativeTemplateTask =
            creativeTemplateRepository.FindByIdAsync<CreativeTemplate>(source.CreativeTemplateId);
        findCreativeTemplateTask.Wait();

        CreativeTemplate creativeTemplate = findCreativeTemplateTask.Result!;

        creative.CreativeTemplateId = creativeTemplate.Id;
        creative.CreativeTemplateName = creativeTemplate.Name;

        IReadOnlyDictionary<string, CreativeTemplateCreativeField> templateFields = creativeTemplate.CreativeFields
            .ToDictionary(creativeTemplateField => creativeTemplateField.Id,
                creativeTemplateField => creativeTemplateField)
            .AsReadOnly();

        CreativeFieldValue[] creativeFieldValues = context.Mapper.Map<CreativeFieldValue[]>(source.Fields);

        foreach (CreativeFieldValue creativeFieldValue in creativeFieldValues)
        {
            creativeFieldValue.Name = templateFields[creativeFieldValue.Id].Name;
        }

        creative.Fields = creativeFieldValues;

        return creative;
    }
}