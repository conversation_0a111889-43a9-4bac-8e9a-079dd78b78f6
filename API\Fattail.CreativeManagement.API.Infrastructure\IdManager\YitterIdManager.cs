﻿using Fattail.CreativeManagement.API.Domain.Common;
using Yitter.IdGenerator;

namespace Fattail.CreativeManagement.API.Infrastructure.IdManager;

internal class YitterIdManager : IIdManager
{
    public YitterIdManager ()
    {
        ushort workerId = Convert.ToUInt16(Thread.CurrentThread.ManagedThreadId);
        var idGeneratorOptions = new IdGeneratorOptions(workerId);
        YitIdHelper.SetIdGenerator(idGeneratorOptions);
    }

    public long GetId ()
    {
        return YitIdHelper.NextId();
    }
}