﻿using Fattail.CreativeManagement.API.Infrastructure.HttpClients;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Models;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Utils;
using FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Infrastructure.Tests.HttpClients.Utils
{
    [TestFixture(Category = "Infrastructure - Http Clients", TestName = "Authentication Manager")]
    public class AuthenticationManagerTests
    {
        private AuthenticationManager _authenticationManager;
        private Mock<IAuthenticationHttpClient> _authenticationHttpClient;

        [SetUp]
        public void SetUp ()
        {
            _authenticationHttpClient = new Mock<IAuthenticationHttpClient>();
            _authenticationManager = new AuthenticationManager(_authenticationHttpClient.Object);
        }

        [Test]
        public async Task Authentication_manager_gets_active_token ()
        {
            var jwt = new Jwt
            {
                AccessToken = "abcdef",
                ExpiresIn = 3600
            };

            _authenticationHttpClient.Setup(ahc => ahc.LoginAsync()).ReturnsAsync(jwt);

            string activeToken = await _authenticationManager.GetActiveJwt();

            activeToken.Should().Be(jwt.AccessToken);
        }

        [Test]
        public async Task Authentication_manager_gets_active_token_from_login_the_first_time ()
        {
            var jwt = new Jwt
            {
                AccessToken = "abcdef",
                ExpiresIn = 3600
            };

            _authenticationHttpClient.Setup(ahc => ahc.LoginAsync()).ReturnsAsync(jwt);

            await _authenticationManager.GetActiveJwt();

            _authenticationHttpClient.Verify(ahc => ahc.LoginAsync(), Times.Once);
        }

        [Test]
        public async Task Authentication_manager_gets_previously_retrieved_token ()
        {
            var jwt = new Jwt
            {
                AccessToken = "abcdef",
                ExpiresIn = 3600
            };

            _authenticationHttpClient.Setup(ahc => ahc.LoginAsync()).ReturnsAsync(jwt);

            await _authenticationManager.GetActiveJwt();

            string activeToken = await _authenticationManager.GetActiveJwt();

            activeToken.Should().Be(jwt.AccessToken);
            _authenticationHttpClient.Verify(ahc => ahc.LoginAsync(), Times.Once);
        }

        [Test]
        public async Task Authentication_manager_retrieves_active_token_from_login_When_token_expired ()
        {
            var jwt = new Jwt
            {
                AccessToken = "abcdef",
                ExpiresIn = 0
            };

            _authenticationHttpClient.Setup(ahc => ahc.LoginAsync()).ReturnsAsync(jwt);

            await _authenticationManager.GetActiveJwt();

            await _authenticationManager.GetActiveJwt();

            _authenticationHttpClient.Verify(ahc => ahc.LoginAsync(), Times.Exactly(2));
        }
    }
}
