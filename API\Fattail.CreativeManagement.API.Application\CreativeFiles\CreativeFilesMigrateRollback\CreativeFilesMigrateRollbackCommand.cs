﻿using Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrate;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrateRollback;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrateRollback;

public sealed record CreativeFilesMigrateRollbackCommand (RollbackAction RollbackAction) : IRequest<Result>;

public enum RollbackAction
{
    Full,
    Partial
}