﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;

public class CreativeFilesManager : ICreativeFilesManager
{
    private readonly ICreativeFileStorageManager _creativeFileStorageManager;

    public CreativeFilesManager (ICreativeFileStorageManager creativeFileStorageManager)
    {
        _creativeFileStorageManager = creativeFileStorageManager;
    }

    public async Task<double> GetFileSizeInMegabytes (CreativeFile creativeFile)
    {
        double fileSizeInMegabytes = await _creativeFileStorageManager.GetFileSizeInMegabytes(creativeFile);
        return fileSizeInMegabytes;
    }
}