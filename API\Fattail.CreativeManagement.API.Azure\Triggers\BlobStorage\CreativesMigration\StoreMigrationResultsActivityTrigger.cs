using Azure.Storage.Blobs;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Fattail.CreativeManagement.API.Azure.Triggers.BlobStorage.CreativesMigration;

public class StoreMigrationResultsActivityTrigger
{
    private readonly ILogger<StoreMigrationResultsActivityTrigger> _logger;

    public StoreMigrationResultsActivityTrigger (ILogger<StoreMigrationResultsActivityTrigger> logger)
    {
        _logger = logger;
    }

    [Function(nameof(StoreMigrationResultsActivityTrigger))]
    public async Task Run (
        [ActivityTrigger] StoreMigrationResultsModel storeMigrationResultsModel,
        [BlobInput("creative-migration-reports")]
        BlobContainerClient blobContainerClient,
        FunctionContext context)
    {
        var sb = new StringBuilder();

        sb.AppendLine("Number of creatives processed: " + storeMigrationResultsModel.MigrationResults.Count());
        sb.AppendLine("Number of creatives failed: " + storeMigrationResultsModel.MigrationResults.Count(result => !result.IsSuccess));
        
        // Log each result  
        foreach (MigrationResult result in storeMigrationResultsModel.MigrationResults)
        {
            if (!result.IsSuccess)
            {
                sb.AppendLine($"{string.Join(", ", result.Errors)}");
            }
        }

        BlobClient? logBlobClient = blobContainerClient.GetBlobClient(
            $"{storeMigrationResultsModel.OrgId}/output/{DateTime.Now:yyyy.MM.dd}/{storeMigrationResultsModel.Name}_{DateTime.Now:yyyy.MM.dd.HH.mm.ss}.log");

        await logBlobClient.UploadAsync(new MemoryStream(Encoding.UTF8.GetBytes(sb.ToString()), false), true);
    }
}