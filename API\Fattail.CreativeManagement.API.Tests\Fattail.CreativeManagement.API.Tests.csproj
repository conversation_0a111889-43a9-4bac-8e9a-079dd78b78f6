<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AutoBogus" Version="2.13.1" />
    <PackageReference Include="AutoBogus.Conventions" Version="2.13.1" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Bogus" Version="35.6.3" />
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="FluentAssertions" Version="7.0.0" />
    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="FluentResults.Extensions.FluentAssertions" Version="2.1.2" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="NUnit" Version="4.2.2" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.6.0" />
    <PackageReference Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="3.116.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Application\Fattail.CreativeManagement.API.Application.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Azure\Fattail.CreativeManagement.API.Azure.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Domain\Fattail.CreativeManagement.API.Domain.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.CosmosDb\Fattail.CreativeManagement.API.Infrastructure.CosmosDb.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.QueryServices\Fattail.CreativeManagement.API.Infrastructure.QueryServices.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Application\CreativeAdd\" />
  </ItemGroup>
</Project>