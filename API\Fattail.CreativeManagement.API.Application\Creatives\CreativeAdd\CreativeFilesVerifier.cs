﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.CreativeFileSanitizers;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;

internal sealed class CreativeFilesVerifier : ICreativeFilesVerifier
{
    private readonly ICreativeFileRepository _creativeFileRepository;

    public CreativeFilesVerifier (ICreativeFileRepository creativeFileRepository)
    {
        _creativeFileRepository = creativeFileRepository;
    }

    public async Task<IReadOnlyList<CreativeFile>> GetExistingCreativeFilesFrom (
        params CreativeFileId[] creativeFileIds)
    {
        return await _creativeFileRepository.FindManyByIdAsync<CreativeFile>(creativeFileIds);
    }
}