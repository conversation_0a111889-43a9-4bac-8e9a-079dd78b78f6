﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper.CustomTypeConverters;

internal sealed class ValidationRuleTypeConverter : ITypeConverter<
        Dictionary<CreativeFieldValidationRuleType, ValidationRule>, Entities.ValidationRule[]>,
    ITypeConverter<Entities.ValidationRule[], Dictionary<CreativeFieldValidationRuleType, ValidationRule>>
{
    public Entities.ValidationRule[] Convert (Dictionary<CreativeFieldValidationRuleType, ValidationRule> source,
        Entities.ValidationRule[] destination, ResolutionContext context)
    {
        var entityCollection = new List<Entities.ValidationRule>();
        foreach (ValidationRule value in source.Values)
        {
            entityCollection.Add(new Entities.ValidationRule { Type = value.Type, Options = value.Options.ToArray() });
        }

        return entityCollection.ToArray();
    }

    public Dictionary<CreativeFieldValidationRuleType, ValidationRule> Convert (Entities.ValidationRule[] source,
        Dictionary<CreativeFieldValidationRuleType, ValidationRule> destination, ResolutionContext context)
    {
        var domainCollection = new Dictionary<CreativeFieldValidationRuleType, ValidationRule>();

        if (source is null)
        {
            return domainCollection;
        }

        foreach (Entities.ValidationRule validationRule in source)
        {
            domainCollection.Add(validationRule.Type,
                context.Mapper.Map<Entities.ValidationRule, ValidationRule>(validationRule));
        }

        return domainCollection;
    }
}