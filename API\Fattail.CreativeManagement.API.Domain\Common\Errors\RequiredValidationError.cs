﻿namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

public class RequiredValidationError : ErrorBase
{
    internal RequiredValidationError (string fieldValue, string entity, string creativeFieldId) : base(
        $"Custom required validation failed for '{fieldValue}' in '{entity}' entity.",
        ErrorType.RequiredValidationError)
    {
        Metadata.Add(nameof(fieldValue), fieldValue);
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
    }
}