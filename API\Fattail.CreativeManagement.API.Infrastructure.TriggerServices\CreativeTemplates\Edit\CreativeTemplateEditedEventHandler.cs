using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Infrastructure.TriggerServices.CreativeTemplates.Edit;

internal class CreativeTemplateEditedEventHandler : INotificationHandler<CreativeTemplateEditedEvent>
{
    private readonly CreativeEventHandlerService _creativeEventHandlerService;
    private readonly ILogger<CreativeTemplateEditedEventHandler> _logger;

    public CreativeTemplateEditedEventHandler (CreativeEventHandlerService creativeEventHandlerService, ILogger<CreativeTemplateEditedEventHandler> logger)
    {
        _creativeEventHandlerService = creativeEventHandlerService;
        _logger = logger;
    }

    public async Task Handle (CreativeTemplateEditedEvent request, CancellationToken cancellationToken)
    {
        try
        {
            await _creativeEventHandlerService.UpdateCreativeTemplateReference(request.Id, request.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, @"An error occurred while updating creative template references in creative {creativeId}", request.Id);
        }
    }
}