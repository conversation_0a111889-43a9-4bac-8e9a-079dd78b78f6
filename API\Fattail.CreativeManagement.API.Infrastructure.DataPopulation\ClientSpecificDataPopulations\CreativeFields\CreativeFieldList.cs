﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using CreativeField = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields
{
    public class CreativeFieldList
    {
        private readonly List<string> _orgIdList;
        public List<CreativeField> _creativeFields;

        public CreativeFieldList (List<string> orgIdList)
        {
            _orgIdList = orgIdList;
            _creativeFields = new List<CreativeField>() { };

            foreach (string orgid in _orgIdList)
            {
           
                _creativeFields.Add(
                    new CreativeField
                    {
                        OrgId = orgid,
                        Id = "374650397348615",
                        Name = "CREATIVE MOCKS",
                        Type = CreativeFieldTypeEnum.MultiFileUpload
                    });
            }

        }
    }
}
