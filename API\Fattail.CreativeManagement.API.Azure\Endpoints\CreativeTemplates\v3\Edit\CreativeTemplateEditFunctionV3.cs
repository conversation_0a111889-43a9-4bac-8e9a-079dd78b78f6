using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateEdit;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2.Edit;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v3.Edit;

public class CreativeTemplateEditFunctionV3: BaseFunction
{
    public CreativeTemplateEditFunctionV3 (IMediator mediator, IMapper mapper): base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Creative Templates Update v3",
        tags: new[] { "v3" },
        Summary = "Updates a creative template v3")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, In = ParameterLocation.Path, Type = typeof(long))]
    [OpenApiRequestBody("application/json", typeof(CreativeTemplateEditRequest),
        Description = "Creative template to update", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeTemplateResponse))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound,
        Description = "Returned when the creative template does not exist.")]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeTemplateEditFunctionV3))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "v3/creative-templates/{id:long}")]
        HttpRequestData req,
        long id)
    {
        CreativeTemplateEditCommand creativeTemplateEditCommand = await FromRequest<CreativeTemplateEditRequest, CreativeTemplateEditCommand>(req, 
            new Dictionary<string, object>
            {
                { CreativeTemplateEditAutoMapperProfile.CreativeTemplateEditCommandIdParameterName, id }
            });

        Result<CreativeTemplateResult> creativeTemplateResult = await _mediator.Send(creativeTemplateEditCommand);
        
        return await FromResult<CreativeTemplateResult, CreativeTemplateResponse>(req, creativeTemplateResult);
    }
}