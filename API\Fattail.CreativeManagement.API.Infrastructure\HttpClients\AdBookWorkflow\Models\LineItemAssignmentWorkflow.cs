namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow.Models;

public class LineItemAssignmentWorkflow
{
    public LineItemAssignmentWorkflow (string triggerType, long campaignId)
    {
        TriggerType = triggerType;
        TriggerInfo = new TriggerInfo(campaignId);
    }
    public TriggerInfo TriggerInfo { get; }
    public string TriggerType { get; }
}

public class TriggerInfo
{
    public TriggerInfo (long campaignId)
    {
        CampaignId = campaignId;
        CreativeAssignmentRelations = new List<CreativeAssignmentRelationship>();
    }

    public List<CreativeAssignmentRelationship> CreativeAssignmentRelations { get; }
    public long CampaignId { get; }
}

public class CreativeAssignmentRelationship
{
    public CreativeAssignmentRelationship (long creativeId, long lineItemId)
    {
        AdId = creativeId;
        DropId = lineItemId;
    }

    public long AdId { get; }
    public long DropId { get; }
}