﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
public class EditNameTests : CreativeTestsBase
{
    [Test]
    public async Task Editing_name_modifies_name ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;
        
        const string NewCreativeName = "new creative name";

        Result creativeEditNameResult = creative.EditName(NewCreativeName, UpdateInformation.Create("<PERSON><PERSON><PERSON>", DateTime.Now).Value);

        creativeEditNameResult.Should().BeSuccess();
        creative.Name.Should().Be(NewCreativeName);
    }

    [Test]
    public async Task Editing_name_does_not_modify_other_values ()
    {
        (DateTime _, CreativeTemplate creativeTemplate, string _, long adBookClientId, long campaignId, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;

        creative.EditName("new creative name", UpdateInformation.Create("Pepe Grillo", DateTime.Now).Value);
        
        creative.CreativeTemplateId.Should().Be(creativeTemplate.Id);
        creative.AdBookClientId.Should().Be(adBookClientId);
        creative.CampaignId.Should().Be(campaignId);
        creative.Fields.Should().BeEquivalentTo(creativeTemplate.CreativeFields.Where(ctf => ctf.Type != CreativeFieldType.SectionDivider),
                options => options.ExcludingMissingMembers()
                    .ComparingByMembers<CreativeTemplateCreativeField>())
            .And.AllSatisfy(creativeFieldValue => creativeFieldValue.Should()
                .BeAssignableTo<MultiFileUploadFieldValue>().Which.Value.Should().BeNullOrEmpty());
    }

    [Test]
    public async Task Editing_name_modifies_last_update_values ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) =
            await CreateCreative();

        Creative creative = creativeResult.Value;

        const string NewUpdater = "Pepe Grillo";
        DateTime lastUpdatedOn = DateTime.UtcNow.AddHours(15);

        creative.EditName("new creative name", UpdateInformation.Create(NewUpdater, lastUpdatedOn).Value);

        creative.LastUpdatedBy.Should().Be(NewUpdater);
        creative.LastUpdatedOn.Should().Be(lastUpdatedOn);
    }

    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public async Task Creative_cant_edit_name_without_new_value (string? newName)
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) =
            await CreateCreative();

        Creative creative = creativeResult.Value;

        Result creativeEditNameResult = creative.EditName(newName!, UpdateInformation.Create("updater abc", DateTime.Now).Value);

        creativeEditNameResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative name", nameof(Creative)));
    }

    [Test]
    public async Task Edit_name_transition_creative_status_to_pending_approval ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) =
            await CreateCreative();

        Creative creative = creativeResult.Value;

        creative.Approve(ApprovalInformation.Create("Approver Name", DateTime.Now).Value);
        creative.EditName("newName", UpdateInformation.Create("updater abc", DateTime.Now).Value);

        creative.Status.Should().Be(CreativeStatus.PendingApproval);
    }
}