﻿using SkiaSharp;
using System.Drawing;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;

public sealed class ImageFileType : CreativeFileType
{
    private static readonly IReadOnlySet<FileExtension> _supportedExtensions = new HashSet<FileExtension>
    {
        new (".jpg"), 
        new (".jpeg"), 
        new (".png"), 
        new (".gif"), 
        new (".bmp"), 
        new (".tiff"), 
    };

    public ImageFileType() : base(CreativeFileTypeEnum.Image.ToString(), (int)CreativeFileTypeEnum.Image)
    {
    }

    protected override bool MatchesExtension (FileExtension extension)
    {
        return _supportedExtensions.Contains(extension);
    }

    public override CreativeFileMetadata GenerateMetadata (Stream creativeFileStream)
    {
        var skData = SKData.Create(creativeFileStream);
        SKImageInfo fileHeader = SKBitmap.DecodeBounds(skData);

        var imageFileMetadata = new Dictionary<string, string>
        {
            { MetadataKeys.Width, fileHeader.Width.ToString() }, 
            { MetadataKeys.Height, fileHeader.Height.ToString() },
        };

        creativeFileStream.Position = 0;
        return new CreativeFileMetadata(imageFileMetadata);
    }

    public static class MetadataKeys
    {
        public const string Width = "Width";
        public const string Height = "Height";
    }
}