﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;

public static class CreativeTemplateFactory
{
    public static Result<CreativeTemplate> Create (CreativeTemplateCreateRequest createCreativeTemplateRequest)
    {
        var result = Result.Ok();

        if (!createCreativeTemplateRequest.OrderedCreativeFields.Any())
        {
            result.WithError(new RequiredValueMissingError("creative template fields", nameof(CreativeTemplate)));
        }

        List<DisplayOrderCreativeField> orderedCreativeFields = createCreativeTemplateRequest.OrderedCreativeFields;

        bool allFieldsDoNotHaveOrder = createCreativeTemplateRequest.OrderedCreativeFields.All(c => c.DisplayOrder is null);

        bool someFieldsDoNotHaveOrder = orderedCreativeFields.Any(creativeField => creativeField.DisplayOrder is null) &&
                                        orderedCreativeFields.Any(creativeField => creativeField.DisplayOrder is not null);

        bool creativeFieldsWithDuplicatedDisplayOrder = orderedCreativeFields.GroupBy(c => c.DisplayOrder).Any(c => c.Count() > 1);

        if (allFieldsDoNotHaveOrder)
        {
            int order = 0;
            orderedCreativeFields = orderedCreativeFields.Select(c => new DisplayOrderCreativeField(c.Id, order++, c.SpecialProperties)).ToList();
        }
        else if (someFieldsDoNotHaveOrder)
        {
            foreach (DisplayOrderCreativeField creativeField in orderedCreativeFields.Where(creativeField => creativeField.DisplayOrder is null).ToList())
            {
                result.WithError(new CreativeTemplateMissingDisplayOrderPropertyError(creativeField.Id.ToString(),
                    nameof(CreativeField)));
            }
        }
        else if (creativeFieldsWithDuplicatedDisplayOrder)
        {
            result.WithError(new CreativeTemplateDuplicateDisplayOrderError(nameof(CreativeField)));
        }

        if (result.IsFailed)
        {
            return result;
        }

        var creativeFieldSet = new HashSet<CreativeTemplateCreativeField>();
        foreach (DisplayOrderCreativeField creativeField in orderedCreativeFields)
        {
            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(
                creativeField.Id,
                createCreativeTemplateRequest.ExistingCreativeFields,
                (int)creativeField.DisplayOrder!,
                creativeField.SpecialProperties);

            if (creativeTemplateFieldResult.IsFailed)
            {
                result = Result.Merge(result, creativeTemplateFieldResult.ToResult());
                continue;
            }

            if (!creativeFieldSet.Add(creativeTemplateFieldResult.Value))
            {
                result = Result.Merge(result,
                    Result.Fail(new CreativeTemplateDuplicateFieldError(creativeField.Id, nameof(CreativeField))));
            }
        }

        if (result.IsFailed)
        {
            return result;

        }

        var creativeTemplate = new CreativeTemplate(createCreativeTemplateRequest.CreativeTemplateId, createCreativeTemplateRequest.Name, createCreativeTemplateRequest.CreativeType, creativeFieldSet, createCreativeTemplateRequest.IsPredefined);

        return creativeTemplate;
    }
}
