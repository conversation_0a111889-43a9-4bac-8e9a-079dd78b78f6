﻿using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using FluentAssertions;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeFiles;

[TestFixture]
public class CreativeFileNameTests
{
    [Test]
    public void Creative_file_name_is_created_from_file_name ()
    {
        const string FileName = "abcde.jpg";
        var fileExtension = FileExtension.From(FileName);

        var creativeFileName = CreativeFileName.From(FileName);

        creativeFileName.Name.Should().Be(FileName);
        creativeFileName.Extension.Should().Be(fileExtension);
        creativeFileName.NameWithoutExtension.Should().Be("abcde");
    }

    [Test]
    public void Creative_file_name_fails_When_null_file_name ()
    {
        Invoking(() => CreativeFileName.From(null!)).Should().ThrowExactly<ArgumentNullException>();
    }

    [Test]
    public void Creative_file_name_as_string_returns_file_name ()
    {
        const string FileName = "abcde.jpg";

        var creativeFileName = CreativeFileName.From(FileName);

        creativeFileName.ToString().Should().Be(FileName);
    }
}