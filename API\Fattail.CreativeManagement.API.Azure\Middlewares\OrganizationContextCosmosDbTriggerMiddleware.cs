﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Azure.Middlewares.Dtos;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;

namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal sealed class OrganizationContextCosmosDbTriggerMiddleware : IFunctionsWorkerMiddleware
{
    private readonly ILogger _logger;
    private readonly List<OrganizationSettings> _organizationsSettings;

    public OrganizationContextCosmosDbTriggerMiddleware (IOptions<List<OrganizationSettings>> organizationsSettings,
        ILogger<OrganizationContextCosmosDbTriggerMiddleware> iLogger)
    {
        _organizationsSettings = organizationsSettings.Value;
        _logger = iLogger;
    }

    public async Task Invoke (FunctionContext context, FunctionExecutionDelegate next)
    {
        IEnumerable<EntityDto> documents = GetDocumentsFromContext(context);

        if (!documents.Any())
        {
            _logger.LogWarning("No documents to process were found");
            return;
        }

        string orgId = documents.First().OrgId;

        if (_organizationsSettings.All(organizationSettings => organizationSettings.OrgId.ToString() != orgId))
        {
            return;
        }

        IOrganizationContext organizationContext = context.InstanceServices.GetRequiredService<IOrganizationContext>();

        organizationContext.SetOrganizationId(long.Parse(orgId));

        await next(context);

        IEnumerable<EntityDto> GetDocumentsFromContext (FunctionContext context)
        {
            KeyValuePair<Type, object> keyValuePair =
                context.Features.SingleOrDefault(f => f.Key.Name == "IFunctionBindingsFeature");
            object functionBindingsFeature = keyValuePair.Value;
            Type type = functionBindingsFeature.GetType();
            var inputData =
                type.GetProperties().Single(p => p.Name == "InputData").GetValue(functionBindingsFeature) as
                    IReadOnlyDictionary<string, object>;
            var jsonDocuments = JArray.Parse(inputData?.Values.SingleOrDefault().ToString());
            List<EntityDto> documents = jsonDocuments.ToObject<List<EntityDto>>();
            return documents;
        }
    }
}