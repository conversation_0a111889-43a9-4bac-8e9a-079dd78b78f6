﻿namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.Models;

internal sealed class ActiveJwt
{
    private readonly DateTime _expiresAt;

    internal ActiveJwt (string accessToken, int expiresInSeconds)
    {
        AccessToken = string.IsNullOrEmpty(accessToken)
            ? throw new ArgumentException("Access token must be set")
            : accessToken;
        _expiresAt = DateTime.UtcNow.AddSeconds(expiresInSeconds - 60);
    }

    internal string AccessToken { get; }

    internal bool IsExpired ()
    {
        return DateTime.UtcNow.CompareTo(_expiresAt) > 0;
    }
}