﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker.Http;
using System.Collections.Specialized;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Common;

public abstract class BaseFunction
{
    protected readonly IMapper _mapper;
    protected readonly IMediator _mediator;

    internal BaseFunction (
        IMediator mediator,
        IMapper mapper)
    {
        _mediator = mediator;
        _mapper = mapper;
    }

    protected async Task<TCommand> FromRequest<TRequest, TCommand> (HttpRequestData requestData)
    {
        return await FromRequest<TRequest, TCommand>(requestData, new Dictionary<string, object>());
    }

    protected async Task<TCommand> FromRequest<TRequest, TCommand> (HttpRequestData requestData, Dictionary<string, object> contextItems)
    {
        TRequest request = await requestData.ReadFromJsonAsync<TRequest>();
        return _mapper.Map<TCommand>(request,
            opts =>
            {
                foreach (KeyValuePair<string, object> contextItem in contextItems)
                {
                    opts.Items.Add(contextItem.Key, contextItem.Value);
                }
            });
    }

    protected async Task<TODataQuery> ODataQueryFromRequest<TODataQuery> (HttpRequestData requestData)
    where TODataQuery : ODataQuery
    {
        NameValueCollection queryString = System.Web.HttpUtility.ParseQueryString(requestData.Url.Query);
        
        return _mapper.Map<TODataQuery>(queryString);
    }

    protected async Task<HttpResponseData> FromResult<TResultValue, TResponse> (HttpRequestData requestData,
        Result<TResultValue> result, HttpStatusCode successCode = HttpStatusCode.OK)
    {
        if (result.HasError<EntityNotFoundError>())
        {
            return requestData.CreateResponse(HttpStatusCode.NotFound);
        }

        HttpResponseData response;

        if (result.IsFailed)
        {
            response = requestData.CreateResponse(HttpStatusCode.BadRequest);
            await response.WriteAsJsonAsync(_mapper.Map<IEnumerable<ErrorInformation>>(result.Errors));
            return response;
        }

        response = requestData.CreateResponse(successCode);
        if (successCode != HttpStatusCode.NoContent)
        {
            await response.WriteAsJsonAsync(_mapper.Map<TResponse>(result.Value));
        }

        return response;
    }

    protected async Task<HttpResponseData> FromResultWithLocation<TResultValue, TResponse> (HttpRequestData requestData,
        Result<TResultValue> result, long? entityId)
    {
        HttpResponseData response =
            await FromResult<TResultValue, TResponse>(requestData, result, HttpStatusCode.Created);

        if (result.IsFailed)
        {
            return response;
        }

        response.Headers.Add("Location", $"{requestData.Url.AbsolutePath}/{entityId}");

        return response;
    }
    
    protected async Task<HttpResponseData> FromQueryResult<TResult, TResponse> (HttpRequestData requestData,
        QueryResult<TResult> queryResult)
    {
        HttpResponseData response = requestData.CreateResponse(HttpStatusCode.OK);
        
        await response.WriteAsJsonAsync(_mapper.Map<List<TResponse>>(queryResult.Value));
        
        response.Headers.Add("Total-Count", $"{queryResult.TotalCount}");

        return response;
    }
    
    protected async Task<HttpResponseData> FromSingleQueryResult<TResult, TResponse> (HttpRequestData requestData,
        TResult? resultValue)
    {
        HttpResponseData response = requestData.CreateResponse(HttpStatusCode.OK);

        if (resultValue is null)
        {
            response.StatusCode = HttpStatusCode.NotFound;
            return response;
        }
        
        await response.WriteAsJsonAsync(_mapper.Map<TResponse>(resultValue));

        return response;
    }

    protected async Task<HttpResponseData> FromDeleteResult (HttpRequestData requestData, Result deleteResult)
    {
        return deleteResult.HasError<EntityNotFoundError>() ? 
            requestData.CreateResponse(HttpStatusCode.NotFound) : 
            requestData.CreateResponse(deleteResult.HasError<EntityInUseError>() ? HttpStatusCode.Conflict : HttpStatusCode.NoContent);
    }
}