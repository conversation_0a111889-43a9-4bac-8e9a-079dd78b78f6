﻿using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using FluentResults;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common
{
    public class DataPopulationHandler : IRequestHandler<DataPopulationCommand,
        Result<ICollection<DataPopulationResponseModel>>>
    {
        private readonly IDataPopulationService _dataPopulationService;
        private readonly ILogger _logger;

        public DataPopulationHandler (IDataPopulationService dataPopulationService, ILogger<DataPopulationHandler> logger)
        {
            _dataPopulationService = dataPopulationService;
            _logger = logger;
        }

        public async Task<Result<ICollection<DataPopulationResponseModel>>> Handle (DataPopulationCommand request, CancellationToken cancellationToken)
        {
            var results = new Result<ICollection<DataPopulationResponseModel>>();

            try
            {
                results = (await _dataPopulationService.RunAsync()).ToResult();
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "An error occurred while executing");
                throw;
            }

            return results;
        }
    }
}
