﻿using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;

public interface ICosmosDbContainerFactory
{
    /// <summary>
    ///     Returns a CosmosDbContainer wrapper
    /// </summary>
    /// <param name="containerName"></param>
    /// <returns></returns>
    ICosmosDbContainer GetContainer (string containerName);

    /// <summary>
    ///     Ensure the database is created
    /// </summary>
    /// <returns></returns>
    Task EnsureDbSetupAsync ();

    /// <summary>
    ///     Creates the Containers if do not exist
    /// </summary>
    /// <returns>List of created containers</returns>
    Task<List<ContainerResponse>> CreateContainersIfNotExistsAsync ();

    /// <summary>
    /// Creates DataBase if does not exist
    /// </summary>
    /// <returns>Database response</returns>
    Task<DatabaseResponse> CreateDatabaseIfNotExistsAsync ();
}