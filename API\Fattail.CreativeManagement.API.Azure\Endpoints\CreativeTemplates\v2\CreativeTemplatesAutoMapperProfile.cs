﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;

internal sealed class CreativeTemplatesAutoMapperProfile : Profile
{
    public CreativeTemplatesAutoMapperProfile ()
    {
        CreateMap<CreativeTemplateQueryResult, CreativeTemplateResponse>();
        CreateMap<CreativeTemplateResult, CreativeTemplateResponse>();
        CreateMap<CreativeTemplateCreativeFieldQueryResult, CreativeTemplateCreativeFieldResponse>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldQueryResult), typeof(CreativeTemplateMultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldQueryResult), typeof(CreativeTemplateSingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldQueryResult), typeof(CreativeTemplateSectionDividerCreativeFieldResponse));

        CreateMap<ValidationRuleQueryResult, ValidationRuleResponse>();
        CreateMap<CreativeTemplateMultiSelectCreativeFieldQueryResult, CreativeTemplateMultiSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSingleSelectCreativeFieldQueryResult, CreativeTemplateSingleSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSectionDividerCreativeFieldQueryResult, CreativeTemplateSectionDividerCreativeFieldResponse>();
    }
}