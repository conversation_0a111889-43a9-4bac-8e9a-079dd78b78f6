﻿using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Domain.Common;

[ExcludeFromCodeCoverage]
public abstract class Entity<TId>
{
    public Entity (TId id)
    {
        Id = id;
    }

    public TId Id { get; }

    public override bool Equals (object? obj)
    {
        if (!(obj is Entity<TId> other))
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        if (IsTransient() || other.IsTransient())
        {
            return false;
        }

        return Id.Equals(other.Id);
    }

    private bool IsTransient ()
    {
        return Id is null || Id.Equals(default(TId));
    }

    public static bool operator == (Entity<TId>? a, Entity<TId>? b)
    {
        if (a is null && b is null)
        {
            return true;
        }

        if (a is null || b is null)
        {
            return false;
        }

        return a.Equals(b);
    }

    public static bool operator != (Entity<TId>? a, Entity<TId>? b)
    {
        return !(a == b);
    }

    public override int GetHashCode ()
    {
        return (ToString() + Id).GetHashCode();
    }
}