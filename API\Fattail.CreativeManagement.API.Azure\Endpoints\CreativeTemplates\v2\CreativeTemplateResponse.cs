using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using CreativeFieldValidationRuleType = Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common.CreativeFieldValidationRuleType;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;

internal sealed record ValidationRuleResponse
{
    [property: OpenApiProperty(Description = "Validation type")]
    public CreativeFieldValidationRuleType Type { get; init; }

    [property: OpenApiProperty(Description = "Validation configurations")]
    public IReadOnlyList<string> Options { get; init; }
};

internal record CreativeTemplateCreativeFieldResponse
(
    [property: OpenApiProperty(Description=  "Creative field id")]
    long Id,
    [property: OpenApiProperty(Description=  "Creative field name")]
    string Name,
    [property: OpenApiProperty(Description = "Creative field order")]
    int DisplayOrder,
    [property: OpenApiProperty(Description=  "Creative field type")]
    Domain.CreativeFields.CreativeFieldType Type,
    [property: OpenApiProperty(Description = "Creative field validation rules")]
    IReadOnlyList<ValidationRuleResponse> ValidationRules,
    [property: OpenApiProperty(Description = "Creative field tooltip")]
    string? Tooltip
);

internal record CreativeTemplateMultiSelectCreativeFieldResponse
(
    long Id,
    string Name,
    int DisplayOrder,
    Domain.CreativeFields.CreativeFieldType Type,
    [property: OpenApiProperty(Description = "Creative field configurations")]
    IReadOnlyList<SelectOptionResponse> Options,
    IReadOnlyList<ValidationRuleResponse> ValidationRules,
    string? Tooltip
) : CreativeTemplateCreativeFieldResponse(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip);

internal record CreativeTemplateSingleSelectCreativeFieldResponse
(
    long Id,
    string Name,
    int DisplayOrder,
    Domain.CreativeFields.CreativeFieldType Type,
    [property: OpenApiProperty(Description = "Creative field configurations")]
    IReadOnlyList<SelectOptionResponse> Options,
    IReadOnlyList<ValidationRuleResponse> ValidationRules,
    string? Tooltip
) : CreativeTemplateCreativeFieldResponse(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip);

internal record CreativeTemplateSectionDividerCreativeFieldResponse
(
    long Id,
    string Name,
    int DisplayOrder,
    Domain.CreativeFields.CreativeFieldType Type,
    IReadOnlyList<ValidationRuleResponse> ValidationRules,
    string? Tooltip,
    string? Content
) : CreativeTemplateCreativeFieldResponse(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip);

internal sealed record CreativeTemplateResponse ()
{
    [property: OpenApiProperty(Description = "Creative template id")]
    public long Id { get; init; }

    [property: OpenApiProperty(Description = "Creative template name")]
    public string Name { get; init; }

    [property: OpenApiProperty(Description = "Creative template type")]
    public CreativeType CreativeType { get; init; }

    [property: OpenApiProperty(Description = "Indicates if creative template is archived")]
    public bool Archive { get; init; }

    [property: OpenApiProperty(Description = "Indicates whether the given creative template is predefined or not")]
    public bool Predefined { get; init; }

    [property: OpenApiProperty(Description = "ID of the template this was cloned from, if applicable")]
    public long? ClonedFrom { get; init; }

    [property: OpenApiProperty(Description = "Creative fields")]
    public IReadOnlyList<CreativeTemplateCreativeFieldResponse> CreativeFields { get; init; }
}