﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using FluentResults;
using System.Text.RegularExpressions;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields.ValidationRuleValidatorStrategy;

public class FileUploadExtensionsValidationRuleValidatorStrategy : IValidationRuleValidatorStrategy
{
    public Result IsValid (CreativeFieldId creativeFieldId, CreativeFieldValidationRuleType type, IReadOnlyList<string> options)
    {
        var result = Result.Ok();

        if (!options.Any())
        {
            return result;
        }

        var invalidExtensions = options
            .Where(option => !Regex.IsMatch(option, @"\.[^.]+$"))
            .ToList();

        if (invalidExtensions.Any())
        {
            result = Result.Merge(result, Result.Fail(new CreativeTemplateInvalidFileExtensionError(
                creativeFieldId.ToString(),
                nameof(CreativeField),
                CreativeFieldValidationRuleType.FileUploadExtensions.ToString())));
        }

        return result;
    }
}