﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <UserSecretsId>c05a72db-ae04-4593-99a4-f1c8fc2de26d</UserSecretsId>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Endpoints\Creatives\**" />
    <Compile Remove="Endpoints\CreativeTemplates\**" />
    <Compile Include="Endpoints\Creatives\Common\CreativeCommonAutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\Common\CreativeFieldRequest.cs" />
    <Compile Include="Endpoints\Creatives\Common\CreativeResponse.cs" />
    <Compile Include="Endpoints\Creatives\Common\OpenApiModel\CreativeFieldDocumentFilter.cs" />
    <Compile Include="Endpoints\Creatives\Common\OpenApiModel\CreativeFieldRequestOpenApiModel.cs" />
    <Compile Include="Endpoints\Creatives\Common\OpenApiModel\CreativeFieldResponseOpenApiModel.cs" />
    <Compile Include="Endpoints\Creatives\Common\OpenApiModel\PolymorphicCreativeFieldRequestOpenApiModel.cs" />
    <Compile Include="Endpoints\Creatives\Common\OpenApiModel\PolymorphicCreativeFieldResponseOpenApiModel.cs" />
    <Compile Include="Endpoints\Creatives\v3\AddCreative\CreativeAddFunctionV3.cs" />
    <Compile Include="Endpoints\Creatives\v3\AddCreative\CreativeAddRequestV3.cs" />
    <Compile Include="Endpoints\Creatives\v3\AddCreative\CreativeAddV3AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v3\Common\CreativeResponseV3.cs" />
    <Compile Include="Endpoints\Creatives\v3\EditCreative\CreativeEditFunctionV3.cs" />
    <Compile Include="Endpoints\Creatives\v3\EditCreative\CreativeEditRequestV3.cs" />
    <Compile Include="Endpoints\Creatives\v3\EditCreative\CreativeEditV3AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v3\Get\CreativeGetByIdFunctionV3.cs" />
    <Compile Include="Endpoints\Creatives\v3\Get\CreativeGetFunctionV3.cs" />
    <Compile Include="Endpoints\Creatives\v2\AddCreative\CreativeAddV2AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v2\AddCreative\CreativeAddFunctionV2.cs" />
    <Compile Include="Endpoints\Creatives\v2\AddCreative\CreativeAddRequestV2.cs" />
    <Compile Include="Endpoints\Creatives\v2\Common\CreativeResponseV2.cs" />
    <Compile Include="Endpoints\Creatives\v2\EditCreative\CreativeEditV2AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v2\EditCreative\CreativeEditFunctionV2.cs" />
    <Compile Include="Endpoints\Creatives\v2\EditCreative\CreativeEditRequestV2.cs" />
    <Compile Include="Endpoints\Creatives\v2\Get\CreativeGetByIdFunctionV2.cs" />
    <Compile Include="Endpoints\Creatives\v2\Get\CreativeGetFunctionV2.cs" />
    <Compile Include="Endpoints\Creatives\v4\AddCreative\CreativeAddFunctionV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\AddCreative\CreativeAddRequestV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\AddCreative\CreativeAddV4AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v4\ApproveCreative\CreativeApproveFunctionV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\ApproveCreative\CreativeApproveRequestV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\ApproveCreative\CreativeApproveV4AutoMapperProfile.cs" />    
    <Compile Include="Endpoints\Creatives\v4\SetLineItems\CreativeSetLineItemsRequest.cs" />
    <Compile Include="Endpoints\Creatives\v4\SetLineItems\CreativeSetLineItemsFunctionV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\SetLineItems\CreativeSetLineItemsV4AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v4\Common\CreativeResponseV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\EditCreative\CreativeEditFunctionV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\EditCreative\CreativeEditRequestV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\EditCreative\CreativeEditV4AutoMapperProfile.cs" />
    <Compile Include="Endpoints\Creatives\v4\Get\CreativeGetByIdFunctionV4.cs" />
    <Compile Include="Endpoints\Creatives\v4\Get\CreativeGetFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateAddCustomMapper.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateAddRequest.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateCommonAutoMapperProfile.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateCreativeFieldRequest.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateEditAutoMapperProfile.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateEditRequest.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\CreativeTemplateResponse.cs" />
    <Compile Include="Endpoints\CreativeTemplates\Common\ValidationRuleRequest.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Add\CreativeTemplateAddFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Clone\PredefinedCreativeTemplateCloneAutoMapperProfile.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Clone\PredefinedCreativeTemplateCloneFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Clone\PredefinedCreativeTemplateCloneRequest.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Delete\CreativeTemplateDeleteFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Edit\CreativeTemplateEditFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Get\CreativeTemplateGetByIdFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Get\CreativeTemplateGetFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Get\PredefinedCreativeTemplateGetByIdFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v4\Get\PredefinedCreativeTemplateGetFunctionV4.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v3\Add\CreativeTemplateAddFunctionV3.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v3\Delete\CreativeTemplateDeleteFunctionV3.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v3\Edit\CreativeTemplateEditFunctionV3.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v3\Get\CreativeTemplateGetByIdFunctionV3.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v3\Get\CreativeTemplateGetFunctionV3.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Add\CreativeTemplateAddAutoMapperProfile.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Add\CreativeTemplateAddFunctionV2.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Add\CreativeTemplateAddRequestV2.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\CreativeTemplatesAutoMapperProfile.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\CreativeTemplateResponse.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Delete\CreativeTemplateDeleteFunctionV2.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Edit\CreativeTemplateEditAutoMapperProfile.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Edit\CreativeTemplateEditRequest.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Edit\CreativeTemplateEditFunctionV2.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Get\CreativeTemplateGetByIdFunctionV2.cs" />
    <Compile Include="Endpoints\CreativeTemplates\v2\Get\CreativeTemplateGetFunctionV2.cs" />
    <EmbeddedResource Remove="Endpoints\Creatives\**" />
    <EmbeddedResource Remove="Endpoints\CreativeTemplates\**" />
    <None Remove="Endpoints\Creatives\**" />
    <None Remove="Endpoints\CreativeTemplates\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Azure.Identity" Version="1.14.0" />
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="HttpMultipartParser" Version="8.4.0" />
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    <PackageReference Include="Microsoft.Azure.Core.NewtonsoftJson" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.CosmosDB" Version="4.12.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.DurableTask" Version="1.4.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore.Analyzers" Version="1.0.3" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.OpenApi" Version="2.0.0-preview2" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Storage.Blobs" Version="6.6.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk.Analyzers" Version="1.2.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" Version="9.0.6" />
    <PackageReference Include="SecurityCodeScan.VS2019" Version="5.6.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="SkiaSharp" Version="3.116.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Application\Fattail.CreativeManagement.API.Application.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.CosmosDb\Fattail.CreativeManagement.API.Infrastructure.CosmosDb.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.DataPopulation\Fattail.CreativeManagement.API.Infrastructure.DataPopulation.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.QueryServices\Fattail.CreativeManagement.API.Infrastructure.QueryServices.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.TriggerServices\Fattail.CreativeManagement.API.Infrastructure.TriggerServices.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure\Fattail.CreativeManagement.API.Infrastructure.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="FluentValidation.DependencyInjectionExtensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Endpoints\Creatives\CreativesAutoMapperProfile.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Update="extensions.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <PropertyGroup>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
    <DocumentationFile>
    </DocumentationFile>
    <ExcludeXmlAssemblyFiles>false</ExcludeXmlAssemblyFiles>
    <WarningsAsErrors>
    </WarningsAsErrors>
    <OutputType>Exe</OutputType>
  </PropertyGroup>
  <ItemGroup>
    <Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
</Project>