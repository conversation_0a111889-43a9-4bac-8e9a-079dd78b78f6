﻿namespace Fattail.CreativeManagement.API.Domain.Common.Errors
{
    public sealed class FileUploadExtensionsValidationError : ErrorBase
    {
        internal FileUploadExtensionsValidationError (string fieldValue, string entity, string creativeFieldId, string allowedExtensions) :
        base($"Custom file upload extensions validation failed for '{fieldValue}' in '{entity}' entity.", ErrorType.FileUploadExtensionsValidationError)
        {
            Metadata.Add(nameof(fieldValue), fieldValue);
            Metadata.Add(nameof(entity), entity);
            Metadata.Add(nameof(creativeFieldId), creativeFieldId);
            Metadata.Add(nameof(allowedExtensions), allowedExtensions);
        }
    }
}
