﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using System.Linq.Expressions;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;

public class CreativeTemplateNameInUseSpecification (
    CreativeTemplate? currentCreativeTemplate,
    string? creativeTemplateName)
    : Specification<CreativeTemplate>
{
    public override Expression<Func<CreativeTemplate, bool>> ToExpression ()
    {
        bool currentCreativeTemplateIsNull = currentCreativeTemplate == null;
        string currentCreativeTemplateId = currentCreativeTemplateIsNull ? string.Empty : currentCreativeTemplate!.Id.ToString();

        return creativeTemplate => (currentCreativeTemplateIsNull || creativeTemplate.Id.ToString() != currentCreativeTemplateId) && creativeTemplate.Name == creativeTemplateName;
    }
}