﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.v4.AddCreativeField;

public class CreativeFieldAddFunctionV4 : BaseFunction
{
    public CreativeFieldAddFunctionV4 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Add a creative field v4", 
        tags: new[] { "v4" },
        Summary = "Add a creative field v4")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeFieldAddRequest),
        Description = "The creative field to add", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeFieldResponse))]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeFieldAddFunctionV4))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v4/creative-fields")]
        HttpRequestData req)
    {
        CreativeFieldAddCommand creativeAddCommand =
            await FromRequest<CreativeFieldAddRequest, CreativeFieldAddCommand>(req);

        Result<CreativeFieldResult> result = await _mediator.Send(creativeAddCommand);

        HttpResponseData response =
            await FromResultWithLocation<CreativeFieldResult, CreativeFieldResponse>(req, result,
                result.ValueOrDefault?.Id);

        return response;
    }
}