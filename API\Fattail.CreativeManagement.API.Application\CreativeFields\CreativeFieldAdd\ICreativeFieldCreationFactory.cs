﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;

namespace Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;

public interface ICreativeFieldCreationFactory
{
    Task<Result<CreativeField>> CreateField (IIdManager idManager,
        ICreativeFieldRepository creativeFieldRepository,
        CreativeFieldAddCommand requestModel);
}