﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using Microsoft.Azure.Cosmos;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Utilities
{
    [ExcludeFromCodeCoverage]
    public static class DataPopulationUtilities
    {
        public enum EntityTypes
        {
            Database = 0,
            Collection = 1,
            Item = 2
        }
        /// <summary>
        /// Prints to console details about the response code and throws an exception if an unexpected status code is returned
        /// </summary>
        /// <param name="statusCode"></param>
        /// <param name="entityType"></param>
        /// <param name="entityId"></param>
        /// <param name="dataItemCollectionName">Should only be used when entityType is EntityTypes.Data. This is the table/collection name for additional logging details</param>
        public static DataPopulationResponseModel HandleEntityResponse (HttpStatusCode statusCode, EntityTypes entityType, string entityId, string dataItemCollectionName = "")
        {
            return statusCode switch
            {
                HttpStatusCode.Created => new DataPopulationResponseModel
                {
                    Message = $"Created {entityType}: {dataItemCollectionName} {entityId}"
                },
                HttpStatusCode.OK => new DataPopulationResponseModel
                {
                    Message =
                      $"Already exists. Entity was either updated or no action was taken. {entityType}: {dataItemCollectionName} {entityId}"
                },
                _ => throw new Exception(
                  $" Unexpected status code: {statusCode}. {entityType}: {dataItemCollectionName} {entityId}"),
            };
        }

        public async static Task<DataPopulationResponseModel> ExecuteUpsertAsync<T> (IDataPopulationRepository<T> itemResponseRepository, T entity, string partitionKeyValue) where T : Entity
        {
            ItemResponse<T> response = await itemResponseRepository.UpsertWithResponseAsync(entity, partitionKeyValue);

            return HandleEntityResponse(response.StatusCode, EntityTypes.Item, entity.Id.ToString(), typeof(T).Name);
        }
    }
}
