﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;

public record CreativeFieldResponse
(
    [property: OpenApiProperty(Description=  "Creative field id")]
    long Id,
    [property: OpenApiProperty(Description=  "Creative field name")]
    string Name,
    [property: OpenApiProperty(Description=  "Creative field type")]
    CreativeFieldType Type
);

public record SelectOptionResponse (
    [property: OpenApiProperty(Description = "Option id")]
    long Id,
    [property: OpenApiProperty(Description = "Option description")]
    string Description
);

public record MultiSelectCreativeFieldResponse
(
    long Id,
    string Name,
    CreativeFieldType Type,
    [property: OpenApiProperty(Description = "Creative field configurations")]
    IReadOnlyList<SelectOptionResponse> Options
) : CreativeFieldResponse(Id, Name, Type);

public record SingleSelectCreativeFieldResponse
(
    long Id,
    string Name,
    CreativeFieldType Type,
    [property: OpenApiProperty(Description = "Creative field configurations")]
    IReadOnlyList<SelectOptionResponse> Options
) : CreativeFieldResponse(Id, Name, Type);

//NOTE: This class has been just created for retro compatibility with the previous version of the API. Creatives shouldn't include SectionDivider fields.
public record SectionDividerCreativeFieldResponse
(
    long Id,
    string Name,
    CreativeFieldType Type,
    string? Content
) : CreativeFieldResponse(Id, Name, Type);