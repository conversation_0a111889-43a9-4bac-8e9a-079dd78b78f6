using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class SectionDividerFieldType : CreativeFieldType
{
    internal SectionDividerFieldType () : base(nameof(CreativeFieldTypeEnum.SectionDivider), (int)CreativeFieldTypeEnum.SectionDivider)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return DefaultCreativeFieldSettings.Instance;
    }
}
