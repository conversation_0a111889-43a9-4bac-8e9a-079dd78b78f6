﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrate;

public class CreativeMigrateHandler (
    ICreativeTemplateRepository creativeTemplateRepository,
    ICreativeRepository creativeRepository,
    ICreativeFileRepository creativeFileRepository,
    ICreativeFactory creativeFactory,
    IDateTimeProvider dateTimeProvider,
    ISanitizerStrategyProvider sanitizerStrategyProvider,
    ICreativeFilesManager creativeFilesManager,
    IIdManager idManager)
    : IRequestHandler<CreativeMigrateCommand, Result<CreativeResult>>
{
    public async Task<Result<CreativeResult>> Handle (CreativeMigrateCommand request,
        CancellationToken cancellationToken)
    {
        CreativeTemplate? creativeTemplate = request.CreativeTemplateId > 0
            ? await creativeTemplateRepository.FindByIdAsync(new CreativeTemplateId(request.CreativeTemplateId))
            : null;

        if (creativeTemplate is null)
        {
            return Result.Fail(new EntityNotFoundError(request.CreativeTemplateId.ToString(),
                nameof(CreativeTemplate)));
        }

        Result<Creative> migratedAdBookCreativeResult =
            await creativeRepository.FindMigratedAdBookCreativeAsync(request.AdBookClientId, request.CampaignId,
                request.AdBookAdId);

        if (migratedAdBookCreativeResult.IsSuccess)
        {
            Creative? existingCreative = migratedAdBookCreativeResult.Value;
            var currentCreativeFile =
                (Domain.Creatives.Fields.MultiFileUploadFieldValue)await existingCreative.GetCreativeFieldValue(
                    CreativeFieldIdentifier.Create(new CreativeFieldId(request.CreativeFieldFileId),
                        CreativeFieldType.MultiFileUpload));

            var assignValuesToFieldsResult = new Result();
            
            UpdateInformation createUpdateInformation =
                UpdateInformation.Create(request.UpdatedBy, dateTimeProvider.CurrentTime).Value;

            foreach (CreativeFieldDto creativeFieldRequest in request.Fields)
            {
                var creativeFieldId = new CreativeFieldId(creativeFieldRequest.Id);
                var creativeFieldIdentifier =
                    CreativeFieldIdentifier.Create(creativeFieldId,
                        CreativeFieldType.FromValue((int)creativeFieldRequest.Type));

                var validationRules = creativeTemplate?.CreativeFields
                    .FirstOrDefault(creativeField => creativeField.Id == creativeFieldId)?.ValidationRules.ToList();

                Result assignValueToFieldResult = await existingCreative.AssignValueToField(creativeFieldIdentifier,
                    creativeFieldRequest.GetValue(),
                    createUpdateInformation,
                    validationRules ?? new List<ValidationRule>(), sanitizerStrategyProvider, creativeFilesManager);

                assignValuesToFieldsResult = Result.Merge(assignValuesToFieldsResult, assignValueToFieldResult);
            }

            assignValuesToFieldsResult = Result.Merge(assignValuesToFieldsResult,
                existingCreative.EditName(request.Name, createUpdateInformation));

            if (assignValuesToFieldsResult.IsFailed)
            {
                return assignValuesToFieldsResult;
            }

            CreativeFile? oldCreativeFile =
                await creativeFileRepository.FindByIdAsync(currentCreativeFile.Value.First());
            if (oldCreativeFile is not null)
            {
                oldCreativeFile.PrepareToDelete();
                await creativeFileRepository.UpdateAsync<CreativeFile>(oldCreativeFile);
            }

            return await creativeRepository.MigrateAsync<CreativeResult>(existingCreative);
        }

        if (migratedAdBookCreativeResult.IsFailed &&
            migratedAdBookCreativeResult.Errors.Any(e => e is MigratedCreativeFileIsUpdatedError))
        {
            CreativeFile? unassignedCreativeFile = await creativeFileRepository.FindByIdAsync(
                new CreativeFileId(request.Fields.First(field => field.Id == request.CreativeFieldFileId).Id));

            if (unassignedCreativeFile is not null)
            {
                unassignedCreativeFile.PrepareToDelete();
                await creativeFileRepository.UpdateAsync<CreativeFile>(unassignedCreativeFile);
            }
            
            return migratedAdBookCreativeResult.ToResult();
        }

        var fields = request.Fields.ToDictionary(x => new CreativeFieldId(x.Id), v => v.GetValue());

        Result<Creative> creativeResult = await creativeFactory.Create(new CreateCreativeRequest(idManager.GetId(), request.Name,
            creativeTemplate, request.AdBookClientId, request.AdBookAdId, request.CampaignId, request.LineItemIds,
            request.UpdatedBy, fields, dateTimeProvider.CurrentTime));

        if (creativeResult.IsFailed)
        {
            return creativeResult.ToResult();
        }

        Creative creative = creativeResult.Value;

        return await creativeRepository.MigrateAsync<CreativeResult>(creative);
    }
}