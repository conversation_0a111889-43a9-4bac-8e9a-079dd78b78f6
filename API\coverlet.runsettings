﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
  Documentation:
  https://github.com/tonerdo/coverlet/blob/master/Documentation/VSTestIntegration.md
-->
<RunSettings>
  <RunConfiguration>
   <ResultsDirectory>.\TestResults</ResultsDirectory>
  </RunConfiguration>
  <DataCollectionRunSettings>
    <DataCollectors>
      <DataCollector friendlyName="XPlat code coverage">
        <Configuration>
          <Format>cobertura</Format>
		  <Exclude>[*.Tests?]*,[*.Azure]*,[*]*Errors.*,[*.Infrastructure*]*,[*]*DependencyInjection,[*]*Exception</Exclude>
          <ExcludeByAttribute>Obsolete,GeneratedCodeAttribute</ExcludeByAttribute>
		  <SkipAutoProps>true</SkipAutoProps>
		  <CodeCoverage>
			<UseVerifiableInstrumentation>True</UseVerifiableInstrumentation>
            <AllowLowIntegrityProcesses>True</AllowLowIntegrityProcesses>
            <CollectFromChildProcesses>True</CollectFromChildProcesses>
            <CollectAspDotNet>False</CollectAspDotNet>
		  </CodeCoverage>
		</Configuration>
      </DataCollector>
    </DataCollectors>
  </DataCollectionRunSettings>
</RunSettings>
