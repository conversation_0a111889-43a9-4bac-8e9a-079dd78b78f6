﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;
using DomainCreativeFields = Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeAdd;

[TestFixture]
public class CreativeAddHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));

        _dateTimeProviderMock = new Mock<IDateTimeProvider>();
        _creativeTemplateRepositoryMock = new Mock<ICreativeTemplateRepository>();
        _creativeRepositoryMock = new Mock<ICreativeRepository>();
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _creativeFilesManager = new Mock<ICreativeFilesManager>();

        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _creativeFactory = new CreativeFactory(_creativeFilesManager.Object, _sanitizerStrategyProviderMock.Object);

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider =>
                sanitizerStrategyProvider.GetFrom(DomainCreativeFields.CreativeFieldType.MultiFileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeAddHandler = new CreativeAddHandler(
            _idManagerMock.Object,
            _dateTimeProviderMock.Object,
            _creativeTemplateRepositoryMock.Object,
            _creativeRepositoryMock.Object,
            _creativeFactory);
    }

    private CreativeAddHandler _creativeAddHandler = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<IDateTimeProvider> _dateTimeProviderMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepositoryMock = null!;
    private Mock<ICreativeRepository> _creativeRepositoryMock = null!;
    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    private Mock<ISanitizer> _sanitizerMock = null!;
    private Mock<ICreativeFilesManager> _creativeFilesManager = null!;
    private ICreativeFactory _creativeFactory = null!;
    private readonly string _fieldName = "fieldName";

    [Test]
    public async Task Invalid_creative_is_not_persisted ()
    {
        CreativeAddCommand addCommand = new AutoFaker<CreativeAddCommand>()
            .RuleFor(addCommand => addCommand.CreativeTemplateId, 0)
            .Generate();

        Result<CreativeResult> result = await _creativeAddHandler.Handle(addCommand, CancellationToken.None);

        result.Should().BeFailure().And.HaveReason(new InvalidValueError("creative template", nameof(Creative)));
        _creativeRepositoryMock.Verify(
            creativeRepository => creativeRepository.CreateAsync<CreativeResult>(It.IsAny<Creative>()),
            Times.Never());
    }

    [Test]
    public async Task Valid_creative_is_persisted ()
    {
        var uniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(123),
            CreativeTemplateName.Create("creative template 1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(12345), 1), new(new(6789), 2)],
            new HashSet<CreativeField>([
                CreativeField.Create(new CreativeFieldId(12345), _fieldName, CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new CreativeFieldId(6789), _fieldName, CreativeFieldType.MultiFileUpload, uniqueNameRequirement, false, null, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        _creativeTemplateRepositoryMock.Setup(creativeTemplateRepository =>
                creativeTemplateRepository.FindByIdAsync(creativeTemplate.Id))
            .ReturnsAsync(creativeTemplate);

        CreativeAddCommand addCommand = new AutoFaker<CreativeAddCommand>()
            .RuleFor(creativeAddCommand => creativeAddCommand.AdBookClientId, faker => faker.Random.Long(1))
            .RuleFor(creativeAddCommand => creativeAddCommand.CampaignId, faker => faker.Random.Long(1))
            .RuleFor(creativeAddCommand => creativeAddCommand.CreativeTemplateId, creativeTemplate.Id)
            .RuleFor(creativeAddCommand => creativeAddCommand.Fields, faker => creativeTemplate.CreativeFields
                .Select<CreativeTemplateCreativeField, CreativeFieldDto>(creativeField =>
                    creativeField.Type.EnumType switch
                    {
                        CreativeFieldTypeEnum.MultiFileUpload => new MultiFileUploadFieldValue(
                            creativeField.Id, faker.Make(3, () => faker.Random.Long(1)).AsReadOnly()),
                        CreativeFieldTypeEnum.SingleLineText => new SingleLineTextFieldValue(
                            creativeField.Id, faker.Random.String()),
                        _ => throw new ArgumentOutOfRangeException()
                    }).ToList())
            .Generate();

        Result<CreativeResult> result = await _creativeAddHandler.Handle(addCommand, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeRepositoryMock.Verify(
            creativeRepository => creativeRepository.CreateAsync<CreativeResult>(It.IsAny<Creative>()),
            Times.Once());
    }
}