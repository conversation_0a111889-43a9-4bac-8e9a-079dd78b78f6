﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common.OpenApiModel;

public class CreativeFieldDocumentFilter : IDocumentFilter
{
    public void Apply (IHttpRequestDataObject req, OpenApiDocument document)
    {
        document.Components.Schemas["creativeFieldRequest"] = new PolymorphicCreativeFieldRequestOpenApiModel();
        document.Components.Schemas["creativeFieldValueResponse"] = new PolymorphicCreativeFieldResponseOpenApiModel();
    }
}

