﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Extensions.OpenApi.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;

namespace Fattail.CreativeManagement.API.Azure;

internal static class FunctionsWorkerApplicationBuilderExtensions
{
    internal static void UseNewtonsoftJsonWihtCustomSerializer (this IFunctionsWorkerApplicationBuilder builder)
    {
        var serializerSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            NullValueHandling = NullValueHandling.Ignore,
             
        };

        serializerSettings.Converters.Add(new StringEnumConverter());

        builder.UseNewtonsoftJson(serializerSettings);
    }
}