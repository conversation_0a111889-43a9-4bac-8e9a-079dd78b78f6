﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateFieldInvalidSpecialPropertiesError : ErrorBase
{
    public CreativeTemplateFieldInvalidSpecialPropertiesError (CreativeFieldId creativeFieldId, IReadOnlySet<SpecialPropertyKey> invalidSpecialPropertiesKeys, string entity)
        : base($"The creative field with id {creativeFieldId} does not support the following special properties: {string.Join(", ", invalidSpecialPropertiesKeys)}.",
            ErrorType.CreativeTemplateFieldUnsupportedValidationRuleType)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(invalidSpecialPropertiesKeys), string.Join(", ", invalidSpecialPropertiesKeys));
    }
}