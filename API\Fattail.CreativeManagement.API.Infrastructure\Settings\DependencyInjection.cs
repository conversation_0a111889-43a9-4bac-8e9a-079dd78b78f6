﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Infrastructure.Settings;

internal static class DependencyInjection
{
    internal static void AddSettings (this IServiceCollection services)
    {
        services.AddOptions<List<OrganizationSettings>>()
            .Configure<IConfiguration>((settings, configuration)
                => configuration.GetSection(OrganizationSettings.OrganizationSettingsKey)
                    .Bind(settings));
    }
}