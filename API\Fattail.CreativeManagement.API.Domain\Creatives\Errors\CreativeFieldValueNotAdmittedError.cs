using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Errors;

internal sealed class CreativeFieldValueNotAdmittedError : ErrorBase
{
    public CreativeFieldValueNotAdmittedError ( CreativeFieldType creativeFieldType, string entity) :
        base($"Creative field with type {creativeFieldType} can't have a value",
            ErrorType.CreativeIsMigratedError)
    {
        Metadata.Add(nameof(creativeFieldType), creativeFieldType);
        Metadata.Add(nameof(entity), entity);
    }
}