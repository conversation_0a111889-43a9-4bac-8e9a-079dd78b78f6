﻿using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Models;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Utils;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;

namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.Authentication;

internal sealed class AuthenticationHttpClient : IAuthenticationHttpClient
{
    private readonly AuthenticationHttpClientConfiguration _authenticationHttpClientConfiguration;
    private readonly HttpClient _httpClient;

    public AuthenticationHttpClient (HttpClient httpClient, IOptions<AuthenticationHttpClientConfiguration> options)
    {
        _authenticationHttpClientConfiguration = options.Value;
        _httpClient = httpClient;
        _httpClient.BaseAddress = new Uri("https://login.microsoftonline.com");
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    }

    public async Task<Jwt> LoginAsync ()
    {
        var parameters = new Dictionary<string, string>
        {
            { "grant_type", _authenticationHttpClientConfiguration.GrantType },
            {
                "scope",
                $"https://fattailauth.onmicrosoft.com/{_authenticationHttpClientConfiguration.ApiApplicationId}/.default"
            },
            { "client_id", _authenticationHttpClientConfiguration.ClientId },
            { "client_secret", _authenticationHttpClientConfiguration.ClientSecret }
        };

        using var request = new HttpRequestMessage(HttpMethod.Post,
            $"{_authenticationHttpClientConfiguration.TenantId}/oauth2/v2.0/token");

        request.Content = new FormUrlEncodedContent(parameters);

        HttpResponseMessage response = await _httpClient.SendAsync(request);

        response.EnsureSuccessStatusCode();

        Jwt? jwt = await response.Content.ReadFromJsonAsync<Jwt>(new JsonSerializerOptions
        {
            PropertyNamingPolicy = new SnakeCaseNamingPolicy()
        });

        return jwt!;
    }
}