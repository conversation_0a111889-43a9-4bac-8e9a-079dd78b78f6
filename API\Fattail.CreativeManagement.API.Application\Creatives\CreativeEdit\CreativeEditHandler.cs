﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;

internal sealed class CreativeEditHandler (
    ICreativeRepository creativeRepository,
    ICreativeTemplateRepository creativeTemplateRepository,
    IDateTimeProvider dateTimeProvider,
    ISanitizerStrategyProvider sanitizerStrategyProvider,
    ICreativeFilesManager creativeFilesManager)
    : IRequestHandler<CreativeEditCommand, Result<CreativeResult>>
{
    public async Task<Result<CreativeResult>> Handle (CreativeEditCommand request, CancellationToken cancellationToken)
    {
        Creative? creative = await creativeRepository.FindByIdAsync(request.Id);

        if (creative is null)
        {
            return Result.Fail(new EntityNotFoundError(request.Id.ToString(), nameof(Creative)));
        }

        CreativeTemplate? creativeTemplate = creative.CreativeTemplateId > 0
            ? await creativeTemplateRepository.FindByIdAsync(new CreativeTemplateId(creative.CreativeTemplateId))
            : null;

        Result<UpdateInformation> createUpdateInformationResult = UpdateInformation.Create(request.UpdatedBy, dateTimeProvider.CurrentTime);

        if (createUpdateInformationResult.IsFailed)
        {
            return createUpdateInformationResult.ToResult();
        }

        UpdateInformation updateInformation = createUpdateInformationResult.Value;
        Result editNameResult = creative.EditName(request.Name, updateInformation);

        if (editNameResult.IsFailed)
        {
            return editNameResult;
        }
        
        //TODO: Once POST creative v3 is deleted, we can remove LineItemIds check
        if (request.LineItemIds is not null)
        {
            var deletedLineItems = creative.LineItemIds.Except(request.LineItemIds).ToList();
            if (deletedLineItems.Any())
            {
                //TODO: Call AB workflow for deleted line items
            }

            //Added items
            var addedLineItems = request.LineItemIds.Except(creative.LineItemIds).ToList();
            if (addedLineItems.Any())
            {
                //TODO: Call AB workflow for added line items
            }
            creative.UpdateLineItemIds(request.LineItemIds);
        }

        var assignValuesToFieldsResult = new Result();

        foreach (CreativeFieldDto creativeFieldRequest in request.Fields)
        {
            var creativeFieldId = new CreativeFieldId(creativeFieldRequest.Id);
            var creativeFieldIdentifier =
                Domain.Creatives.Fields.CreativeFieldIdentifier.Create(creativeFieldId,
            CreativeFieldType.FromValue((int)creativeFieldRequest.Type));

            var validationRules = creativeTemplate?.CreativeFields.FirstOrDefault(creativeField => creativeField.Id == creativeFieldId)?.ValidationRules.ToList();

            Result assignValueToFieldResult = await creative.AssignValueToField(creativeFieldIdentifier, creativeFieldRequest.GetValue(), updateInformation,
                validationRules ?? new List<ValidationRule>(), sanitizerStrategyProvider, creativeFilesManager);

            assignValuesToFieldsResult = Result.Merge(assignValuesToFieldsResult, assignValueToFieldResult);
        }

        return assignValuesToFieldsResult.IsFailed
            ? assignValuesToFieldsResult
            : await creativeRepository.UpdateAsync<CreativeResult>(creative);
    }
}
