﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares.Errors;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal sealed class ContentTypeValidatorMiddleware : IFunctionsWorkerMiddleware
{
    private readonly IMapper _mapper;

    public ContentTypeValidatorMiddleware (IMapper mapper)
    {
        _mapper = mapper;
    }

    public async Task Invoke (FunctionContext context, FunctionExecutionDelegate next)
    {
        HttpRequestData? request = await context.GetHttpRequestDataAsync();

        if (request != null && !IsAllowedMethod())
        {
            await next(context);
            return;
        }

        if (!context.TryGetAttribute(out OpenApiRequestBodyAttribute openApiRequestBodyAttribute))
        {
            throw new MissingMemberException($"Function must be marked with {nameof(OpenApiRequestBodyAttribute)}.");
        }

        const string ContentTypeHeaderName = "Content-Type";

        Result<string> getContentHeaderResult = request.GetHeaderValue(ContentTypeHeaderName);

        if (getContentHeaderResult.IsFailed)
        {
            await request.AssignErrorResponse(_mapper.Map<ErrorInformation>(getContentHeaderResult), HttpStatusCode.BadRequest);
            return;
        }

        string contentType = getContentHeaderResult.Value;

        if (!contentType.Contains(openApiRequestBodyAttribute.ContentType, StringComparison.CurrentCultureIgnoreCase))
        {
            var result = Result.Fail(new HttpHeaderError(ContentTypeHeaderName));
            await request.AssignErrorResponse(_mapper.Map<ErrorInformation>(result), HttpStatusCode.BadRequest);
            return;
        }

        await next(context);

        bool IsAllowedMethod ()
        {
            string[] allowedMethods = { "post", "put", "patch" };
            return allowedMethods.Any(allowedMethod =>
                allowedMethod.Equals(request.Method, StringComparison.InvariantCultureIgnoreCase));
        }
    }
}