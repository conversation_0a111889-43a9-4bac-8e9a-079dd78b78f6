﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Infrastructure.TriggerServices.CreativeFiles.Unassign;
internal sealed class CreativeFileUnassignedEventHandler : INotificationHandler<CreativeFileUnassignedEvent>
{
    private readonly ILogger<CreativeFileUnassignedEventHandler> _logger;
    private readonly ICreativeFileStorageManager _creativeFileStorageManager;
    private readonly ICreativeFileRepository _creativeFileRepository;

    public CreativeFileUnassignedEventHandler (
        ILogger<CreativeFileUnassignedEventHandler> logger,
        ICreativeFileStorageManager creativeFileStorageManager,
        ICreativeFileRepository creativeFileRepository)
    {
        _logger = logger;
        _creativeFileStorageManager = creativeFileStorageManager;
        _creativeFileRepository = creativeFileRepository;
    }

    public async Task Handle (CreativeFileUnassignedEvent creativeFileUnassignedEvent, CancellationToken cancellationToken)
    {
        var tasks = new List<Task>();
        tasks.Add(_creativeFileRepository.DeleteAsync(new CreativeFileId(creativeFileUnassignedEvent.CreativeFileId)));
        tasks.Add(_creativeFileStorageManager.DeleteCreativeFile(creativeFileUnassignedEvent.BlobName));
        
        await Task.WhenAll(tasks);
    }
}