using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip.Errors;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip;

public class
    CreativeFilesGenerateZipHandler : IRequestHandler<CreativeFilesGenerateZipCommand,
        Result<CreativeFilesGenerateZipResult>>
{
    private readonly ICreativeFilesZipGenerator _creativeFilesZipGenerator;
    private readonly ICreativeFileRepository _creativeFileRepository;
    private readonly IMapper _mapper;

    public CreativeFilesGenerateZipHandler (ICreativeFilesZipGenerator creativeFilesZipGenerator,
        ICreativeFileRepository creativeFileRepository,
        IMapper mapper)
    {
        _creativeFilesZipGenerator = creativeFilesZipGenerator;
        _creativeFileRepository = creativeFileRepository;
        _mapper = mapper;
    }

    public async Task<Result<CreativeFilesGenerateZipResult>> Handle (CreativeFilesGenerateZipCommand request,
        CancellationToken cancellationToken)
    {
        IReadOnlyList<CreativeFile> creativeFileItems =
            await _creativeFileRepository.FindManyByIdAsync<CreativeFile>(request.CreativeFilesToZip);

        if (!creativeFileItems.Any())
        {
            return Result.Fail(new CreativeFilesToDownloadAreInvalid());
        }

        Result<CreativeFilesGenerateZipResult> generateTemporalCreativeFileZipResult =
            await _creativeFilesZipGenerator.GenerateTemporalCreativeFileZip(creativeFileItems);

        if (generateTemporalCreativeFileZipResult.IsFailed)
        {
            return generateTemporalCreativeFileZipResult.ToResult();
        }

        return generateTemporalCreativeFileZipResult.Value;
    }
}