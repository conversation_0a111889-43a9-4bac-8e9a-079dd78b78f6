﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DatabaseAndContainerSetupService;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DataPopulationModels;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DevAndTestEnvDataPopulations;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using Microsoft.Extensions.Options;
using System.Diagnostics.CodeAnalysis;
using System.Net.Sockets;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Services
{
    [ExcludeFromCodeCoverage]
    public class DataPopulationService : IDataPopulationService
    {
        private readonly IDatabaseAndContainerSetupService _databaseAndContainerSetupService;
        private readonly IClientSpecificDataPopulationService _exampleDataPopulationService;
        private readonly IDevAndTestEnvDataPopulationService _devAndTestEndDataPopulationService;
        private readonly IOptions<CosmosDbSettings> _cosmosDbSettingsOptions;

        public DataPopulationService (IDatabaseAndContainerSetupService databaseAndContainerSetupService,
            IClientSpecificDataPopulationService exampleDataPopulationService,
            IDevAndTestEnvDataPopulationService devAndTestEndDataPopulationService,
            IOptions<CosmosDbSettings> cosmosDbSettingsOptions)
        {
            _databaseAndContainerSetupService = databaseAndContainerSetupService;
            _exampleDataPopulationService = exampleDataPopulationService;
            _devAndTestEndDataPopulationService = devAndTestEndDataPopulationService;
            _cosmosDbSettingsOptions = cosmosDbSettingsOptions;
        }

        public async Task<ICollection<DataPopulationResponseModel>> RunAsync (ICollection<DataPopulationModel> dataPopulationModels)
        {
            try
            {
                var responseResults = new List<DataPopulationResponseModel>();

                responseResults.AddRange(await _databaseAndContainerSetupService.CreateIfNotExists());
                responseResults.AddRange(await _exampleDataPopulationService.RunAllClientSpecificDataPopulations(dataPopulationModels));

                if (IsDevOrTestEnv())
                {
                    responseResults.AddRange(await _devAndTestEndDataPopulationService.RunDevAndTestDataPopulations());
                }

                return responseResults;
            }
            catch (HttpRequestException exception)
            {
                Console.WriteLine("Unable to connect to database.");
                Console.WriteLine(exception);
                throw;
            }
            catch (SocketException exception)
            {
                Console.WriteLine("Unable to connect to database. Try restarting visual studio if you are running locally.");//TODO Matt check if this works now that its service based, otherwise remove
                Console.WriteLine(exception);
                throw;
            }

        }

        private bool IsDevOrTestEnv ()
        {
            return _cosmosDbSettingsOptions.Value.IsDevOrTestEnvironment;
        }
    }
}
