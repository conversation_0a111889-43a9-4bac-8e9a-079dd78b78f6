using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.CreativeTemplates.Edit;
using MediatR;
using Microsoft.Azure.Functions.Worker;

namespace Fattail.CreativeManagement.API.Azure.Triggers.CosmosDb.CreativeTemplates;

public class CreativeTemplateUpdateTrigger
{
    private readonly IMediator _mediator;

    public CreativeTemplateUpdateTrigger (IMediator mediator)
    {
        _mediator = mediator;
    }

    [Function(nameof(CreativeTemplateUpdateTrigger))]
    public async Task Run ([CosmosDBTrigger(
            "CreativeManagement",
            "CreativeTemplates",
            Connection = "CMS:ConnectionString",
            CreateLeaseContainerIfNotExists = true)]
        IReadOnlyList<CreativeTemplateDocument> creativeTemplateDocuments, FunctionContext context)
    {
        foreach (CreativeTemplateDocument creativeTemplateDocument in creativeTemplateDocuments)
        {
            switch (creativeTemplateDocument.LastAction)
            {
                case ActionType.Update:
                    await _mediator.Publish(new CreativeTemplateEditedEvent(
                        creativeTemplateDocument.Id,
                        creativeTemplateDocument.Name));
                    break;
                case ActionType.Create:
                case ActionType.Migrate:
                default:
                    break;
            }
        }
    }
}