﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateDelete;

public class CreativeTemplateDeleteHandler : IRequestHandler<CreativeTemplateDeleteCommand, Result>
{
    private readonly ICreativeTemplateRepository _creativeTemplateRepository;
    private readonly ICreativeTemplateInUseVerifier _creativeTemplateInUseVerifier;

    public CreativeTemplateDeleteHandler (
        ICreativeTemplateRepository creativeTemplateRepository,
        ICreativeTemplateInUseVerifier creativeTemplateInUseVerifier)
    {
        _creativeTemplateRepository = creativeTemplateRepository;
        _creativeTemplateInUseVerifier = creativeTemplateInUseVerifier;
    }

    public async Task<Result> Handle (CreativeTemplateDeleteCommand request,
        CancellationToken cancellationToken)
    {
        CreativeTemplate? creativeTemplate = await _creativeTemplateRepository.FindByIdAsync(new CreativeTemplateId(request.Id));

        if (creativeTemplate is null)
        {
            return Result.Fail(new EntityNotFoundError(request.Id.ToString(), nameof(CreativeTemplate)));
        }

        Result canBeRemovedResult = await creativeTemplate.CanBeRemoved(_creativeTemplateInUseVerifier);

        if (canBeRemovedResult.IsFailed)
        {
            return canBeRemovedResult;
        }

        return await _creativeTemplateRepository.DeleteAsync(creativeTemplate.Id);
    }
}