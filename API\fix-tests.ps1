# PowerShell script to fix CreativeField.<PERSON><PERSON> calls in test files

$testFiles = Get-ChildItem -Path "." -Recurse -Filter "*.cs" -Include "*Tests.cs" | Where-Object { $_.FullName -like "*\Tests\*" }

foreach ($file in $testFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content

    # Pattern 1: CreativeField.Create with 5 parameters ending with false or true (missing settings)
    $content = $content -replace 'CreativeField\.Create\(([^)]+), (false|true)\)\.Value', 'CreativeField.Create($1, $2, DefaultCreativeFieldSettings.Instance).Value'
    $content = $content -replace 'CreativeField\.Create\(([^)]+), (false|true)\)', 'CreativeField.Create($1, $2, DefaultCreativeFieldSettings.Instance)'

    # Pattern 2: Handle multiline CreativeField.Create calls
    $content = $content -replace '(?s)CreativeField\.Create\(([^)]+),\s*(false|true)\s*\)(?!\.)', 'CreativeField.Create($1, $2, DefaultCreativeFieldSettings.Instance)'

    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Updated $($file.Name)"
    }
}

Write-Host "Finished processing test files"
