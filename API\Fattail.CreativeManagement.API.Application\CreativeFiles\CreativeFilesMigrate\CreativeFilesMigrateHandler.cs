﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;
using System.Net;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrate;

internal sealed class
    CreativeFilesMigrateHandler : IRequestHandler<CreativeFilesMigrateCommand,
    Result<CreativeFilesMigrateResult>>
{
    private readonly ICreativeFileRepository _creativeFileRepository;
    private readonly ICreativeFileStorageManager _creativeFileStorageManager;
    private static ICreativeFilesUploadPolicy _creativeFilesUploadPolicy = new CreativeFilesMigrationPolicy();

    private readonly IIdManager _idManager;

    public CreativeFilesMigrateHandler (
        IIdManager idManager,
        ICreativeFileStorageManager creativeFileStorageManager,
        ICreativeFileRepository creativeFileRepository)
    {
        _idManager = idManager;
        _creativeFileStorageManager = creativeFileStorageManager;
        _creativeFileRepository = creativeFileRepository;
    }

    public async Task<Result<CreativeFilesMigrateResult>> Handle (
        CreativeFilesMigrateCommand request,
        CancellationToken cancellationToken)
    {
        var creativeFileName = CreativeFileName.From(WebUtility.UrlDecode(request.CreativeFileToMigrate.FileName));
        Result creativeFilePrepareToMigrateResult = CreativeFile.CanBeMigrated(
            _creativeFilesUploadPolicy, creativeFileName);

        if (creativeFilePrepareToMigrateResult.IsFailed)
        {
            return creativeFilePrepareToMigrateResult;
        }

        Result<Tuple<CreativeFileStorageMetadata, Stream>> creativeFileStoredMetaDataResult =
            await _creativeFileStorageManager.StoreAbOmsCreativeFile(creativeFileName,
                request.CreativeFileToMigrate.FileLocation);
        
        if (creativeFileStoredMetaDataResult.IsFailed)
        {
            return creativeFileStoredMetaDataResult.ToResult();
        }

        Result<CreativeFile> creativeFileResult = CreativeFile.PrepareToUpload(_creativeFilesUploadPolicy, _idManager,
            creativeFileName, creativeFileStoredMetaDataResult.Value.Item2);

        if (creativeFileResult.IsFailed)
        {
            return creativeFileResult.ToResult();
        }

        CreativeFile creativeFile = creativeFileResult.Value;

        creativeFile.PromoteToUploaded(creativeFileStoredMetaDataResult.Value.Item1);

        return await _creativeFileRepository.MigrateAsync<CreativeFilesMigrateResult>(creativeFile);
    }
}