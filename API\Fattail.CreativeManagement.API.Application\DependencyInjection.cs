﻿using Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateDelete;
using Fattail.CreativeManagement.API.Domain;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.CreativeFileSanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Application;

public static class DependencyInjection
{
    public static void AddApplication (this IServiceCollection services)
    {
        services.AddAutoMapper(typeof(DependencyInjection));
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly));

        services.AddSingleton<ICreativeFilesUploadPolicy>(_ => new CreativeFilesUploadPolicy()); 
        
        services.AddScoped<ICreativeFilesVerifier, CreativeFilesVerifier>();
        services.AddScoped<ICreativeTemplateInUseVerifier, CreativeTemplateInUseVerifier>();
        services.AddScoped<ICreativeFieldCreationFactory, CreativeFieldCreationFactory>();
        services.AddScoped<ICreativeFilesManager, CreativeFilesManager>();
        services.AddScoped<ICreativeFactory, CreativeFactory>();
        
        services.AddDomain();
    }
}