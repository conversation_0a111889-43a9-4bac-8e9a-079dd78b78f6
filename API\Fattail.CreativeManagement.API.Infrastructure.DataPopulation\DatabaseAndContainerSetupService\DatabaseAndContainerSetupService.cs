﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Utilities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using Microsoft.Azure.Cosmos;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DatabaseAndContainerSetupService
{
    [ExcludeFromCodeCoverage]
    public class DatabaseAndContainerSetupService : IDatabaseAndContainerSetupService
    {
        private readonly ICosmosDbContainerFactory _cosmosDbRepository;

        public DatabaseAndContainerSetupService (ICosmosDbContainerFactory cosmosDbRepository)
        {
            _cosmosDbRepository = cosmosDbRepository;
        }

        public async Task<ICollection<DataPopulationResponseModel>> CreateIfNotExists ()
        {
            var results = new List<DataPopulationResponseModel>();

            DatabaseResponse databaseResponse = await _cosmosDbRepository.CreateDatabaseIfNotExistsAsync();
            results.Add(DataPopulationUtilities.HandleEntityResponse(databaseResponse.StatusCode,
                DataPopulationUtilities.EntityTypes.Database, databaseResponse.Resource.Id));

            List<ContainerResponse> containerResponses = await _cosmosDbRepository.CreateContainersIfNotExistsAsync();
            results.AddRange(HandleContainerEntityResponses(containerResponses));

            return results;
        }

        private List<DataPopulationResponseModel> HandleContainerEntityResponses (List<ContainerResponse> responses)
        {
            var containerResponses = new List<DataPopulationResponseModel>();

            foreach (ContainerResponse response in responses)
            {
                containerResponses.Add(DataPopulationUtilities.HandleEntityResponse(response.StatusCode,
                    DataPopulationUtilities.EntityTypes.Collection, response.Resource.Id));
            }

            return containerResponses;
        }

    }
}
