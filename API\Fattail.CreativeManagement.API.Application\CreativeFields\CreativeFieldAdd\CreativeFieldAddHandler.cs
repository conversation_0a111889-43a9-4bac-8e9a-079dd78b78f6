﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;

public class CreativeFieldAddHandler (
    ICreativeFieldRepository creativeFieldRepository,
    IIdManager idManager,
    ICreativeFieldCreationFactory creativeFieldCreationFactory)
    : IRequestHandler<CreativeFieldAddCommand, Result<CreativeFieldResult>>
{
    public async Task<Result<CreativeFieldResult>> Handle (CreativeFieldAddCommand request,
        CancellationToken cancellationToken)
    {
        Result<CreativeField> creativeFieldResult = await creativeFieldCreationFactory.CreateField(idManager, creativeFieldRepository, request);

        if (creativeFieldResult.IsFailed)
        {
            return creativeFieldResult.ToResult();
        }

        return await creativeFieldRepository.CreateAsync<CreativeFieldResult>(creativeFieldResult.Value);
    }
}