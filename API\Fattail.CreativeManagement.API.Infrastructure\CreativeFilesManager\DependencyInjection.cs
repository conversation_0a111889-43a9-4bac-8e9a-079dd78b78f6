﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Infrastructure.CreativeFilesManager;

internal static class DependencyInjection
{
    public static void AddAzureBlobStorage (this IServiceCollection services)
    {
        services.AddOptions<BlobStorageSettings>()
            .Configure<IConfiguration>((settings, configuration)
                => configuration.GetSection(BlobStorageSettings.BlobStorageSettingsKey).Bind(settings));
        
        services.AddOptions<AdBookCreativeSettings>()
            .Configure<IConfiguration>((settings, configuration) =>
            {
                settings.AdBookCreativeBaseUri = configuration.GetValue<string>(nameof(settings.AdBookCreativeBaseUri));
                settings.AdBookEWScrippsCreativeBaseUri = configuration.GetValue<string>(nameof(settings.AdBookEWScrippsCreativeBaseUri));
            });

        services.AddSingleton<AzureBlobContainerClientManager>();
        services.AddScoped<ICreativeFilesZipGenerator, CreativeFileManager>();
        services.AddScoped<ICreativeFileStorageManager, CreativeFileManager>();
        
        services.AddHttpClient<CreativeFileManager>();
    }
}