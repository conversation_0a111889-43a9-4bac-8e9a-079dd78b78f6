﻿using AutoMapper;
using CreativeFieldEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using SelectOptionEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectOption;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common.Configuration.AutoMapper;

internal sealed class CreativeFieldResultProfile : Profile
{
    public CreativeFieldResultProfile ()
    {
        CreateMap<CreativeFieldEntity, CreativeFieldQueryResult>()
            .ConstructUsing((src, context) =>
            {
                if (src.Options != null && src.Options.Length > 0)
                {
                    var options = src.Options.Select(o => context.Mapper.Map<SelectOptionQueryResult>(o)).ToList().AsReadOnly();

                    return src.Type switch
                    {
                        CreativeFieldTypeEnum.MultiSelectOption => new MultiSelectCreativeFieldQueryResult(long.Parse(src.Id), src.Name, src.Type, options),
                        CreativeFieldTypeEnum.SingleSelectOption => new SingleSelectCreativeFieldQueryResult(long.Parse(src.Id), src.Name, src.Type, options),
                        _ => new CreativeFieldQueryResult(long.Parse(src.Id), src.Name, src.Type)
                    };
                }

                return new CreativeFieldQueryResult(long.Parse(src.Id), src.Name, src.Type);
            });

        CreateMap<SelectOptionEntity, SelectOptionQueryResult>()
            .ConstructUsing(src => new SelectOptionQueryResult(long.Parse(src.Id), src.Description));
    }
}