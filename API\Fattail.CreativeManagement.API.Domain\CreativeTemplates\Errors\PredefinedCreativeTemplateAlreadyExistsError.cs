using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public sealed class PredefinedCreativeTemplateAlreadyExistsError : ErrorBase
{
    public PredefinedCreativeTemplateAlreadyExistsError (CreativeTemplateId predefinedCreativeTemplateId) :
        base("Organization has already cloned a predefined template of this creative type", ErrorType.PredefinedTemplateAlreadyCloned)
    {
        Metadata.Add("entity", nameof(CreativeTemplate));
        Metadata.Add("PredefinedCreativeTemplateId", predefinedCreativeTemplateId);
    }
}
