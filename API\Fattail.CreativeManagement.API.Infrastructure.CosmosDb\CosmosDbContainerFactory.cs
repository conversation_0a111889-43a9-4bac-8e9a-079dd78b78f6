﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb;

internal class CosmosDbContainerFactory : ICosmosDbContainerFactory
{
    private readonly ContainerInfo[] _containers;
    private readonly CosmosClient _cosmosClient;
    private readonly string _databaseName;

    public CosmosDbContainerFactory (CosmosClient cosmosClient,
        string databaseName,
        ContainerInfo[] containers)
    {
        _databaseName = databaseName ?? throw new ArgumentNullException(nameof(databaseName));
        _containers = containers ?? throw new ArgumentNullException(nameof(containers));
        _cosmosClient = cosmosClient ?? throw new ArgumentNullException(nameof(cosmosClient));
    }

    public ICosmosDbContainer GetContainer (string containerName)
    {
        return _containers.Any(x => x.Name == containerName)
            ? (ICosmosDbContainer)new CosmosDbContainer(_cosmosClient, _databaseName, containerName)
            : throw new ArgumentException($"Unable to find container: {containerName}");
    }

    public async Task EnsureDbSetupAsync ()
    {
        DatabaseResponse database = await _cosmosClient.CreateDatabaseIfNotExistsAsync(_databaseName);

        foreach (ContainerInfo container in _containers)
        {
            await database.Database.CreateContainerIfNotExistsAsync(container.Name, $"{container.PartitionKey}");
        }
    }

    public Task<DatabaseResponse> CreateDatabaseIfNotExistsAsync ()
    {
        return _cosmosClient.CreateDatabaseIfNotExistsAsync(_databaseName);
    }

    public async Task<List<ContainerResponse>> CreateContainersIfNotExistsAsync ()
    {
        Database database = _cosmosClient.GetDatabase(_databaseName);

        var responses = new List<ContainerResponse>();

        foreach (ContainerInfo container in _containers)
        {
            responses.Add(await database.DefineContainer(name: container.Name, partitionKeyPath: container.PartitionKey).CreateIfNotExistsAsync());
        }

        return responses;
    }
}