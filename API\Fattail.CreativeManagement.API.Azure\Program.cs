﻿using Community.OData.Linq;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Azure.Extensions;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation;
using Fattail.CreativeManagement.API.Infrastructure;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices;
using Fattail.CreativeManagement.API.Infrastructure.TriggerServices;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Extensions.OpenApi.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Reflection;

namespace Fattail.CreativeManagement.API.Azure;

public class Program
{
    private static async Task Main (string[] args)
    {
        IHost host = new HostBuilder()
            .ConfigureAppConfiguration(builder => 
                builder
                    .AddUserSecrets(Assembly.GetExecutingAssembly(), true))
            .ConfigureFunctionsWebApplication(builder =>
            {
                builder.UseNewtonsoftJsonWihtCustomSerializer();
                builder.UseWhen<UnhandledExceptionHttpTriggerMiddleware>(GetAllowedHttpTriggers());
                builder.UseWhen<ContentTypeValidatorMiddleware>(GetAllowedHttpTriggers());
                builder.UseWhen<OrganizationContextHttpTriggerMiddleware>(GetAllowedOrganizationContextHttpTriggers());
                builder.UseWhen<OrganizationContextCosmosDbTriggerMiddleware>(CosmosDbTriggers());
                builder.UseWhen<OrganizationContextBlobTriggerMiddleware>(BlobTriggers());
                builder.UseWhen<OrganizationContextActivityTriggerMiddleware>(ActivityTrigger());
                builder.UseWhen<UnhandledExceptionCosmosTriggerMiddleware>(CosmosDbTriggers());
            })
            .ConfigureWebHost(builder =>
            {
                builder.ConfigureKestrel(serverOptions =>
                {
                    //NOTE: This is a workaround for a problem when using Newtonsoft as a serializer on the lastest Azure Function version. This is not desired, but it solves the problem for now. We'll need to migrate to System.Text.Json as our serializer
                    //TODO: Migrate the serializer to use System.Text.Json instead of Newtonsoft
                    serverOptions.AllowSynchronousIO = true;

                    serverOptions.Limits.MaxRequestBodySize = null;
                });
            })
            .ConfigureOpenApi()
            .ConfigureServices((appBuilder, services) =>
            {
                services.AddApplicationInsightsTelemetryWorkerService();
                services.ConfigureFunctionsApplicationInsights();
                services.AddDataPopulation();
                services.AddInfrastructure();
                services.AddAppInsightsTelemetry();

                CosmosDbSettings? cosmosDbConfig = appBuilder.Configuration.GetSection("ConnectionStrings:CMS")
                    .Get<CosmosDbSettings>();

                if (cosmosDbConfig != null)
                {
                    services.AddCosmosDb(cosmosDbConfig);
                    services.AddApplication();
                    services.AddQueryServices();
                    services.AddTriggerServices();
                    services.AddAzureFunctions(appBuilder);
                }
                else
                {
                    throw new ArgumentException("Connection string for CMS was not found");
                }
            })
            .Build();

        ODataSettings.SetInitializer(s =>
            {
                s.QuerySettings.PageSize = int.MaxValue;
                s.ValidationSettings.MaxNodeCount = int.MaxValue;
                s.ValidationSettings.MaxAnyAllExpressionDepth = 5;
            }
        );

        await host.RunAsync();
    }

    private static Func<FunctionContext, bool> GetAllowedHttpTriggers ()
    {
        return context =>
        {
            // We want to use this middleware only for http trigger invocations except swagger ones and data population related.
            return context.FunctionDefinition.InputBindings.Values.First(a => a.Type.EndsWith("Trigger")).Type ==
                   "httpTrigger"
                   && !context.FunctionDefinition.Name.Contains("Swagger") &&
                   !context.FunctionDefinition.Name.Contains("DataPopulation") &&
                   !context.FunctionDefinition.Name.Contains("MigrationFunction");

        };
    }

    private static Func<FunctionContext, bool> CosmosDbTriggers ()
    {
        return context =>
        {
            //Middleware for cosmosDbTriggers
            return context.FunctionDefinition.InputBindings.Values.First(
                a => a.Type.EndsWith("Trigger")).Type == "cosmosDBTrigger" ;
        };
    }
    
    private static Func<FunctionContext, bool> BlobTriggers ()
    {
        return context =>
        {
            //Middleware for blobTriggers
            return context.FunctionDefinition.InputBindings.Values.First(
                a => a.Type.EndsWith("Trigger")).Type == "blobTrigger";
        };
    }

    private static Func<FunctionContext, bool> ActivityTrigger ()
    {
        return context =>
        {
            //Middleware for activityTriggers
            return context.FunctionDefinition.InputBindings.Values.First(
                a => a.Type.EndsWith("Trigger")).Type == "activityTrigger";
        };
    }
    
    private static Func<FunctionContext, bool> GetAllowedOrganizationContextHttpTriggers ()
    {
        return context =>
        {
            // We want to use this middleware only for http trigger invocations except swagger ones and data population related.
            return context.FunctionDefinition.InputBindings.Values.First(a => a.Type.EndsWith("Trigger")).Type ==
                   "httpTrigger"
                   && !context.FunctionDefinition.Name.Contains("Swagger") &&
                   !context.FunctionDefinition.Name.Contains("DataPopulation") &&
                   !context.FunctionDefinition.Name.Contains("HealthCheckGetFunction");
        };
    }
}