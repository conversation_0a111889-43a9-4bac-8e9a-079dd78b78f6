﻿namespace Fattail.CreativeManagement.API.Domain.Creatives.Statuses;

public sealed class PendingApproval () : CreativeStatus(nameof(CreativeStatusEnum.PendingApproval), (int)CreativeStatusEnum.PendingApproval)
{
    public override string Description => "Pending Approval";

    public override bool CanTransitionTo (CreativeStatus creativeStatus)
    {
        return creativeStatus == Approved;
    }
}