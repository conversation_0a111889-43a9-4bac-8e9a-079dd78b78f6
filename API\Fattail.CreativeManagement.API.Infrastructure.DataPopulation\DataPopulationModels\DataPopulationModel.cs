﻿using Newtonsoft.Json;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DataPopulationModels
{
    [ExcludeFromCodeCoverage]
    public class DataPopulationModel
    {
        [JsonProperty(PropertyName = "parameters")]
        public Dictionary<string, string> Parameters { get; set; }

        [JsonProperty(PropertyName = "orgId")]
        public long OrgId { get; set; }

        [JsonProperty(PropertyName = "settingsType")]
        public long SettingsType { get; set; }

        public static bool IsValidDataPopulationModel (DataPopulationModel dataPopulationModel)
        {
            return dataPopulationModel != null &&
                dataPopulationModel.Parameters != null &&
                dataPopulationModel.Parameters.Any() &&
                !dataPopulationModel.Parameters.Any(p => string.IsNullOrEmpty(p.Key) || string.IsNullOrEmpty(p.Value));
        }
    }
}
