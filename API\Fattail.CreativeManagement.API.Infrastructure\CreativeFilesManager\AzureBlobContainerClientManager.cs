﻿using Azure.Storage.Blobs;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Infrastructure.CreativeFilesManager;

internal sealed class AzureBlobContainerClientManager
{
    public AzureBlobContainerClientManager (
        IOptions<BlobStorageSettings> blobStorageSettingsOptions,
        IOptions<List<OrganizationSettings>> organizationSettingsOptions)
    {
        BlobStorageSettings blobStorageSettings = blobStorageSettingsOptions.Value;
        IReadOnlyList<OrganizationSettings> organizationsSettings = organizationSettingsOptions.Value;
        
        var containerClients = new Dictionary<long, ContainerClients>();
        
        foreach (OrganizationSettings organizationSettings in organizationsSettings)
        {
            if (organizationSettings.Trial)
            {
                continue;
            }
            var creativeContainerClient = new Lazy<BlobContainerClient>(() => 
                new BlobContainerClient(
                    organizationSettings.AzureBlobStorageConnectionString, 
                    blobStorageSettings.CreativeFilesBlobContainerName));
            
            var zipCreativeContainerClient = new Lazy<BlobContainerClient>(() => 
                new BlobContainerClient(
                    organizationSettings.AzureBlobStorageConnectionString, 
                    blobStorageSettings.ZipCreativeFilesBlobContainerName));
            
            containerClients.Add(
                organizationSettings.OrgId,
                new ContainerClients(creativeContainerClient, zipCreativeContainerClient));
        }
        ContainerClient = containerClients;
    }
    public IReadOnlyDictionary<long, ContainerClients> ContainerClient { get; }
}