﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFiles.Common;

internal sealed record CreativeFilesUploadResponse(
    [property: OpenApiProperty(Description = "The id of the created in the creative file.")]
    long Id,
    [property: OpenApiProperty(Description = "The name of the creative file.")]
    string Name,
    [property: OpenApiProperty(Description = "The extension of the creative file.")]
    string Extension,
    [property: OpenApiProperty(Description = "The location where the creative file is stored.")]
    string Location,
    [property: OpenApiProperty(Description = "The size in bytes of the creative file.")]
    long Size,
    [property: OpenApiProperty(Description = "The metadata of the creative file. If it's an image, it will have \"Width\" and \"Height\"")]
    Dictionary<string, string> Metadata,
    [property: OpenApiProperty(Description = "The date when the creative file was stored.")]
    DateTime UploadedDate
);