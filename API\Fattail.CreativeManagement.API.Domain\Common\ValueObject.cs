﻿using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Domain.Common;

[ExcludeFromCodeCoverage]
public abstract class ValueObject
{
    protected abstract IEnumerable<object?> GetEqualityComponents ();

    public override bool Equals (object? obj)
    {
        if (obj == null)
        {
            return false;
        }

        if (GetType() != obj.GetType())
        {
            return false;
        }

        var valueObject = (ValueObject)obj;

        return GetEqualityComponents().SequenceEqual(valueObject.GetEqualityComponents());
    }

    public override int GetHashCode ()
    {
        return GetEqualityComponents()
            .Aggregate(1, (current, obj) =>
            {
                unchecked
                {
                    return (current * 23) + (obj?.GetHashCode() ?? 0);
                }
            });
    }

    public static bool operator == (ValueObject? a, ValueObject? b)
    {
        return (a is null && b is null) || (a is not null && b is not null && a.Equals(b));
    }

    public static bool operator != (ValueObject? a, ValueObject? b)
    {
        return !(a == b);
    }
}