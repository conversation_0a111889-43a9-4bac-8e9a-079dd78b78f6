﻿using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common.OpenApiModel;
public class PolymorphicCreativeFieldResponseOpenApiModel : OpenApiSchema
{
    public PolymorphicCreativeFieldResponseOpenApiModel()
    {
        AnyOf = new List<OpenApiSchema>
        {
            new MultiFileUploadFieldValueResponseOpenApiModel(),
            new FileUploadFieldValueResponseOpenApiModel(),
            new SingleLineTextFieldValueResponseOpenApiModel(),
            new MultiSelectOptionFieldValueResponseOpenApiModel(),
            new SingleSelectOptionFieldValueResponseOpenApiModel(),
            new MultiLineTextFieldValueResponseOpenApiModel()
        };
    }
}

