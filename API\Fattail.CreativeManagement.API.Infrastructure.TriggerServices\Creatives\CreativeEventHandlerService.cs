using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;

namespace Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives;

internal class CreativeEventHandlerService : EventHandlerService
{
    public CreativeEventHandlerService (ICosmosDbContainerFactory cosmosDbContainerFactory,
        IOrganizationContext organizationContext) : base(cosmosDbContainerFactory, organizationContext)
    {
    }

    public override string ContainerName => "Creatives";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }

    public async Task UpdateCreativeTemplateReference (string creativeTemplateId, string creativeTemplateName)
    {
        var creativesToUpdate = new List<Creative>();
        IQueryable<Creative> dbQuery = _container.GetItemLinqQueryable<Creative>(
                requestOptions: new QueryRequestOptions
                {
                    PartitionKey = ResolvePartitionKey(), MaxItemCount = int.MaxValue
                },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                })
            .Where(creative => creative.CreativeTemplateId == creativeTemplateId);

        using (var resultSet = dbQuery.ToFeedIterator())
        {
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Creative> response = await resultSet.ReadNextAsync();
                creativesToUpdate.AddRange(response.Resource);
            }
        }

        if (creativesToUpdate.Any())
        {
            foreach (Creative creativeEntity in creativesToUpdate)
            {
                creativeEntity.CreativeTemplateName = creativeTemplateName;
                await _container.UpsertItemAsync(creativeEntity);
            }
        }
    }
}