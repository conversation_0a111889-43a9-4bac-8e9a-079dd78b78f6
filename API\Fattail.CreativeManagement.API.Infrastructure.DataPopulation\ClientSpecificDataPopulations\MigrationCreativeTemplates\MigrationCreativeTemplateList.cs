﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeTemplates;

public class MigrationCreativeTemplateList
{
    private readonly List<string> _orgIdList;
    public readonly List<CreativeTemplate> CreativeTemplates;
    private const string CreativeTemplateId = "574650397348900";
    private const string CreativeFieldFileId = "574650397348901";
    private const string CreativeFieldDestinationUrlId = "574650397348902";
    private const string CreativeFieldApplyCreativeToFuturePlacementId = "574650397348903";
    private const string CreativeFieldCommentsId = "574650397348904";
    private const string ApplyCreativeToFuturePlacementFieldOptionId = "1";

    public MigrationCreativeTemplateList (List<string> orgIdList)
    {
        _orgIdList = orgIdList;
        CreativeTemplates = new List<CreativeTemplate>();
        var creativeTemplateCreativeFields = new List<CreativeTemplateCreativeField>();

        creativeTemplateCreativeFields.Add(new CreativeTemplateCreativeField()
        {
            DisplayOrder = 1, Id = CreativeFieldFileId, Name = "File", Type = CreativeFieldTypeEnum.MultiFileUpload
        });
        creativeTemplateCreativeFields.Add(new CreativeTemplateCreativeField()
        {
            DisplayOrder = 2,
            Id = CreativeFieldDestinationUrlId,
            Name = "Destination URL",
            Type = CreativeFieldTypeEnum.SingleLineText
        });
        creativeTemplateCreativeFields.Add(new CreativeTemplateMultiSelectCreativeField()
        {
            DisplayOrder = 3,
            Id = CreativeFieldApplyCreativeToFuturePlacementId,
            Name = "For Digital products only - Apply creative to future placement as applicable",
            Type = CreativeFieldTypeEnum.MultiSelectOption,
            Options = new []{ new CreativeTemplateSelectOption
            {
                Id = ApplyCreativeToFuturePlacementFieldOptionId,
                Description = "Yes"
            }}
        });
        creativeTemplateCreativeFields.Add(new CreativeTemplateCreativeField()
        {
            DisplayOrder = 4,
            Id = CreativeFieldCommentsId,
            Name = "Comments",
            Type = CreativeFieldTypeEnum.MultiLineText
        });

        foreach (string orgId in _orgIdList)
        {
            CreativeTemplates.Add(new CreativeTemplate()
            {
                Id = CreativeTemplateId,
                OrgId = orgId,
                Archive = false,
                CreativeFields = creativeTemplateCreativeFields.ToArray(),
                Name = "Creative Migration Template"
            });
        }
    }
}