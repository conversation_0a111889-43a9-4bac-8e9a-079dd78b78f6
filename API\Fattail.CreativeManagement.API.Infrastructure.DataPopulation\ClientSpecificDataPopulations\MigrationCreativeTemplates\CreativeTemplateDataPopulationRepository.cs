﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeTemplates;

public class CreativeTemplateDataPopulationRepository : DataPopulationRepository<CreativeTemplate>
{
    public CreativeTemplateDataPopulationRepository (ICosmosDbContainerFactory cosmosDbContainerFactory,
        IOrganizationContext organizationContext) :
        base(cosmosDbContainerFactory, organizationContext)
    {
    }

    public override string ContainerName => "CreativeTemplates";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }
}