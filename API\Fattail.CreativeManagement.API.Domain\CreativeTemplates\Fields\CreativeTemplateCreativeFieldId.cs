﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

public class CreativeTemplateCreativeFieldId : ValueObject
{
    public CreativeTemplateCreativeFieldId (CreativeTemplateId creativeTemplateId, CreativeFieldId creativeFieldId)
    {
        CreativeTemplateId = creativeTemplateId;
        CreativeFieldId = creativeFieldId;
    }

    public CreativeTemplateId CreativeTemplateId { get; }
    public CreativeFieldId CreativeFieldId { get; }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return CreativeTemplateId;
        yield return CreativeFieldId;
    }
}