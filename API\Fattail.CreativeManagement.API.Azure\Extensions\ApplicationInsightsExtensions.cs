﻿using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights.Extensibility.Implementation;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Azure.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationInsightsExtensions
{
    private const string AppInsightsConnectionStringSettingName = "APPLICATIONINSIGHTS_CONNECTION_STRING";

    public static IServiceCollection AddAppInsightsTelemetry (this IServiceCollection serviceCollection)
    {
        serviceCollection.Configure<TelemetryConfiguration>(telemetryConfiguration =>
        {
            TelemetryProcessorChainBuilder? builder =
                telemetryConfiguration.DefaultTelemetrySink.TelemetryProcessorChainBuilder;

            // Using fixed rate sampling
            double fixedSamplingPercentage = 100;
            builder.UseSampling(fixedSamplingPercentage);
        });

        serviceCollection.AddApplicationInsightsTelemetry(new ApplicationInsightsServiceOptions
        {
            EnableAdaptiveSampling = false,
            ConnectionString = Environment.GetEnvironmentVariable(AppInsightsConnectionStringSettingName)
        });

        return serviceCollection;
    }
}