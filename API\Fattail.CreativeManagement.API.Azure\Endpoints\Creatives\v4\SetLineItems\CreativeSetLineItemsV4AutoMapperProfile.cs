﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeSetLineItems;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.SetLineItems;

internal sealed class CreativeSetLineItemsV4AutoMapperProfile : Profile
{
    internal const string CreativeSetLineItemsCommandIdParameterName = "Id";

    public CreativeSetLineItemsV4AutoMapperProfile ()
    {
        CreateMap<CreativeSetLineItemsRequest, CreativeSetLineItemsCommand>()
            .ForMember(dest => dest.Id,
                memberOptions => memberOptions.MapFrom((src, dest, opt, context) =>
                    (long)context.Items[CreativeSetLineItemsCommandIdParameterName]))
            .ForMember(dest => dest.LineItemIds,
                opt => opt.Ignore())
            .ConstructUsing((src, context) =>
                new CreativeSetLineItemsCommand(
                    (long)context.Items[CreativeSetLineItemsCommandIdParameterName],
                    src.LineItems == null ? null : [..src.LineItems.Select(li => li.Id)],
                    src.UpdatedBy));
    }
}