﻿using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

public sealed class CreativeFile : Entity
{
    public string Name { get; set; }
    public string BlobName { get; set; }
    public string Location { get; set; }
    public string Extension { get; set; }
    public CreativeFileTypeEnum Type { get; set; }
    public long Size { get; set; }
    public Dictionary<string, string> Metadata { get; set; } = new ();
    public DateTime UploadedDate { get; set; }
    public bool ToBeDeleted { get; set; }
}