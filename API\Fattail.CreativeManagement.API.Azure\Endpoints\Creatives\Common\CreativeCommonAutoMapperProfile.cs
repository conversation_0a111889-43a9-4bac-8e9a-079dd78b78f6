﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using CommonCreatives = Fattail.CreativeManagement.API.Application.Creatives;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common;

internal sealed class CreativeCommonAutoMapperProfile: Profile
{
    public CreativeCommonAutoMapperProfile ()
    {
        CreateMap<CreativeFieldRequest, CreativeFieldDto>()
            .Include(typeof(CreativeFieldValue<>), typeof(Application.Creatives.CreativeFieldValue<>));

        CreateMap(typeof(CreativeFieldValue<>), typeof(Application.Creatives.CreativeFieldValue<>))
            .Include(typeof(MultiFileUploadFieldValue), typeof(Application.Creatives.MultiFileUploadFieldValue))
            .Include(typeof(FileUploadFieldValue), typeof(Application.Creatives.FileUploadFieldValue))
            .Include(typeof(SingleLineTextFieldValue), typeof(Application.Creatives.SingleLineTextFieldValue))
            .Include(typeof(MultiSelectOptionFieldValue), typeof(Application.Creatives.MultiSelectOptionFieldValue))
            .Include(typeof(SingleSelectOptionFieldValue), typeof(Application.Creatives.SingleSelectOptionFieldValue))
            .Include(typeof(MultiLineTextFieldValue), typeof(Application.Creatives.MultiLineTextFieldValue));

        CreateMap<MultiFileUploadFieldValue, Application.Creatives.MultiFileUploadFieldValue>();
        CreateMap<FileUploadFieldValue, Application.Creatives.FileUploadFieldValue>();
        CreateMap<SingleLineTextFieldValue, Application.Creatives.SingleLineTextFieldValue>();
        CreateMap<MultiSelectOptionFieldValue, Application.Creatives.MultiSelectOptionFieldValue>();
        CreateMap<SingleSelectOptionFieldValue, Application.Creatives.SingleSelectOptionFieldValue>();
        CreateMap<MultiLineTextFieldValue, Application.Creatives.MultiLineTextFieldValue>();

        CreateMap<CreativeResult, CreativeResponse>();
        CreateMap<CreativeResult, CreativeResponseV2>();
        CreateMap<CreativeResult, CreativeResponseV3>();
        CreateMap<CreativeResult, CreativeResponseV4>();
        
        CreateMap<CreativeStatusResult, CreativeStatusResponse>();
        CreateMap<ApprovalInformationResult, ApprovalInformationResponse>();

        CreateMap<CreativeFileResult, CreativeFileResponse>();
        CreateMap<CreativeFieldValueResult, CreativeFieldValueResponse>()
            .Include(typeof(CreativeFieldValueResult<>), typeof(CreativeFieldValueResponse<>));

        CreateMap(typeof(CreativeFieldValueResult<>), typeof(CreativeFieldValueResponse<>))
            .Include(typeof(MultiFileUploadFieldValueResult), typeof(MultiFileUploadCreativeFieldResponse))
            .Include(typeof(FileUploadFieldValueResult), typeof(FileUploadCreativeFieldResponse))
            .Include(typeof(SingleLineTextFieldValueResult), typeof(SingleLineTextCreativeFieldResponse))
            .Include(typeof(MultiSelectOptionFieldValueResult), typeof(MultiSelectOptionCreativeFieldResponse))
            .Include(typeof(SingleSelectOptionFieldValueResult), typeof(SingleSelectOptionCreativeFieldResponse))
            .Include(typeof(MultiLineTextFieldValueResult), typeof(MultiLineTextCreativeFieldResponse));

        CreateMap<MultiFileUploadFieldValueResult, MultiFileUploadCreativeFieldResponse>();
        CreateMap<SingleLineTextFieldValueResult, SingleLineTextCreativeFieldResponse>();
        CreateMap<FileUploadFieldValueResult, FileUploadCreativeFieldResponse>();
        CreateMap<MultiSelectOptionFieldValueResult, MultiSelectOptionCreativeFieldResponse>();
        CreateMap<SingleSelectOptionFieldValueResult, SingleSelectOptionCreativeFieldResponse>();
        CreateMap<MultiLineTextFieldValueResult, MultiLineTextCreativeFieldResponse>();
    }
}
