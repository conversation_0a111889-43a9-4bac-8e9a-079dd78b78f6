using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

internal sealed record CreativeTemplateResponse ()
{
    [property: OpenApiProperty(Description = "Creative template id")]
    public long Id { get; init; }

    [property: OpenApiProperty(Description = "Creative template name")]
    public string Name { get; init; }

    [property: OpenApiProperty(Description = "Creative template type")]
    public CreativeType CreativeType { get; init; }

    [property: OpenApiProperty(Description = "Creative fields")]
    public IReadOnlyList<CreativeFieldResponse> CreativeFields { get; init; }

    [property: OpenApiProperty(Description = "ID of the template this was cloned from, if applicable")]
    public long? ClonedFrom { get; init; }
}