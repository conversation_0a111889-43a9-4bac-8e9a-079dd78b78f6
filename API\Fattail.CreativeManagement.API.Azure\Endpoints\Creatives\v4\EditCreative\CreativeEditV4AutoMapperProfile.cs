﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.EditCreative;

internal sealed class CreativeEditV4AutoMapperProfile : Profile
{
    internal const string CreativeEditCommandIdParameterName = "Id";

    public CreativeEditV4AutoMapperProfile ()
    {
        CreateMap<CreativeEditRequestV4, CreativeEditCommand>()
            .ForMember(dest => dest.Id,
                opt => opt.MapFrom((src, dest, opt, context) =>
                    (long)context.Items[CreativeEditCommandIdParameterName]))
            .ForMember(dest => dest.LineItemIds,
                opt => opt.Ignore()) // LineItemIds are not used in this version
            .ConstructUsing((src, context) =>
                new CreativeEditCommand(
                    Id: (long)context.Items[CreativeEditCommandIdParameterName]!,
                    Name: src.Name,
                    LineItemIds: null,
                    Fields: context.Mapper.Map<IReadOnlyList<CreativeFieldDto>>(src.Fields),
                    UpdatedBy: src.UpdatedBy
                )
            );
    }
}