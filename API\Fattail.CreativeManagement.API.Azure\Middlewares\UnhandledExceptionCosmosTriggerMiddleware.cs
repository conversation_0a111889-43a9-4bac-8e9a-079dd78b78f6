﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal sealed class UnhandledExceptionCosmosTriggerMiddleware : IFunctionsWorkerMiddleware
{
    private readonly ILogger _logger;

    public UnhandledExceptionCosmosTriggerMiddleware (ILogger<OrganizationContextCosmosDbTriggerMiddleware> iLogger)
    {
        _logger = iLogger;
    }

    public async Task Invoke (FunctionContext context, FunctionExecutionDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{context.FunctionDefinition.Name} - Unhandled error exception occurred");
        }
    }
}