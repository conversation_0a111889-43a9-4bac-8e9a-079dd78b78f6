﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateInvalidFileSizeError: ErrorBase
{
    public CreativeTemplateInvalidFileSizeError (string creativeFieldId, string entity, string validationRuleType)
        : base($"The creative field with id {creativeFieldId} has invalid value format for validation type {validationRuleType}.",
            ErrorType.CreativeTemplateFieldInvalidFileSize)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(validationRuleType), validationRuleType);
    }
}