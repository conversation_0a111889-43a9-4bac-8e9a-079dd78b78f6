﻿using Dawn;

namespace Fattail.CreativeManagement.API.Domain.Common.Guards;

internal static class NotFutureDateGuard
{
    public static ref readonly Guard.ArgumentInfo<DateTime> NotFutureDate (
        in this Guard.ArgumentInfo<DateTime> argument)
    {
        //NOTE: This is needed due to avoid some delays between servers time synchronization
        const int OffSetInSeconds = 2;
        DateTime now = DateTime.UtcNow.AddSeconds(OffSetInSeconds);
        if (argument.Value.ToUniversalTime() > now)
        {
            throw Guard.Fail(new ArgumentException(
                $"{argument.Name} ({argument.Value.ToUniversalTime()}) can't be a future date. Current UTC date {now}",
                argument.Name));
        }

        return ref argument;
    }
}