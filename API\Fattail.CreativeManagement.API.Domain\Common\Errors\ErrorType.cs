﻿namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

public enum ErrorType
{
    EntityNotFound = 1001,
    EntityInUse = 1002,
    DeletePartitionError = 1003,
    RequiredValueMissing = 9005,
    InvalidField = 9011,
    DuplicateCreativeTemplateName = 9014,
    CreativeFileSizeExceededError = 9016,
    CreativeFileExtensionNotAllowed = 9017,
    CreativeFileStorage = 9018,
    CreativeFileNotFoundInAbOms = 9019,
    CreativeFilesToDownloadAreInvalid = 9021,
    CreativeFilesDownloadError = 9023,
    DuplicateCreativeFieldName = 9025,
    FileUploadExtensionsValidationError = 9026,
    FileSizeValidationError = 9027,
    RequiredValidationError = 9028,
    CreativeTemplateFieldInvalidFileSize = 9029,
    CreativeTemplateFieldInvalidFileExtension = 9030,
    DuplicateCreativeTemplateValidationRule = 9031,
    DuplicateCreativeTemplateField = 9032,
    CreativeTemplateFieldUnsupportedValidationRuleType = 9033,
    CreativeTemplateDuplicateFieldOrderError = 9034,
    CreativeTemplateFieldMissingDisplayOrderPropertyError = 9035,
    CreativeIsMigratedError = 9036,
    MigratedCreativeWasUpdatedError = 9040,
    CreativeFileDeleteError = 9041,
    CreativeInvalidStatusTransition = 9042,
    PredefinedTemplateAlreadyCloned = 9043,
    PredefinedTemplateCannotBeArchived = 9044,
}