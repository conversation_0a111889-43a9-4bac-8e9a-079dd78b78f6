﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields.SpecialProperties;

[TestFixture]
public class SectionDividerSetSpecialPropertiesValuesTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(_fieldId);
    }

    private Mock<IIdManager> _idManagerMock = null!;
    private readonly string _fieldName = "fieldName";
    private readonly long _fieldId = 1234;

    [Test]
    public async Task Section_divider_field_can_not_set_invalid_special_properties ()
    {
        CreativeTemplateSectionDividerCreativeField creativeField = await CreateCreativeTemplateField();

        var invalidSpecialProperty = new SpecialPropertyKey("invalid_special_property");
        const string ContentValue = "invalid special property";

        Result setSpecialPropertiesValuesResult = creativeField.SetSpecialPropertiesValues(new Dictionary<SpecialPropertyKey, object?>(){
            { invalidSpecialProperty, ContentValue } });

        setSpecialPropertiesValuesResult.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateFieldInvalidSpecialPropertiesError(creativeField.Id, new HashSet<SpecialPropertyKey>([invalidSpecialProperty]),
                nameof(CreativeTemplateSectionDividerCreativeField)));
    }

    [Test]
    public async Task Section_divider_field_type_content_special_property_value_can_be_set ()
    {
        CreativeTemplateSectionDividerCreativeField creativeField = await CreateCreativeTemplateField();
        const string ContentValue = "content value";

        Result setSpecialPropertiesValuesResult = creativeField.SetSpecialPropertiesValues(new Dictionary<SpecialPropertyKey, object?>(){
            { SpecialPropertyKey.Content, ContentValue } });

        setSpecialPropertiesValuesResult.Should().BeSuccess();
        creativeField.Content.Should().Be(ContentValue);
    }

    [Test]
    public async Task Section_divider_field_type_content_special_property_fails_when_value_is_not_string ()
    {
        CreativeTemplateSectionDividerCreativeField creativeField = await CreateCreativeTemplateField();
        const int ContentValue = 1;

        Result setSpecialPropertiesValuesResult = creativeField.SetSpecialPropertiesValues(new Dictionary<SpecialPropertyKey, object?>(){
            { SpecialPropertyKey.Content, ContentValue } });

        setSpecialPropertiesValuesResult.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateFieldSpecialPropertyInvalidTypeError(creativeField.Id, nameof(CreativeTemplateSectionDividerCreativeField.Content),
                nameof(String), nameof(CreativeTemplateSectionDividerCreativeField)));
    }

    private async Task<CreativeTemplateSectionDividerCreativeField> CreateCreativeTemplateField ()
    {
        var creativeFieldId = new CreativeFieldId(_fieldId);
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(_fieldId);

        CreativeField creativeField = CreativeField.Create(creativeFieldId, _fieldName, CreativeFieldType.SectionDivider, creativeFieldUniqueNameRequirement, false, null, null).Value;

        Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId, new HashSet<CreativeField>([creativeField]), 1);

        return (CreativeTemplateSectionDividerCreativeField)creativeTemplateFieldResult.Value;
    }
}
