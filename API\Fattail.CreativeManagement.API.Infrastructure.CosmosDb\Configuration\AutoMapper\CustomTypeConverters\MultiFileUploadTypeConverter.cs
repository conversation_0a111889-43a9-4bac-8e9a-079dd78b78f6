﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using MultiFileUploadFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.MultiFileUploadFieldValue;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper.CustomTypeConverters;

internal sealed class
    MultiFileUploadTypeConverter : ITypeConverter<MultiFileUploadFieldValue, Entities.MultiFileUploadFieldValue>
{
    private readonly ICreativeFileRepository _creativeFileRepository;

    public MultiFileUploadTypeConverter (ICreativeFileRepository creativeFileRepository)
    {
        _creativeFileRepository = creativeFileRepository;
    }

    public Entities.MultiFileUploadFieldValue Convert (MultiFileUploadFieldValue source,
        Entities.MultiFileUploadFieldValue destination,
        ResolutionContext context)
    {
        Task<IReadOnlyList<CreativeFileValue>> creativeFilesFindManyByIdATask =
            _creativeFileRepository.FindManyByIdAsync<CreativeFileValue>(source.Value.ToArray());
        creativeFilesFindManyByIdATask.Wait();

        IReadOnlyList<CreativeFileValue> creativeFiles = creativeFilesFindManyByIdATask.Result!;

        destination.Value = creativeFiles.ToArray();

        return destination;
    }
}