﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeApprove;

public class CreativeApproveHandler (
    IDateTimeProvider dateTimeProvider, 
    ICreativeRepository creativeRepository) 
    : IRequestHandler<CreativeApproveCommand, Result<CreativeResult>>
{
    public async Task<Result<CreativeResult>> Handle (CreativeApproveCommand request, CancellationToken cancellationToken)
    {
        Creative? creative = await creativeRepository.FindByIdAsync(request.Id);

        if (creative == null)
        {
            return Result.Fail(new EntityNotFoundError(request.Id.ToString(), nameof(Creative)));
        }

        Result<ApprovalInformation> approvalInformationCreateResult = ApprovalInformation.Create(request.ApproverName, dateTimeProvider.CurrentTime);

        if (approvalInformationCreateResult.IsFailed)
        {
            return approvalInformationCreateResult.ToResult();
        }

        Result approvalResult = creative.Approve(approvalInformationCreateResult.Value);

        if (approvalResult.IsFailed)
        {
            return approvalResult;
        }

        return await creativeRepository.UpdateAsync<CreativeResult>(creative);
    }
}