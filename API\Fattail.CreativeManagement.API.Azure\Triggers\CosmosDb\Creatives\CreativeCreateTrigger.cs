using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives.Create;
using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives.Events;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Azure.Triggers.CosmosDb.Creatives;

public class CreativeCreateTrigger
{
    private readonly ILogger<CreativeCreateTrigger> _logger;
    private readonly IMediator _mediator;

    public CreativeCreateTrigger (
        IMediator mediator, 
        ILogger<CreativeCreateTrigger> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    [Function(nameof(CreativeCreateTrigger))]
    public async Task Run ([CosmosDBTrigger(
            "CreativeManagement",
            "Creatives",
            Connection = "CMS:ConnectionString",
            CreateLeaseContainerIfNotExists = true)]
        IReadOnlyList<CreativeDocument> creativeDocuments, FunctionContext context)
    {
        foreach (CreativeDocument creativeDocument in creativeDocuments)
        {
            _logger.LogInformation("Creative id => {creativeId} Campaign Id=> {campaignId}", creativeDocument.Id,
                creativeDocument.CampaignId);

            switch (creativeDocument.LastAction)
            {
                case ActionType.Create:
                    await _mediator.Publish(new CreativeCreatedEvent(
                        long.Parse(creativeDocument.Id),
                        creativeDocument.CampaignId));
                    //TODO: Add event for lineItem creation
                    break;
                case ActionType.Update:
                    await _mediator.Publish(new CreativeUpdatedEvent(
                        long.Parse(creativeDocument.Id),
                        creativeDocument.CampaignId));
                    break;
                case ActionType.Migrate:
                default:
                    break;
            }
        }
    }
}