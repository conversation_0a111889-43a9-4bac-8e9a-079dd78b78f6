﻿using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using FluentResults;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;

public interface ICreativeFileStorageManager
{
    Task<Result<CreativeFileStorageMetadata>> StoreCreativeFile (CreativeFileName creativeFileName,
        Stream content);

    Task<Result<Tuple<CreativeFileStorageMetadata, Stream>>> StoreAbOmsCreativeFile (CreativeFileName creativeFileName, string fileLocation);
    
    Task<Result> DeleteOrganizationVirtualFolder ();
    
    Task<double> GetFileSizeInMegabytes (CreativeFile creativeFile);

    Task<Result> DeleteCreativeFile (string blobName);
}