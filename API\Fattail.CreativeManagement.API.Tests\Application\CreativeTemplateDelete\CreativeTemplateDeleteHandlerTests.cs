﻿using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateDelete;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativesTemplateDelete;

[TestFixture]
public class CreativeTemplateDeleteHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(1234);

        _creativeTemplateRepository = new Mock<ICreativeTemplateRepository>();
        _creativeRepository = new Mock<ICreativeRepository>();

        _creativeTemplateInUseVerifier = new CreativeTemplateInUseVerifier(_creativeRepository.Object);

        _creativeTemplateDeleteHandler = new CreativeTemplateDeleteHandler(_creativeTemplateRepository.Object, _creativeTemplateInUseVerifier);
        _creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
    }

    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepository = null!;
    private Mock<ICreativeRepository> _creativeRepository = null!;
    private CreativeTemplateDeleteHandler _creativeTemplateDeleteHandler = null!;
    private CreativeTemplateInUseVerifier _creativeTemplateInUseVerifier = null!;
    private CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = null!;

    [Test]
    public async Task Creative_template_is_in_use_and_cant_be_deleted ()
    {
        long id = 2;

        var creativeTemplateDeleteCommand = new CreativeTemplateDeleteCommand(id);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            CreativeTemplateName.Create("Old template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(1), 1),
                new(new(2), 2)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new (1), "Creative field 1", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new (2), "Creative field 2", CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value
            ]),
            false
        );
        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        _creativeRepository.Setup(c =>
                c.FindManyByCreativeTemplateIdAsync<CreativeResult>(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync((CreativeTemplateId creativeTemplateId) =>
            {
                var creativeResultItems = new List<CreativeResult>()
                {
                    new CreativeResult(1, null, (long)creativeTemplateId, "creative template 1", "creative 1", 1, 1, new List<long>(),
                        new List<CreativeFieldValueResult>(), new CreativeStatusResult(CreativeStatusEnum.PendingApproval, "Pending Approval"), "Updater name", DateTime.Now.ToUniversalTime(), null)
                };
                return creativeResultItems;
            });

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.DeleteAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(Result.Ok());

        Result result = await _creativeTemplateDeleteHandler.Handle(creativeTemplateDeleteCommand, CancellationToken.None);

        result.Should().BeFailure().And.HaveReason(new EntityInUseError(nameof(CreativeTemplate)));
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeRepository.Verify(c => c.FindManyByCreativeTemplateIdAsync<CreativeResult>(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeTemplateRepository.Verify(c => c.DeleteAsync(It.IsAny<CreativeTemplateId>()),
            Times.Never);
    }

    [Test]
    public async Task Creative_template_not_found ()
    {
        long id = 2;

        var creativeTemplateDeleteCommand = new CreativeTemplateDeleteCommand(id);

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync((CreativeTemplate)null);

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.DeleteAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(Result.Ok());

        Result result = await _creativeTemplateDeleteHandler.Handle(creativeTemplateDeleteCommand, CancellationToken.None);

        result.Should().BeFailure().And.HaveReason(new EntityNotFoundError(id.ToString(), nameof(CreativeTemplate)));
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeRepository.Verify(c => c.FindManyByCreativeTemplateIdAsync<CreativeResult>(It.IsAny<CreativeTemplateId>()),
            Times.Never);
        _creativeTemplateRepository.Verify(c => c.DeleteAsync(It.IsAny<CreativeTemplateId>()),
            Times.Never);
    }

    [Test]
    public async Task Creative_template_can_be_deleted ()
    {
        long id = 2;
        var creativeTemplateDeleteCommand = new CreativeTemplateDeleteCommand(id);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            CreativeTemplateName.Create("Old template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(1), 1),
                new(new(2), 2)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new (1), "Creative field 1", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new (2), "Creative field 2", CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        _creativeRepository.Setup(c =>
                c.FindManyByCreativeTemplateIdAsync<CreativeResult>(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(new List<CreativeResult>());

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.DeleteAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(Result.Ok());

        Result result = await _creativeTemplateDeleteHandler.Handle(creativeTemplateDeleteCommand, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeRepository.Verify(c => c.FindManyByCreativeTemplateIdAsync<CreativeResult>(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeTemplateRepository.Verify(c => c.DeleteAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
    }
}
