﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Tests.BaseResponse;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using FluentAssertions;
using Moq;
using NUnit.Framework;
using System.Net;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Tests.ClientSpecificDataPopulations.CreativeFields
{
    public class CreativeFieldDataPopulationTests
    {
        private Mock<IDataPopulationRepository<CreativeField>> _repositoryMock;

        [SetUp]
        public void Setup ()
        {
            _repositoryMock = new Mock<IDataPopulationRepository<CreativeField>>();
            _repositoryMock.Setup(_ => _.UpsertWithResponseAsync(It.IsAny<CreativeField>(), It.IsAny<string>()))
                .ReturnsAsync(new BaseTestResponse<CreativeField>(HttpStatusCode.Created));
        }

        [Test]
        public async Task ExampleDataPopulation_Run_WithCorrectData ()
        {
            //Act
            ICollection<DataPopulationResponseModel> createCreativeFieldsAttempt =
                await CreativeFieldDataPopulation.RunAsync(_repositoryMock.Object);

            //Assert
            createCreativeFieldsAttempt.Should()
                .NotBeNullOrEmpty()
                .And.OnlyHaveUniqueItems()
                .And.HaveCount(50, "It is necessary CREATIVE MOCKS field for each OrgId in Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields");

            createCreativeFieldsAttempt.Select(x => x.Message)
                .Should().ContainMatch("Created Item: CreativeField *");

        }
    }
}

