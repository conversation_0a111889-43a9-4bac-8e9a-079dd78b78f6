using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common;

internal sealed record CreativeResponse
{
    [property: OpenApiProperty(Description = "Creative id")]
    public long Id { get; init; }

    [property: OpenApiProperty(Description = "Creative template id")]
    public long CreativeTemplateId { get; init; }

    [property: OpenApiProperty(Description = "Creative template name")]
    public string CreativeTemplateName { get; init; }

    [property: OpenApiProperty(Description = "Creative name")]
    public string? Name { get; init; }

    [property: OpenApiProperty(Description = "Campaign id")]
    public long CampaignId { get; init; }

    [property: OpenApiProperty(Description = "Creative fields")]
    public IReadOnlyList<CreativeFieldValueResponse> Fields { get; init; }

    [property: OpenApiProperty(Description = "Last time the creative was updated")]
    public DateTime LastUpdatedOn { get; init; }

    [property: OpenApiProperty(Description = "Creative updated by")]
    public string? LastUpdatedBy { get; init; }
}

public abstract record CreativeFieldValueResponse (
    [property: OpenApiProperty(Description = "Creative field id")]
    long Id,
    [property: OpenApiProperty(Description = "Creative field name")]
    string Name,
    [property: OpenApiProperty(Description = "Creative field type")]
    CreativeFieldType Type);

public abstract record CreativeFieldValueResponse<TValue> (
    long Id,
    string Name,
    CreativeFieldType Type,
    [property: OpenApiProperty(Description = "Creative field value")]
    TValue Value) : CreativeFieldValueResponse(Id, Name, Type);

public sealed record CreativeFileResponse
(
    [property: OpenApiProperty(Description = "Creative file id")]
    long Id,
    [property: OpenApiProperty(Description = "Creative file name")]
    string Name,
    [property: OpenApiProperty(Description = "Creative file name extension")]
    string Extension,
    [property: OpenApiProperty(Description = "Creative file location")]
    string Location, 
    [property: OpenApiProperty(Description = "Creative file size")]
    long Size,
    [property: OpenApiProperty(Description = "Creative file Metadata")]
    Dictionary<string, string> Metadata
);

public sealed record FileUploadCreativeFieldResponse (long Id, string Name, CreativeFileResponse Value)
    : CreativeFieldValueResponse<CreativeFileResponse>(Id, Name, CreativeFieldType.FileUpload, Value);

public sealed record MultiFileUploadCreativeFieldResponse (long Id, string Name,
        IReadOnlyList<CreativeFileResponse> Value)
    : CreativeFieldValueResponse<IReadOnlyList<CreativeFileResponse>>(Id, Name, CreativeFieldType.MultiFileUpload,
        Value);

public sealed record SingleLineTextCreativeFieldResponse (long Id, string Name, string Value)
    : CreativeFieldValueResponse<string>(Id, Name, CreativeFieldType.SingleLineText, Value);

public sealed record MultiSelectOptionCreativeFieldResponse (long Id, string Name, IReadOnlyList<long> Value)
    : CreativeFieldValueResponse<IReadOnlyList<long>>(Id, Name, CreativeFieldType.MultiSelectOption, Value);
    
public sealed record SingleSelectOptionCreativeFieldResponse (long Id, string Name, long? Value)
    : CreativeFieldValueResponse<long?>(Id, Name, CreativeFieldType.SingleSelectOption, Value);
    
public sealed record MultiLineTextCreativeFieldResponse (long Id, string Name, string Value)
    : CreativeFieldValueResponse<string>(Id, Name, CreativeFieldType.MultiLineText, Value);