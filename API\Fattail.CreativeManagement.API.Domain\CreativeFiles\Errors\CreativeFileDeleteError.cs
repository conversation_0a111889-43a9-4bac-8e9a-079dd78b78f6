﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;

public sealed class CreativeFileDeleteError : ErrorBase
{
    public CreativeFileDeleteError (string id)
        : base($"An error has occurred while deleting the creative file with id {id}",
            ErrorType.CreativeFileDeleteError)
    {
        Metadata.Add(nameof(id), id);
    }
}