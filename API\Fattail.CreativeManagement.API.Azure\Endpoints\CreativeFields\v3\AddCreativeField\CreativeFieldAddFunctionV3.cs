﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.v3.AddCreativeField;

public class CreativeFieldAddFunctionV3 : BaseFunction
{
    public CreativeFieldAddFunctionV3 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Add a creative field v3", 
        tags: new[] { "v3" },
        Summary = "Add a creative field v3")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeFieldAddRequest),
        Description = "The creative field to add", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeFieldResponse))]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeFieldAddFunctionV3))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v3/creative-fields")]
        HttpRequestData req)
    {
        CreativeFieldAddCommand creativeAddCommand =
            await FromRequest<CreativeFieldAddRequest, CreativeFieldAddCommand>(req);

        Result<CreativeFieldResult> result = await _mediator.Send(creativeAddCommand);

        HttpResponseData response =
            await FromResultWithLocation<CreativeFieldResult, CreativeFieldResponse>(req, result,
                result.ValueOrDefault?.Id);

        return response;
    }
}