﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload.Errors;

public sealed class CreativeFileStorageError : ErrorBase
{
    public CreativeFileStorageError (string creativeFileName)
        : base($"An error has occurred while storing the creative file {creativeFileName}",
            ErrorType.CreativeFileStorage)
    {
        Metadata.Add(nameof(creativeFileName), creativeFileName);
    }
}