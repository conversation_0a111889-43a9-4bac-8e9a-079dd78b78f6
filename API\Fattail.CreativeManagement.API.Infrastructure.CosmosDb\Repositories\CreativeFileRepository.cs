﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using FluentResults;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Options;
using System.Net;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class CreativeFileRepository : CosmosDbRepository<CreativeFile, CreativeFileId, Entities.CreativeFile>,
    ICreativeFileRepository
{
    public CreativeFileRepository (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IMapper mapper,
        IOrganizationContext organizationContext,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings) : base(cosmosDbContainerFactory, mapper,
        organizationContext, parallelExecutionSettings)
    {
    }

    public override string ContainerName => "CreativeFiles";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }

    public async Task<Result> DeletePartitionAsync ()
    {
        PartitionKey partitionKey = ResolvePartitionKey();
        ResponseMessage? result = await _container.DeleteAllItemsByPartitionKeyStreamAsync(partitionKey);
        return result.StatusCode == HttpStatusCode.OK ? Result.Ok() : Result.Fail(partitionKey.ToString());
    }

    public async Task<IReadOnlyList<TResult>> FindMigratedAsync<TResult> ()
    {
        var items = new List<TResult>();
        IQueryable<Entities.CreativeFile> creativeFilesDbQuery = _container
            .GetItemLinqQueryable<Entities.CreativeFile>(
                requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                })
            .Where(c => c.LastAction == ActionType.Migrate);

        using (var resultSet = creativeFilesDbQuery.ToFeedIterator())
        {
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Entities.CreativeFile>? response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                items.AddRange(_mapper.Map<IReadOnlyList<TResult>>(response.Resource));
            }
        }
        return items;
    }
}