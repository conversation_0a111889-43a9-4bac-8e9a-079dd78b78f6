﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.CreativeFileSanitizers;

public class MultiFileUploadSanitizer : ISanitizer
{
    private readonly ICreativeFilesVerifier _creativeFilesVerifier;

    public MultiFileUploadSanitizer (ICreativeFilesVerifier creativeFilesVerifier)
    {
        _creativeFilesVerifier = creativeFilesVerifier;
    }

    public CreativeFieldType CreativeFieldType => CreativeFieldType.MultiFileUpload;

    public async Task<T?> Sanitize<T> (object? value) where T : class
    {
        return value is not IEnumerable<CreativeFileId> creativeFiles
            ? throw new ArgumentException(
                $"{nameof(value)} must be a type compatible with {typeof(IEnumerable<CreativeFileId>)}")
            : (T?)await _creativeFilesVerifier.GetExistingCreativeFilesFrom(creativeFiles.ToArray());
    }
}