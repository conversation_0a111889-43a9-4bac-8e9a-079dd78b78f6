﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates;

public sealed class CreativeTemplate : Entity<CreativeTemplateId>
{
    private CreativeTemplate (
        CreativeTemplateId id,
        CreativeTemplateName name,
        CreativeType creativeType,
        HashSet<CreativeTemplateCreativeField> creativeFields,
        bool archived,
        bool predefined,
        CreativeTemplateId? clonedFrom
    ) : base(id)
    {
        Name = name;
        CreativeType = creativeType;
        CreativeFields = creativeFields;
        Archived = archived;
        Predefined = predefined;
        ClonedFrom = clonedFrom;
    }

    /// <summary>
    /// Constructor for create creative templates from factory
    /// </summary>
    /// <param name="id"></param>
    /// <param name="name"></param>
    /// <param name="creativeType"></param>
    /// <param name="creativeFields"></param>
    /// <param name="isPredefined"></param>
    internal CreativeTemplate (
        CreativeTemplateId id,
        CreativeTemplateName name,
        CreativeType creativeType,
        HashSet<CreativeTemplateCreativeField> creativeFields,
        bool isPredefined
    ) : this(id, name, creativeType, creativeFields, false, isPredefined, null)
    {
    }

    public CreativeTemplateName Name { get; private set; }

    public CreativeType CreativeType { get; }

    public IReadOnlySet<CreativeTemplateCreativeField> CreativeFields { get; }

    public bool Archived { get; private set; }

    public bool Predefined { get; }

    public CreativeTemplateId? ClonedFrom { get; }

    public CreativeTemplateCreativeField? GetFieldByCreativeFieldId (CreativeFieldId? creativeFieldId)
    {
        return creativeFieldId is null ? null : CreativeFields.FirstOrDefault(c => c.Id == creativeFieldId);
    }

    public void EditName (CreativeTemplateName name)
    {
        Name = name;
    }

    public Result Archive ()
    {
        if (Predefined)
        {
            return Result.Fail(new PredefinedCreativeTemplateCannotBeArchivedError(Id));
        }

        Archived = true;
        return Result.Ok();
    }

    public void UnArchive ()
    {
        Archived = false;
    }

    public async Task<Result> CanBeRemoved (ICreativeTemplateInUseVerifier creativeTemplateInUseVerifier)
    {
        return await creativeTemplateInUseVerifier.IsTemplateInUse(Id)
            ? Result.Fail(new EntityInUseError(nameof(CreativeTemplate)))
            : Result.Ok();
    }

    public Result<CreativeTemplate> Clone (CreativeTemplateId newId, CreativeTemplateName newName, PredefinedCreativeTemplateUniqueTypeRequirement? predefinedCreativeTemplateUniqueTypeRequirement)
    {
        if (Predefined && !predefinedCreativeTemplateUniqueTypeRequirement!.IsSatisfied)
        {
            return Result.Fail(new PredefinedCreativeTemplateAlreadyExistsError(Id));
        }

        var clonedFields = new HashSet<CreativeTemplateCreativeField>(CreativeFields);

        return new CreativeTemplate(
            newId,
            newName,
            CreativeType,
            clonedFields,
            false,
            Predefined,
            Id
        );
    }
}