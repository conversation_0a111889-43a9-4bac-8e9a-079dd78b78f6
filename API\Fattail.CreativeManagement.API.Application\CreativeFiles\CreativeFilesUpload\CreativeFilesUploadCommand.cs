﻿using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;

public sealed record CreativeFilesUploadCommand (
        IReadOnlyList<CreativeFileToUpload> CreativeFilesToUpload
    )
    : IRequest<Result<IReadOnlyList<Result<CreativeFilesUploadResult>>>>;

public sealed record CreativeFileToUpload (
    string FileName,
    Stream Content);