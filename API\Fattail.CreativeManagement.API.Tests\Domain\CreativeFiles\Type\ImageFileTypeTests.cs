﻿using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;
using FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeFiles.Type;

[TestFixture]
public class ImageFileTypeTests
{
    [TestCase(".jpg")]
    [TestCase(".jpeg")]
    [TestCase(".png")]
    [TestCase(".gif")]
    [TestCase(".bmp")]
    [TestCase(".tiff")]
    public void Image_file_type_matches_image_extensions (string extension)
    {
        var fileExtension = FileExtension.From(extension);

        CreativeFileType.FromExtension(fileExtension).Should().Be(CreativeFileType.Image);
    }

    [Test]
    public void Image_file_type_has_dimensions_in_file_metadata_generation ()
    {
        //560 x 560 image
        const string Image =
            "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";

        using var stream = new MemoryStream(Convert.FromBase64String(Image));

        CreativeFileMetadata imageFileMetadata = CreativeFileType.Image.GenerateMetadata(stream);
        
        imageFileMetadata[ImageFileType.MetadataKeys.Width].Should().Be("560");
        imageFileMetadata[ImageFileType.MetadataKeys.Height].Should().Be("560");
    }
}
