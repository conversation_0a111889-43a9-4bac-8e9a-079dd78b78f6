using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateDelete;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2.Delete;

public class CreativeTemplateDeleteFunctionV2: BaseFunction
{
    public CreativeTemplateDeleteFunctionV2 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation("Creative Templates Delete v2", new[] { "v2" },
        Deprecated = true,
        Summary = "Deletes an existing creative template that is not referenced in any creative v2")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NoContent)]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound,
        Description = "Returned when the creative template does not exist.")]
    [OpenApiResponseWithoutBody(HttpStatusCode.Conflict,
        Description =
            "Returned when the creative template can't be deleted because it's currently in use by a creative.")]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeTemplateDeleteFunctionV2))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "delete", Route = "v2/creative-templates/{id:long}")]
        HttpRequestData req,
        long id)
    {
        var creativeTemplateDeleteCommand = new CreativeTemplateDeleteCommand(id);

        Result creativeTemplateDeleteResult = await _mediator.Send(creativeTemplateDeleteCommand);

        return await FromDeleteResult(req, creativeTemplateDeleteResult);
    }
}