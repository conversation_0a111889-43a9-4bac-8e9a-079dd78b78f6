﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.EditCreative;

public sealed class CreativeEditFunctionV3 : BaseFunction
{
    public CreativeEditFunctionV3 (
        IMediator mediator,
        IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Edit an existing creative v3", tags: new[] { "v3" },
        Summary = "Edit an existing creative v3")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeEditRequestV3),
        Description = "The creative to edit", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV3))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound, Description = "If the creative doesn't exist.")]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeEditFunctionV3))]
    public async Task<HttpResponseData> EditCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "v3/creatives/{id:long}")] HttpRequestData req,
        long id)
    {
        CreativeEditCommand creativeEditCommand = await FromRequest<CreativeEditRequestV3, CreativeEditCommand>(req, 
            new Dictionary<string, object>
        {
            { CreativeEditV3AutoMapperProfile.CreativeEditCommandIdParameterName, id }
        });

        Result<CreativeResult> result = await _mediator.Send(creativeEditCommand);

        HttpResponseData response =
            await FromResult<CreativeResult, CreativeResponseV3>(req, result);

        return response;
    }
}