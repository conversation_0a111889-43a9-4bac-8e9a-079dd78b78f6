﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields.Validators.RequiredValidators;

[TestFixture]
public class CollectionsRequiredValidatorTests
{
    private CreativeFieldIdentifier _creativeFieldIdentifier = null!;
    private static IReadOnlyList<IReadOnlyList<long>?> _invalidCollection = new List<IReadOnlyList<long>?> { new List<long>(), null };

    [SetUp]
    public void SetUp ()
    {
        var creativeFieldId = new CreativeFieldId(123456);
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;
        _creativeFieldIdentifier = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);
    }

    [Test]
    public async Task Collection_field_value_with_value_is_valid ()
    {
        IReadOnlyList<long> validCollection = new List<long> { 123, 32323, 43434 };
        var collectionsRequiredRuleValidator =
            new CollectionsRequiredRuleValidator<IReadOnlyList<long>, long>(_creativeFieldIdentifier);

        Result result = await collectionsRequiredRuleValidator.IsValid(validCollection);

        result.Should().BeSuccess();
    }

    [TestCaseSource(nameof(_invalidCollection))]
    public async Task Collection_field_value_with_no_value_is_invalid (IReadOnlyList<long> invalidCollection)
    {
        var collectionsRequiredRuleValidator =
            new CollectionsRequiredRuleValidator<IReadOnlyList<long>, long>(_creativeFieldIdentifier);

        Result result = await collectionsRequiredRuleValidator.IsValid(invalidCollection);

        result.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldIdentifier.Id.ToString()));
    }
}