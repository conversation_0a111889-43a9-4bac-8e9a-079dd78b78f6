using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class SingleSelectOptionFieldType : CreativeFieldType
{
    internal SingleSelectOptionFieldType () : base(nameof(CreativeFieldTypeEnum.SingleSelectOption), (int)CreativeFieldTypeEnum.SingleSelectOption)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return SelectCreativeFieldSettings.CreateFromSettings(settings, nameof(SingleSelectOptionFieldType))
            .ToResult<CreativeFieldSettings>(selectSettings => selectSettings);
    }
}
