using Fattail.CreativeManagement.API.Domain.Common.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;

public sealed class SelectCreativeFieldSettings : CreativeFieldSettings
{
    public SelectCreativeFieldSettings (IReadOnlyList<SelectOption> options)
    {
        Options = options;
    }

    public IReadOnlyList<SelectOption> Options { get; }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return Options;
    }

    public static Result<SelectCreativeFieldSettings> CreateFromSettings (IReadOnlyDictionary<string, object> settings, string fieldTypeName)
    {
        string optionsKey = nameof(Options).ToLower();

        if (!settings.TryGetValue(optionsKey, out object? optionsValue))
        {
            return Result.Fail(new RequiredValueMissingError(optionsKey, fieldTypeName));
        }

        if (optionsValue is not IEnumerable<SelectOption> selectOptions)
        {
            return Result.Fail(new InvalidValueError($"{optionsKey} must be an enumerable collection of SelectOption", fieldTypeName));
        }

        return new SelectCreativeFieldSettings(selectOptions.ToList().AsReadOnly());
    }
}
