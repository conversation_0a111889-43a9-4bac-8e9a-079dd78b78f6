﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Guards;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;

public sealed class CreativeFileStorageMetadata : ValueObject
{
    public CreativeFileStorageMetadata (string blobName, string location, DateTime uploadedDate)
    {
        Guard.Argument(location).NotNull().NotEmpty().NotWhiteSpace();
        Guard.Argument(blobName).NotNull().NotEmpty().NotWhiteSpace();
        Guard.Argument(uploadedDate, nameof(uploadedDate)).NotFutureDate();

        BlobName = blobName;
        Location = location;
        UploadedDate = uploadedDate;
    }

    public string BlobName { get; }
    public string Location { get; }
    public DateTime UploadedDate { get; }

    protected override IEnumerable<object> GetEqualityComponents ()
    {
        yield return BlobName;
        yield return Location;
        yield return UploadedDate;
    }
}