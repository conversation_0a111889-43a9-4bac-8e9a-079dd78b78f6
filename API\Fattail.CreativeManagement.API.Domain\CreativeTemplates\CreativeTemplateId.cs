﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates;

public sealed class CreativeTemplateId : ValueObject
{
    private readonly long _value;

    public CreativeTemplateId (long creativeTemplateId)
    {
        Guard.Argument(creativeTemplateId, nameof(creativeTemplateId)).Positive();

        _value = creativeTemplateId;
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return _value;
    }

    public override string ToString ()
    {
        return _value.ToString();
    }

    public static implicit operator long (CreativeTemplateId creativeTemplateId)
    {
        return creativeTemplateId._value;
    }

    public static implicit operator long? (CreativeTemplateId? creativeTemplateId)
    {
        return creativeTemplateId == null ? null : creativeTemplateId._value;
    }

    public static explicit operator CreativeTemplateId (long creativeTemplateId)
    {
        return new CreativeTemplateId(creativeTemplateId);
    }

    public static explicit operator CreativeTemplateId (string creativeTemplateId)
    {
        return new CreativeTemplateId(long.Parse(creativeTemplateId));
    }
}