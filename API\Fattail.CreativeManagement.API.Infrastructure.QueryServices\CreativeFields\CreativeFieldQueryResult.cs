using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

public record CreativeFieldQueryResult
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type
);

public sealed record SelectOptionQueryResult
(
    long Id,
    string Description
);

public sealed record MultiSelectCreativeFieldQueryResult
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<SelectOptionQueryResult> Options
) : CreativeFieldQueryResult(Id, Name, Type);

public sealed record SingleSelectCreativeFieldQueryResult
(
    long Id,
    string Name,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<SelectOptionQueryResult> Options
) : CreativeFieldQueryResult(Id, Name, Type);