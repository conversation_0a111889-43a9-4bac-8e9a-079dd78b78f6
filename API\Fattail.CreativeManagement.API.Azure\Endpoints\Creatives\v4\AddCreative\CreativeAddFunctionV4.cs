﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.AddCreative;

public sealed class CreativeAddFunctionV4 (
    IMediator mediator,
    IMapper mapper) : BaseFunction(mediator, mapper)
{
    [OpenApiOperation(
        "Add creative and assign it to a campaign v4", tags: new[] { "v4" },
        Summary = "Add a creative and assign it to a campaign v4")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeAddRequestV4),
        Description = "The creative to add and assign to the campaign", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeResponseV4))]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeAddFunctionV4))]
    public async Task<HttpResponseData> AddCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v4/creatives")]
        HttpRequestData req)
    {
        CreativeAddCommand creativeAddCommand = await FromRequest<CreativeAddRequestV4, CreativeAddCommand>(req);

        Result<CreativeResult> result = await _mediator.Send(creativeAddCommand);

        HttpResponseData response =
            await FromResultWithLocation<CreativeResult, CreativeResponseV4>(req, result, result.ValueOrDefault?.Id);

        return response;
    }
}