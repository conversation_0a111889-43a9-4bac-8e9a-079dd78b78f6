using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common.OpenApiModel;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure;

internal static class DependencyInjection
{
    internal static void AddAzureFunctions (this IServiceCollection services, HostBuilderContext hostBuilderContext)
    {
        services.AddSingleton((Func<IServiceProvider, IOpenApiConfigurationOptions>)(_ =>
        {
            var options = new OpenApiConfigurationOptions
            {
                Info = new OpenApiInfo
                {
                    Version = hostBuilderContext.Configuration.GetSection("CMS:ApiVersion").Get<string>(),
                    Title = "Creative Management Services",
                    Description = "Swagger API documentation"
                },
                OpenApiVersion = OpenApiVersionType.V3,
                DocumentFilters = new List<IDocumentFilter>()
                {
                    new CreativeFieldDocumentFilter()
                }
            };

            return options;
        }));

        services.AddAutoMapper(typeof(DependencyInjection));
    }
}