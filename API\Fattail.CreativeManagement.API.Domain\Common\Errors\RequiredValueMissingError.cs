﻿namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

internal sealed class RequiredValueMissingError : ErrorBase
{
    public RequiredValueMissingError (string fieldName, string entity) :
        base($"Required field '{fieldName}' is missing in '{entity}' entity.", ErrorType.RequiredValueMissing)
    {
        Metadata.Add(nameof(fieldName), fieldName);
        Metadata.Add(nameof(entity), entity);
    }
}