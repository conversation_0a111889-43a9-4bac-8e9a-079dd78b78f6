using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;

internal class CreativeQueryService : QueryService<Creative>
{
    public CreativeQueryService (ICosmosDbContainerFactory cosmosDbContainerFactory,
        IConfigurationProvider configurationProvider,
        IOrganizationContext organizationContext,
        IMapper mapper) :
        base(cosmosDbContainerFactory, configurationProvider, organizationContext, mapper)
    {
    }

    public override string ContainerName => "Creatives";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }
}