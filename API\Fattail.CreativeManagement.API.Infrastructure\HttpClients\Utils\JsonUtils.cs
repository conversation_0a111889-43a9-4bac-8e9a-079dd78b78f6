﻿using System.Text;

namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.Utils;

public class JsonUtils
{
    public static string ToSnakeCase (string s)
    {
        return ToSeparatedCase(s, '_');
    }

    private static string ToSeparatedCase (string s, char separator)
    {
        if (string.IsNullOrEmpty(s))
        {
            return s;
        }

        var sb = new StringBuilder();
        SeparatedCaseState state = SeparatedCaseState.Start;

        for (int i = 0; i < s.Length; i++)
        {
            if (s[i] == ' ')
            {
                if (state != SeparatedCaseState.Start)
                {
                    state = SeparatedCaseState.NewWord;
                }
            }
            else if (char.IsUpper(s[i]))
            {
                switch (state)
                {
                    case SeparatedCaseState.Upper:
                        bool hasNext = i + 1 < s.Length;
                        if (i > 0 && hasNext)
                        {
                            char nextChar = s[i + 1];
                            if (!char.IsUpper(nextChar) && nextChar != separator)
                            {
                                sb.Append(separator);
                            }
                        }

                        break;
                    case SeparatedCaseState.Lower:
                    case SeparatedCaseState.NewWord:
                        sb.Append(separator);
                        break;
                }

                char c;
                c = char.ToLowerInvariant(s[i]);
                sb.Append(c);

                state = SeparatedCaseState.Upper;
            }
            else if (s[i] == separator)
            {
                sb.Append(separator);
                state = SeparatedCaseState.Start;
            }
            else
            {
                if (state == SeparatedCaseState.NewWord)
                {
                    sb.Append(separator);
                }

                sb.Append(s[i]);
                state = SeparatedCaseState.Lower;
            }
        }

        return sb.ToString();
    }

    private enum SeparatedCaseState
    {
        Start,
        Lower,
        Upper,
        NewWord
    }
}