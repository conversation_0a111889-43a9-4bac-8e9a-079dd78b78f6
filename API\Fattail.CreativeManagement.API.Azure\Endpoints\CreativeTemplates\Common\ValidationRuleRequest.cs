﻿
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

internal sealed record ValidationRuleRequest (
    CreativeFieldValidationRuleType Type,
    IReadOnlyList<string> Options);

[JsonConverter(typeof (StringEnumConverter))]
public enum CreativeFieldValidationRuleType
{
    FileUploadExtensions,
    Required,
    FileSize
}