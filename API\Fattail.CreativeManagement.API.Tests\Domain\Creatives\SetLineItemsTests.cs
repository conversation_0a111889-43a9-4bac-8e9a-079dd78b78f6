﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
public class SetLineItemsTests : CreativeTestsBase
{
    [Test]
    public async Task Creative_cannot_set_line_items_assignments_without_line_items ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;

        HashSet<long>? expectedLineItemIds = null;
        
        Result<LineItemSetResult> creativeAssignLineItemsResult = creative.SetLineItemAssignments(expectedLineItemIds, UpdateInformation.Create("Custom Updater", DateTime.Now).Value);
        
        creativeAssignLineItemsResult.Should().BeFailure();
        creativeAssignLineItemsResult.Should().HaveReason(new RequiredValueMissingError("lineItemIds", nameof(Creative)));
    }
    
    [Test]
    public async Task Line_items_are_set_to_creative_successfully ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;
        
        var expectedLineItemIds = new HashSet<long>{ 1, 2, 3 };
        var oldLineItemIds = creative.LineItemIds.ToHashSet();
        
        Result<LineItemSetResult> creativeSetLineItemsResult = creative.SetLineItemAssignments(expectedLineItemIds, UpdateInformation.Create("Custom Updater", DateTime.Now).Value);
        
        creativeSetLineItemsResult.Should().BeSuccess();
        creative.LineItemIds.Should().BeEquivalentTo(expectedLineItemIds);
        creativeSetLineItemsResult.Value.AssignedLineItemIds.Should().BeEquivalentTo(expectedLineItemIds);
        creativeSetLineItemsResult.Value.UnassignedLineItemIds.Should().BeEquivalentTo(oldLineItemIds);
    }
}