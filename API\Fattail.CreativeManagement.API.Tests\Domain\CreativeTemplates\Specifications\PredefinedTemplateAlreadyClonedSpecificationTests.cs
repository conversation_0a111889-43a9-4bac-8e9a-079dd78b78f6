using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;
using FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Specifications;

[TestFixture]
public class PredefinedTemplateAlreadyClonedSpecificationTests
{
    [Test]
    public void Returns_true_when_template_type_matches_and_predefined_is_true()
    {
        var template = new CreativeTemplate(
            new CreativeTemplateId(1),
            (CreativeTemplateName)"Template1",
            CreativeType.Image,
            new HashSet<CreativeTemplateCreativeField>(),
            true // isPredefined
        );

        var spec = new PredefinedTemplateAlreadyClonedSpecification(CreativeType.Image);

        spec.IsSatisfiedBy(template).Should().BeTrue();
    }

    [Test]
    public void Returns_false_when_template_type_does_not_match_but_predefined_is_true()
    {
        var template = new CreativeTemplate(
            new CreativeTemplateId(1),
            (CreativeTemplateName)"Template1",
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            true // isPredefined
        );

        var spec = new PredefinedTemplateAlreadyClonedSpecification(CreativeType.Image);

        spec.IsSatisfiedBy(template).Should().BeFalse();
    }

    [Test]
    public void Returns_false_when_predefined_is_false_even_if_type_matches()
    {
        var template = new CreativeTemplate(
            new CreativeTemplateId(1),
            (CreativeTemplateName)"Template1",
            CreativeType.Image,
            new HashSet<CreativeTemplateCreativeField>(),
            false // isPredefined
        );

        var spec = new PredefinedTemplateAlreadyClonedSpecification(CreativeType.Image);

        spec.IsSatisfiedBy(template).Should().BeFalse();
    }

    [Test]
    public void Returns_false_when_type_and_predefined_do_not_match()
    {
        var template = new CreativeTemplate(
            new CreativeTemplateId(1),
            (CreativeTemplateName)"Template1",
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false // isPredefined
        );

        var spec = new PredefinedTemplateAlreadyClonedSpecification(CreativeType.Image);

        spec.IsSatisfiedBy(template).Should().BeFalse();
    }
}
