﻿using Microsoft.Azure.Cosmos;
using System.Net;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Tests.BaseResponse
{
    public class BaseTestResponse<T> : ItemResponse<T>
    {
        public BaseTestResponse (HttpStatusCode httpStatusCode)
        {
            StatusCode = httpStatusCode;
        }

        public BaseTestResponse (HttpStatusCode httpStatusCode, T resource)
        {
            StatusCode = httpStatusCode;
            Resource = resource;
        }

        public override T Resource { get; }

        public override HttpStatusCode StatusCode { get; }
    }
}
