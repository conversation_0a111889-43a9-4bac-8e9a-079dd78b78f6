﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;

public class FileSizeRuleValidator : IRuleValidator<IReadOnlyList<CreativeFile?>>
{
    private readonly double _allowedFileSize;
    private readonly string _creativeFieldId;

    //TODO: Remove the dependency on ICreativeFilesManager and use a ther new file size property of the creative file
    private readonly ICreativeFilesManager _creativeFilesManager;

    public FileSizeRuleValidator (IEnumerable<ValidationRule> validationRules,
        ICreativeFilesManager creativeFilesManager, CreativeFieldIdentifier creativeFieldIdentifier)
    {
        ValidationRule fileSizeValidationRule = validationRules.Single(validationRule =>
            validationRule.Type == CreativeFieldValidationRuleType.FileSize);
        _creativeFilesManager = creativeFilesManager;
        _creativeFieldId = creativeFieldIdentifier.Id.ToString();
        string fileSize = fileSizeValidationRule.Options.Select(option => option).Single();

        _allowedFileSize = double.Parse(fileSize);
    }

    private async Task<bool> CreativeFileSizeIsValid (CreativeFile creativeFile)
    {
        double creativeFileSize = await _creativeFilesManager.GetFileSizeInMegabytes(creativeFile);

        return creativeFileSize <= _allowedFileSize;
    }

    public async Task<Result> IsValid (IReadOnlyList<CreativeFile?> creativeFiles)
    {
        var result = Result.Ok();

        if (creativeFiles != null && creativeFiles.Any() && _allowedFileSize > 0)
        {
            foreach (CreativeFile? creativeFile in creativeFiles)
            {
                if (creativeFile != null && !await CreativeFileSizeIsValid(creativeFile)) 
                { 
                    result = Result.Merge(result, Result.Fail(new FileSizeValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldId, _allowedFileSize.ToString())));
                    break;
                } 
            }
        }

        return result;
    }
}