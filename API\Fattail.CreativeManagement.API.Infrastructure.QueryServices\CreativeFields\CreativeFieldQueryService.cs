using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;

internal sealed class CreativeFieldQueryService : QueryService<CreativeField>
{
    public CreativeFieldQueryService (ICosmosDbContainerFactory cosmosDbContainerFactory,
        IConfigurationProvider configurationProvider,
        IOrganizationContext organizationContext,
        IMapper mapper) :
        base(cosmosDbContainerFactory, configurationProvider, organizationContext, mapper)
    {
    }

    public override string ContainerName => "CreativeFields";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }
}