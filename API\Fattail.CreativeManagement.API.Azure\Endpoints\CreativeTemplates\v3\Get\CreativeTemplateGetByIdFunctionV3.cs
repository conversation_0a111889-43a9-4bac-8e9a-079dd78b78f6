﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.GetById;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v3.Get;

[ExcludeFromCodeCoverage]
public class CreativeTemplateGetByIdFunctionV3 : BaseFunction
{
    public CreativeTemplateGetByIdFunctionV3 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation("Creative Template Get By Id v3", tags: new[] { "v3" },
        Summary = "Get creative template by Id v3")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeTemplateResponse))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound,
        Description = "Returned when the creative template does not exist.")]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeTemplateGetByIdFunctionV3))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "v3/creative-templates/{id:long}")]
        HttpRequestData req,
        long id)
    {
        var getByIdQuery = new CreativeTemplateGetByIdQuery(id.ToString());
        
        CreativeTemplateQueryResult? getByIdResult = await _mediator.Send(getByIdQuery);
        
        return await FromSingleQueryResult<CreativeTemplateQueryResult, CreativeTemplateResponse>(req, getByIdResult);

    }
}