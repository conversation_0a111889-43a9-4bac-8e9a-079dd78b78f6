﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class MultiFileUploadFieldValue : CreativeFieldValue<IReadOnlyList<CreativeFileId>>
{
    private MultiFileUploadFieldValue (CreativeFieldIdentifier creativeFieldIdentifier,
        IEnumerable<CreativeFileId>? value)
        : base(creativeFieldIdentifier, value?.ToList() ?? new List<CreativeFileId>())
    {
    }

    internal static async Task<Result<MultiFileUploadFieldValue>> Create (
        ISanitizerStrategyProvider? sanitizerStrategyProvider,
        CreativeFieldIdentifier creativeFieldIdentifier, object? value, IEnumerable<ValidationRule> validationRules,
        ICreativeFilesManager? creativeFilesManager)
    {
        Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
            .FieldTypeIs(CreativeFieldType.MultiFileUpload);

        IReadOnlyList<CreativeFile>? creativeFiles = null;

        if (value is not null)
        {
            Guard.Argument(value, nameof(value)).Compatible<IEnumerable<CreativeFileId>>();

            Guard.Argument(sanitizerStrategyProvider, nameof(sanitizerStrategyProvider)).NotNull(
                "Sanitizer factory must be provided in order to create a multi file upload field with value");

            creativeFiles = await sanitizerStrategyProvider!
                .GetFrom(CreativeFieldType.MultiFileUpload).Sanitize<IReadOnlyList<CreativeFile>>(value);
        }

        Result validationResults = await CreativeFieldValueValidationManager.Validate(creativeFiles, validationRules,
            creativeFieldIdentifier, creativeFilesManager);

        return validationResults.IsFailed
            ? validationResults
            : new MultiFileUploadFieldValue(creativeFieldIdentifier,
                creativeFiles?.Select(creativeFile => creativeFile.Id));
    }

    public override async Task<Result<CreativeFieldValue>> GenerateNewValue (object? value,
        IEnumerable<ValidationRule> validationRules,
        ISanitizerStrategyProvider? sanitizerStrategyProvider = null,
        ICreativeFilesManager? creativeFilesManager = null)
    {
        return (await Create(sanitizerStrategyProvider, CreativeFieldIdentifier, value, validationRules,
                creativeFilesManager))
            .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
    }
}