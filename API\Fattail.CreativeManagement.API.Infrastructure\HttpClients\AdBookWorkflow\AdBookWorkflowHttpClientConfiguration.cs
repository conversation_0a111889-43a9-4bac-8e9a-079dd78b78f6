﻿namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow;

public sealed class AdBookWorkflowHttpClientConfiguration
{
    internal string? AdBookWorkflowBaseUri { private get; set; }
    internal string? AdBookEwScrippsWorkflowBaseUri { private get; set; } 
    private const long EwScrippsOrgId = 400710;

    public string GetAdBookWorkflowBaseUri (long organizationId)
    {
        if (organizationId == EwScrippsOrgId)
        {
            return AdBookEwScrippsWorkflowBaseUri;
        }

        long adBookInstanceNumber = organizationId / 100000;
        return string.Format(AdBookWorkflowBaseUri!, adBookInstanceNumber);
    }
}
