using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives.GetById;

internal class CreativeGetByIdQueryHandler : IRequestHandler<CreativeGetByIdQuery, CreativeQueryResult?>
{
    private readonly CreativeQueryService _creativesQueryService;

    public CreativeGetByIdQueryHandler (CreativeQueryService creativesQueryService)
    {
        _creativesQueryService = creativesQueryService;
    }

    public async Task<CreativeQueryResult?> Handle (CreativeGetByIdQuery request, CancellationToken cancellationToken)
    {
        return await _creativesQueryService.FindByIdAsync<CreativeQueryResult>(request.Id);
    }
}