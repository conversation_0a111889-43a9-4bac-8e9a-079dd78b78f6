﻿using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateEdit;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativesTemplateEdit;

[TestFixture]
public class CreativeTemplateEditHandlerTests
{
    [SetUp]
    public void SetUp()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(1234);

        _creativeTemplateRepository = new Mock<ICreativeTemplateRepository>();

        _creativeTemplateEditHandler =
            new CreativeTemplateEditHandler(_creativeTemplateRepository.Object);

        _creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
    }

    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeTemplateRepository> _creativeTemplateRepository = null!;
    private CreativeTemplateEditHandler _creativeTemplateEditHandler = null!;
    private CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = null!;

    [Test]
    public async Task Creative_template_is_updated()
    {
        string name = "New Template Name";
        long id = 2;

        var creativeTemplateEditCommand = new CreativeTemplateEditCommand(id, name, false);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            CreativeTemplateName.Create("Old template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(1), 1),
                new(new(2), 2)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new (1), "Creative field 1", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new (2), "Creative field 2", CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value
            ]),
            false
        );
        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        var creativeTemplateUpdateResult = new CreativeTemplateResult
        {
            Id = id,
            Name = name,
            CreativeType = CreativeType.Undefined,
            CreativeFields = new List<CreativeTemplateCreativeFieldResult>()
            {
                new(1, "Creative field 1", 1, CreativeFieldTypeEnum.FileUpload, new List <ValidationRuleResult>(), null),
                new(2, "Creative field 2", 2, CreativeFieldTypeEnum.MultiFileUpload, new List <ValidationRuleResult>(), null)
            }
        };

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()))
            .ReturnsAsync(creativeTemplateUpdateResult);

        Result<CreativeTemplateResult> result =
            await _creativeTemplateEditHandler.Handle(creativeTemplateEditCommand, CancellationToken.None);

        result.Should().BeSuccess();
        result.Value.Should().NotBeNull();
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeTemplateRepository.Verify(c => c.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()),
            Times.Once);
    }

    [Test]
    public async Task Creative_template_can_be_archived ()
    {
        long id = 2;

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            CreativeTemplateName.Create("Old template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(1), 1),
                new(new(2), 2)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new (1), "Creative field 1", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new (2), "Creative field 2", CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value
            ]),
            false
        );
        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()))
            .ReturnsAsync((CreativeTemplate ct) => new CreativeTemplateResult
            {
                Id = ct.Id,
                Name = ct.Name,
                CreativeType = ct.CreativeType,
                Archive = ct.Archived,
                CreativeFields = ct.CreativeFields.Select(cf => new CreativeTemplateCreativeFieldResult(
                    cf.Id,
                    cf.Name,
                    cf.DisplayOrder,
                    cf.Type.EnumType,
                    cf.ValidationRules.Select(vr => new ValidationRuleResult { Type = vr.Type, Options = vr.Options }).ToList(),
                    null)).ToList()
            });

        string name = "New Template Name";
        var creativeTemplateEditCommand = new CreativeTemplateEditCommand(id, name, true);

        Result<CreativeTemplateResult> result =
            await _creativeTemplateEditHandler.Handle(creativeTemplateEditCommand, CancellationToken.None);

        result.Should().BeSuccess();
        result.Value.Should().NotBeNull();
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeTemplateRepository.Verify(c => c.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()),
            Times.Once);
        result.Value.Archive.Should().BeTrue();
    }

    [Test]
    public async Task Creative_template_can_be_unarchived ()
    {
        string name = "New Template Name";
        long id = 2;

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            CreativeTemplateName.Create("Old template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [
                new(new(1), 1),
                new(new(2), 2)
            ],
            new HashSet<CreativeField>([
                CreativeField.Create(new (1), "Creative field 1", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value,
                CreativeField.Create(new (2), "Creative field 2", CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value
            ]),
            false
        );
        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;
        creativeTemplate.Archive();

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.FindByIdAsync(It.IsAny<CreativeTemplateId>()))
            .ReturnsAsync(creativeTemplate);

        var creativeTemplateEditCommand = new CreativeTemplateEditCommand(id, name, false);

        _creativeTemplateRepository.Setup(creativeTemplateRepo =>
                creativeTemplateRepo.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()))
            .ReturnsAsync((CreativeTemplate ct) => new CreativeTemplateResult
            {
                Id = ct.Id,
                Name = ct.Name,
                CreativeType = ct.CreativeType,
                Archive = ct.Archived,
                CreativeFields = ct.CreativeFields.Select(cf => new CreativeTemplateCreativeFieldResult(
                    cf.Id,
                    cf.Name,
                    cf.DisplayOrder,
                    cf.Type.EnumType,
                    cf.ValidationRules.Select(vr => new ValidationRuleResult { Type = vr.Type, Options = vr.Options }).ToList(),
                    null)).ToList()
            });

        Result<CreativeTemplateResult> result =
            await _creativeTemplateEditHandler.Handle(creativeTemplateEditCommand, CancellationToken.None);

        result.Should().BeSuccess();
        result.Value.Should().NotBeNull();
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeTemplateRepository.Verify(c => c.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()),
            Times.Once);
        result.Value.Archive.Should().BeFalse();
    }

    [Test]
    public async Task Creative_template_not_found ()
    {
        string creativeTemplate = "Existing Creative Template name";
        long id = 123;
        var creativeTemplateEditCommand = new CreativeTemplateEditCommand(id, creativeTemplate, false);

        Result<CreativeTemplateResult> result =
            await _creativeTemplateEditHandler.Handle(creativeTemplateEditCommand, CancellationToken.None);

        result.Should().BeFailure().And.HaveReason(new EntityNotFoundError(id.ToString(), nameof(CreativeTemplate)));
        _creativeTemplateRepository.Verify(c => c.FindByIdAsync(It.IsAny<CreativeTemplateId>()),
            Times.Once);
        _creativeTemplateRepository.Verify(c => c.UpdateAsync<CreativeTemplateResult>(It.IsAny<CreativeTemplate>()),
            Times.Never);
    }
}
