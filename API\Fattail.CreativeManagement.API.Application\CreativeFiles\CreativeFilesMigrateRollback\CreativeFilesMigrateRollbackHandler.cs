﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrateRollback;

public class CreativeFilesMigrateRollbackHandler : IRequestHandler<CreativeFilesMigrateRollbackCommand, Result>
{
    private readonly ICreativeFileRepository _creativeFileRepository;
    private readonly ICreativeFileStorageManager _creativeFileStorageManager;
    private readonly int _maxDegreeOfParallelism = 200;

    public CreativeFilesMigrateRollbackHandler (
        ICreativeFileRepository creativeFileRepository,
        ICreativeFileStorageManager creativeFileStorageManager)
    {
        _creativeFileRepository = creativeFileRepository;
        _creativeFileStorageManager = creativeFileStorageManager;
    }

    public async Task<Result> Handle (CreativeFilesMigrateRollbackCommand request,
        CancellationToken cancellationToken)
    {
        switch (request.RollbackAction)
        {
            case RollbackAction.Full:
                return Result.Merge(await _creativeFileRepository.DeletePartitionAsync(),
                    await _creativeFileStorageManager.DeleteOrganizationVirtualFolder());
            case RollbackAction.Partial:
                IReadOnlyList<CreativeFile> migratedCreativeFiles = await _creativeFileRepository.FindMigratedAsync<CreativeFile>();

                await Parallel.ForEachAsync(migratedCreativeFiles, new ParallelOptions { MaxDegreeOfParallelism = _maxDegreeOfParallelism }, async (creativeFile, _) =>
                {
                    creativeFile.PrepareToDelete();
                    await _creativeFileRepository.UpdateAsync<CreativeFile>(creativeFile);
                });

                return Result.Ok();
        }

        return Result.Fail(new Error("Invalid rollback action"));
    }
}