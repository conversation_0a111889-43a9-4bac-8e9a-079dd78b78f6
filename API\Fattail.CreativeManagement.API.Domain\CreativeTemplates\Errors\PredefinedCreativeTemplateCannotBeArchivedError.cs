using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public sealed class PredefinedCreativeTemplateCannotBeArchivedError : ErrorBase
{
    public PredefinedCreativeTemplateCannotBeArchivedError (CreativeTemplateId predefinedCreativeTemplateId) :
        base("Predefined creative templates cannot be archived", ErrorType.PredefinedTemplateCannotBeArchived)
    {
        Metadata.Add("entity", nameof(CreativeTemplate));
        Metadata.Add("PredefinedCreativeTemplateId", predefinedCreativeTemplateId);
    }
}
