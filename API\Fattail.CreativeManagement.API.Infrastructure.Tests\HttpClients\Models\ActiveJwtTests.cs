﻿using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Models;
using FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Infrastructure.Tests.HttpClients.Models
{
    [TestFixture(Category = "Infrastructure - Http Clients", TestName = "ActiveJwt")]
    public class ActiveJwtTests
    {
        [Test]
        public void Active_jwt_is_not_created_with_empty_token ()
        {
            FluentActions.Invoking(() => new ActiveJwt("", 100)).Should().Throw<ArgumentException>();
        }

        [Test]
        public void Active_jwt_is_created_with_non_empty_token ()
        {
            var activeJwt = new ActiveJwt("abcde", 100);
            activeJwt.Should().NotBeNull();
        }

        [Test]
        public void Active_jwt_is_created_with_right_values ()
        {
            const string Token = "ABCDEFG";

            var activeJwt = new ActiveJwt(Token, 100);

            activeJwt.AccessToken.Should().Be(Token);
        }

        [Test]
        public void Active_jwt_is_expired_When_expires_in_less_60_seconds ()
        {
            var activeJwt = new ActiveJwt("abcdef", 30);

            bool isExpired = activeJwt.IsExpired();

            isExpired.Should().BeTrue();
        }
    }
}
