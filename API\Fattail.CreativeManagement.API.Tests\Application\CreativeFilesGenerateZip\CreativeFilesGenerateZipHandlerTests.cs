﻿using AutoBogus;
using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using Moq;
using NUnit.Framework;
using System.Reflection;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeFilesGenerateZip;

[TestFixture]
public class CreativeFilesGenerateZipHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _creativeFilesZipGeneratorMock = new Mock<ICreativeFilesZipGenerator>();
        
        _creativeFileRepositoryMock = new Mock<ICreativeFileRepository>();

        _creativeFilesGenerateZipHandler = new CreativeFilesGenerateZipHandler(
            _creativeFilesZipGeneratorMock.Object,
            _creativeFileRepositoryMock.Object,
            _mapper);
    }
    
    private readonly IMapper _mapper =
        new Mapper(new MapperConfiguration(cfg => cfg.AddMaps(Assembly.GetAssembly(typeof(DependencyInjection)))));

    private Mock<ICreativeFilesZipGenerator> _creativeFilesZipGeneratorMock = null!;
    private Mock<ICreativeFileRepository> _creativeFileRepositoryMock = null!;
    private CreativeFilesGenerateZipHandler _creativeFilesGenerateZipHandler = null!;
    
    
    
    [Test]
    public async Task Creative_files_to_download_list_success ()
    {
        List<CreativeFileId> validCreativeFieldIds = new AutoFaker<CreativeFileId>()
            .CustomInstantiator(faker => new CreativeFileId(faker.Random.Number(1, 100)))
            .Generate(10);
        
        var creativeFilesGenerateZipCommand = new CreativeFilesGenerateZipCommand(validCreativeFieldIds);
        
        List<CreativeFile>? validCreativeFileList = new AutoFaker<CreativeFile>()
            .RuleFor(c=> c.Name,
                faker => CreativeFileName.From(faker.System.FileName()))
            .Generate(10);

        _creativeFileRepositoryMock.Setup(c =>
                c.FindManyByIdAsync<CreativeFile>(validCreativeFieldIds))
            .ReturnsAsync(validCreativeFileList);
        
        _creativeFilesZipGeneratorMock.Setup(c =>
                c.GenerateTemporalCreativeFileZip(validCreativeFileList))
            .ReturnsAsync(new CreativeFilesGenerateZipResult("https://azurestorage.test"));
        
        Result<CreativeFilesGenerateZipResult> creativeFileGenerateZipResult = await _creativeFilesGenerateZipHandler.Handle(creativeFilesGenerateZipCommand, CancellationToken.None);

        creativeFileGenerateZipResult.Should().NotBeNull();
        creativeFileGenerateZipResult.IsSuccess.Should().BeTrue();
        creativeFileGenerateZipResult.Value.Should().NotBeNull();
        creativeFileGenerateZipResult.Value.Location.Should().NotBeNullOrEmpty();
    }
    
    [Test]
    public async Task Invalid_creative_files_to_download_return_error ()
    { 
        List<CreativeFileId> invalidCreativeFieldIds = new AutoFaker<CreativeFileId>()
            .CustomInstantiator(faker => new CreativeFileId(faker.Random.Number(1, 100)))
            .Generate(5);
        
        var creativeFilesGenerateZipCommand = new CreativeFilesGenerateZipCommand(invalidCreativeFieldIds);

        var invalidCreativeFileStorageMetadataList = new List<CreativeFile>();
        
        _creativeFileRepositoryMock.Setup(c =>
                c.FindManyByIdAsync<CreativeFile>(invalidCreativeFieldIds))
            .ReturnsAsync(invalidCreativeFileStorageMetadataList);
        
        Result<CreativeFilesGenerateZipResult> creativeFileGenerateZipResult = await _creativeFilesGenerateZipHandler.Handle(creativeFilesGenerateZipCommand, CancellationToken.None);

        creativeFileGenerateZipResult.Should().NotBeNull();
        creativeFileGenerateZipResult.IsSuccess.Should().BeFalse();
        creativeFileGenerateZipResult.Errors.Should().ContainSingle();
        creativeFileGenerateZipResult.Errors.First().Should().BeOfType<CreativeFilesToDownloadAreInvalid>();
    }
    
    [Test]
    public async Task Fail_when_generating_zip_returns_error ()
    {
        List<CreativeFileId> validCreativeFieldIds = new AutoFaker<CreativeFileId>()
            .CustomInstantiator(faker => new CreativeFileId(faker.Random.Number(1, 100)))
            .Generate(5);

        var creativeFilesGenerateZipCommand = new CreativeFilesGenerateZipCommand(validCreativeFieldIds);

        List<CreativeFile>? validCreativeFileList = new AutoFaker<CreativeFile>()
            .RuleFor(c=> c.Name,
                faker => CreativeFileName.From(faker.System.FileName()))
            .Generate(5);

        _creativeFileRepositoryMock.Setup(c =>
                c.FindManyByIdAsync<CreativeFile>(validCreativeFieldIds))
            .ReturnsAsync(validCreativeFileList);

        _creativeFilesZipGeneratorMock.Setup(c =>
                c.GenerateTemporalCreativeFileZip(It.IsAny<IReadOnlyList<CreativeFile>>()))
            .ReturnsAsync(Result.Fail(new CreativeFilesToDownloadError()));

        Result<CreativeFilesGenerateZipResult> creativeFileGenerateZipResult = await _creativeFilesGenerateZipHandler.Handle(creativeFilesGenerateZipCommand, CancellationToken.None);

        creativeFileGenerateZipResult.Should().NotBeNull();
        creativeFileGenerateZipResult.IsSuccess.Should().BeFalse();
        creativeFileGenerateZipResult.Errors.Should().ContainSingle();
        creativeFileGenerateZipResult.Errors.First().Should().BeOfType<CreativeFilesToDownloadError>();
    }
}