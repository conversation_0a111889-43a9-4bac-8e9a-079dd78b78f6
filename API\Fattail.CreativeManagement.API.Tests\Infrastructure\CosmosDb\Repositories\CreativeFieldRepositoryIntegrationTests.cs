using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Specifications;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Infrastructure.CosmosDb.Repositories;

[TestFixture]
public class CreativeFieldRepositoryIntegrationTests
{
    [Test]
    public async Task FindAsync_WithCreativeFieldNameInUseSpecification_ShouldNotThrowCosmosDbException ()
    {
        // This test verifies that the Cosmos DB LINQ exception with "Default" NodeType is resolved
        // The exception was: Microsoft.Azure.Cosmos.Linq.DocumentQueryException: Expression with NodeType 'Default' is not supported.

        // Arrange
        var mockRepository = new MockCreativeFieldRepository();
        var specification = new CreativeFieldNameInUseSpecification("Test Field", CreativeFieldType.SingleLineText);

        // Act & Assert
        // This should not throw a Cosmos DB LINQ exception
        var result = await mockRepository.FindAsync(specification);

        // The result can be null (no matching field found) - that's expected behavior
        // The important thing is that no exception is thrown
        result.Should().BeNull();
    }

    [Test]
    public async Task FindAsync_WithCreativeFieldNameInUseSpecification_WhenFieldExists_ShouldReturnField ()
    {
        // Arrange
        var mockRepository = new MockCreativeFieldRepository();
        var existingField = CreativeField.Create(
            new CreativeFieldId(1),
            "Test Field",
            CreativeFieldType.SingleLineText,
            new CreativeFieldUniqueNameRequirement(true),
            false,
            null,
            null).Value;

        mockRepository.AddField(existingField);

        var specification = new CreativeFieldNameInUseSpecification("Test Field", CreativeFieldType.SingleLineText);

        // Act
        var result = await mockRepository.FindAsync(specification);

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be("Test Field");
        result.Type.Should().Be(CreativeFieldType.SingleLineText);
    }
}

// Mock repository for testing without actual Cosmos DB dependency
public class MockCreativeFieldRepository : ICreativeFieldRepository
{
    private readonly List<CreativeField> _fields = new();

    public void AddField (CreativeField field)
    {
        _fields.Add(field);
    }

    public Task<CreativeField?> FindByIdAsync (CreativeFieldId id)
    {
        var field = _fields.FirstOrDefault(f => f.Id.Equals(id));
        return Task.FromResult(field);
    }

    public Task<TResult?> FindByIdAsync<TResult> (CreativeFieldId id)
    {
        throw new NotImplementedException();
    }

    public Task<CreativeField?> FindAsync (System.Linq.Expressions.Expression<Func<CreativeField, bool>> predicate)
    {
        var compiledPredicate = predicate.Compile();
        var field = _fields.FirstOrDefault(compiledPredicate);
        return Task.FromResult(field);
    }

    public Task<CreativeField?> FindAsync (Specification<CreativeField> specification)
    {
        var predicate = specification.ToExpression().Compile();
        var field = _fields.FirstOrDefault(predicate);
        return Task.FromResult(field);
    }

    public Task<IReadOnlyList<CreativeField>> FindManyAsync (System.Linq.Expressions.Expression<Func<CreativeField, bool>> predicate)
    {
        var compiledPredicate = predicate.Compile();
        var fields = _fields.Where(compiledPredicate).ToList();
        return Task.FromResult<IReadOnlyList<CreativeField>>(fields);
    }

    public Task<IReadOnlyList<TResult>> FindManyByIdAsync<TResult> (IEnumerable<CreativeFieldId> ids)
    {
        throw new NotImplementedException();
    }

    public Task CreateAsync (CreativeField entity)
    {
        _fields.Add(entity);
        return Task.CompletedTask;
    }

    public Task<TResult> CreateAsync<TResult> (CreativeField entity)
    {
        _fields.Add(entity);
        throw new NotImplementedException();
    }

    public Task<TResult> MigrateAsync<TResult> (CreativeField entity)
    {
        throw new NotImplementedException();
    }

    public Task<TResult> UpdateAsync<TResult> (CreativeField entity)
    {
        var existingIndex = _fields.FindIndex(f => f.Id.Equals(entity.Id));
        if (existingIndex >= 0)
        {
            _fields[existingIndex] = entity;
        }
        throw new NotImplementedException();
    }

    public Task<Result> DeleteAsync (CreativeFieldId id)
    {
        _fields.RemoveAll(f => f.Id.Equals(id));
        return Task.FromResult(Result.Ok());
    }

    public Task<IReadOnlyList<CreativeField>> GetAllAsync ()
    {
        return Task.FromResult<IReadOnlyList<CreativeField>>(_fields);
    }
}
