﻿using Fattail.CreativeManagement.API.Azure.Common;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using System.Diagnostics.CodeAnalysis;
using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Health;

[ExcludeFromCodeCoverage]
public class HealthCheckGetFunction : BaseFunction
{
    public HealthCheckGetFunction (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [Function("HealthCheckGetFunction")]
    [OpenApiOperation("Health Check Get Function", tags: new[] { "v3", "v4" },
        Summary = "Check the health of the API")]
    [OpenApiResponseWithoutBody(HttpStatusCode.NoContent, Description = "Returned when the health endpoint is successfully accessed.")]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "health")]
        HttpRequestData req)
    {
        return req.CreateResponse(HttpStatusCode.NoContent);
    }
}