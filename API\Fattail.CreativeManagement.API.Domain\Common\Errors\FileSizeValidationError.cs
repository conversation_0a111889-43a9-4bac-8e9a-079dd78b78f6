﻿namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

public class FileSizeValidationError : ErrorBase
{
    internal FileSizeValidationError (string fieldValue, string entity, string creativeFieldId, string allowedFileSize) : base(
        $"Custom file size validation failed for '{fieldValue}' in '{entity}' entity.",
        ErrorType.FileSizeValidationError)
    {
        Metadata.Add(nameof(fieldValue), fieldValue);
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(allowedFileSize), allowedFileSize);
    }
}