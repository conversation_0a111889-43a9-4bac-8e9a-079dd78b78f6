﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using Moq;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class MultiFileUploadFieldValueTests
{
    [SetUp]
    public void SetUp ()
    {
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider => sanitizerStrategyProvider.GetFrom(CreativeFieldType.MultiFileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeFilesUploadPolicyMock = new Mock<ICreativeFilesUploadPolicy>();
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.MaxSizeInMegabytesAllowed).Returns(1000);
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.AllowedExtensions).Returns(new HashSet<string>(StringComparer.FromComparison(StringComparison.OrdinalIgnoreCase)) { ".txt", ".pdf" });

        _idManagerMock = new Mock<IIdManager>();
    }

    private Mock<ICreativeFilesUploadPolicy> _creativeFilesUploadPolicyMock = null!;
    private Mock<IIdManager> _idManagerMock = null!;

    private static readonly CreativeFieldType[] _invalidCreativeFieldTypes =
        CreativeFieldType.List
        .Where(creativeFieldType => creativeFieldType != CreativeFieldType.MultiFileUpload)
        .ToArray();

    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    private Mock<ISanitizer> _sanitizerMock = null!;

    [Test]
    public async Task Multi_file_upload_field_value_can_be_created ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiFileUpload);
        var creativeFiles = new List<CreativeFileId> { new(1234), new(5678) };

        var fileValues = new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.txt") };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object?>()))
            .ReturnsAsync(fileValues);

        MultiFileUploadFieldValue multiFileUploadFieldValue =
            (await MultiFileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, creativeFiles, new List<ValidationRule>(), null)).Value;

        multiFileUploadFieldValue.CreativeFieldIdentifier.Should().Be(creativeField);
        multiFileUploadFieldValue.Value.Should().BeEquivalentTo(creativeFiles);
    }

    [Test]
    public async Task Multi_file_upload_field_value_can_be_created_with_custom_validation ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiFileUpload);
        var creativeFiles = new List<CreativeFileId> { new(1234), new(5678) };

        var fileValues = new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.txt") };
        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.FileUploadExtensions, new List<string> {".txt"}, creativeField.Id).Value
        };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object?>()))
            .ReturnsAsync(fileValues);

        MultiFileUploadFieldValue multiFileUploadFieldValue =
            (await MultiFileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, creativeFiles, validationRules, null)).Value;

        multiFileUploadFieldValue.CreativeFieldIdentifier.Should().Be(creativeField);
        multiFileUploadFieldValue.Value.Should().BeEquivalentTo(creativeFiles);
    }

    [Test]
    public async Task Multi_file_upload_field_value_cant_be_created_with_custom_validation_failed ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiFileUpload);
        var creativeFiles = new List<CreativeFileId> { new(1234), new(5678) };

        var fileValues = new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.txt") };
        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.FileUploadExtensions, new List<string> {".pdf"}, creativeField.Id).Value
        };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object?>()))
            .ReturnsAsync(fileValues);

        FluentResults.Result<MultiFileUploadFieldValue> multiFileUploadFieldValue =
            await MultiFileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, creativeFiles, validationRules, null);

        multiFileUploadFieldValue.IsFailed.Should().BeTrue();
    }

    [Test]
    public async Task Multi_file_upload_field_value_can_be_created_without_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiFileUpload);

        var multiFileUploadFieldValueWithoutValue =
            (MultiFileUploadFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        multiFileUploadFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);
        multiFileUploadFieldValueWithoutValue.Value.Should().BeEmpty();
    }

    [Test]
    public async Task Multi_file_upload_field_value_creation_with_invalid_value_type_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiFileUpload);

        await Invoking(() =>
                MultiFileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, "invalid value", null, null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [TestCaseSource(nameof(_invalidCreativeFieldTypes))]
    public async Task Multi_file_upload_field_value_creation_with_invalid_creative_field_type_fails (
        CreativeFieldType invalidCreativeFieldType)
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), invalidCreativeFieldType);

        await Invoking(() =>
                MultiFileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField,
                    new List<CreativeFileId>(), null, null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [Test]
    public async Task Multi_file_upload_field_value_generates_new_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.MultiFileUpload);

        var multiFileUploadFieldValueWithoutValue =
            (MultiFileUploadFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        var creativeFiles = new List<CreativeFileId> { new(1234), new(5678) };

        var fileValues = new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.txt") };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object?>()))
            .ReturnsAsync(fileValues);

        var multiFileUploadFieldValueWithNewValue =
            (MultiFileUploadFieldValue)(await multiFileUploadFieldValueWithoutValue.GenerateNewValue(creativeFiles, new List<ValidationRule>(), _sanitizerStrategyProviderMock.Object)).Value;

        multiFileUploadFieldValueWithoutValue.Value.Should().BeEmpty();
        multiFileUploadFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);

        multiFileUploadFieldValueWithNewValue.CreativeFieldIdentifier.Should().Be(creativeField);
        multiFileUploadFieldValueWithNewValue.Value.Should().BeEquivalentTo(creativeFiles);
    }

    private CreativeFile GetCreativeFile (long id, string fileName)
    {
        var creativeFileName = CreativeFileName.From(fileName);

        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(id);

        return CreativeFile.PrepareToUpload(_creativeFilesUploadPolicyMock.Object,
            _idManagerMock.Object, creativeFileName, Stream.Null).Value;
    }
}