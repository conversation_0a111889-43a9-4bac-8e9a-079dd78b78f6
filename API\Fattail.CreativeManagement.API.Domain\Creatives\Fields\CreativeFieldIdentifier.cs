﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class CreativeFieldIdentifier : ValueObject
{
    private CreativeFieldIdentifier (CreativeFieldId id, CreativeFieldType type)
    {
        Id = id;
        Type = type;
    }

    public CreativeFieldId Id { get; }

    public CreativeFieldType Type { get; }

    public static CreativeFieldIdentifier Create (CreativeFieldId id, CreativeFieldType type)
    {
        Guard.Argument(id, nameof(id)).NotNull();

        return new CreativeFieldIdentifier(id, type);
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return Id;
        yield return Type;
    }
}