﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.AddCreative;

public sealed class CreativeAddFunctionV3 : BaseFunction
{
    public CreativeAddFunctionV3 (
        IMediator mediator,
        IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Add creative and assign it to a campaign v3", tags: new[] { "v3" },
        Summary = "Add a creative and assign it to a campaign v3")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeAddRequestV3),
        Description = "The creative to add and assign to the campaign", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeResponseV3))]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeAddFunctionV3))]
    public async Task<HttpResponseData> AddCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v3/creatives")]
        HttpRequestData req)
    {
        CreativeAddCommand creativeAddCommand = await FromRequest<CreativeAddRequestV3, CreativeAddCommand>(req);

        Result<CreativeResult> result = await _mediator.Send(creativeAddCommand);

        HttpResponseData response =
            await FromResultWithLocation<CreativeResult, CreativeResponseV3>(req, result, result.ValueOrDefault?.Id);

        return response;
    }
}