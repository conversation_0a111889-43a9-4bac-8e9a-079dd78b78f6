<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions">
      <HintPath>..\..\..\..\..\..\..\usr\local\share\dotnet\shared\Microsoft.AspNetCore.App\7.0.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="SkiaSharp" Version="3.116.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure.CosmosDb\Fattail.CreativeManagement.API.Infrastructure.CosmosDb.csproj" />
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Infrastructure\Fattail.CreativeManagement.API.Infrastructure.csproj" />
  </ItemGroup>
</Project>