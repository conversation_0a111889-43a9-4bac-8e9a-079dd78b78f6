﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields;

public class CreativeField : Entity<CreativeFieldId>
{
    protected CreativeField (CreativeFieldId id, string name, CreativeFieldType type, bool predefined, string? omsExternalIdentifier, CreativeFieldSettings settings) : base(id)
    {
        Name = name;
        Type = type;
        Predefined = predefined;
        OmsExternalIdentifier = omsExternalIdentifier;
        Settings = settings;
    }

    public string Name { get; }

    public CreativeFieldType Type { get; }

    public bool Predefined { get; }

    public string? OmsExternalIdentifier { get; }

    public CreativeFieldSettings Settings { get; }

    public static Result<CreativeField> Create (CreativeFieldId id, string? name, CreativeFieldType type, CreativeFieldUniqueNameRequirement creativeFieldUniqueNameRequirement, bool predefined, string? omsExternalIdentifier, IReadOnlyDictionary<string, object>? settings)
    {
        var result = new Result();

        if (type == CreativeFieldType.None)
        {
            result.WithError(new RequiredValueMissingError("creative field type", nameof(CreativeField)));
        }

        if (string.IsNullOrWhiteSpace(name))
        {
            result.WithError(new RequiredValueMissingError("creative field name", nameof(CreativeField)));
        }

        if (!creativeFieldUniqueNameRequirement.IsSatisfied)
        {
            result.WithError(new CreativeFieldNameInUseError(name, type.ToString(), nameof(CreativeField)));
        }

        Result<CreativeFieldSettings> settingsResult = type.CreateSettings(settings ?? new Dictionary<string, object>());

        result = Result.Merge(result, settingsResult.ToResult());

        return result.IsSuccess ? new CreativeField(id, name!, type, predefined, omsExternalIdentifier, settingsResult.Value) : result;
    }
}