﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;

public sealed class MigratedCreativeFileIsUpdatedError : ErrorBase
{
    public MigratedCreativeFileIsUpdatedError (long adBookAdId, long adBookClientId, long campaignId)
        : base($"An error has occurred while deleting the creative file with Ad Id: {adBookAdId}, Ad Book Client Id: {adBookClientId} and Campaign Id: {campaignId}", 
            ErrorType.MigratedCreativeWasUpdatedError)
    {
        Metadata.Add(nameof(adBookAdId), adBookAdId);
        Metadata.Add(nameof(adBookClientId), adBookClientId);
        Metadata.Add(nameof(campaignId), campaignId);
    }
}