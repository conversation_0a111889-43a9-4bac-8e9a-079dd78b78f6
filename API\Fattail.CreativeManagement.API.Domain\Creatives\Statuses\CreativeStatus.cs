﻿using Ardalis.SmartEnum;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Statuses;

public abstract class CreativeStatus (string name, int value) : SmartEnum<CreativeStatus>(name, value)
{
    public static readonly CreativeStatus PendingApproval = new PendingApproval();
    public static readonly CreativeStatus Approved = new Approved();

    public CreativeStatusEnum EnumType { get; } = (CreativeStatusEnum)value;

    public abstract string Description { get; }

    public abstract bool CanTransitionTo (CreativeStatus creativeStatus);
}
