﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateEdit;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2.Edit;

internal sealed class CreativeTemplateEditAutoMapperProfile : Profile
{
    internal const string CreativeTemplateEditCommandIdParameterName = "Id";

    public CreativeTemplateEditAutoMapperProfile ()
    {
        CreateMap<CreativeTemplateEditRequest, CreativeTemplateEditCommand>()
            .ForCtorParam(CreativeTemplateEditCommandIdParameterName,
                paramOptions =>
                    paramOptions.MapFrom((src, context) => context.Items[CreativeTemplateEditCommandIdParameterName]));
    }
}