﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.EditCreative;

internal sealed class CreativeEditV2AutoMapperProfile : Profile
{
    internal const string CreativeEditCommandIdParameterName = "Id";

    public CreativeEditV2AutoMapperProfile ()
    {
        CreateMap<CreativeEditRequestV2, CreativeEditCommand>()
            .ForCtorParam(CreativeEditCommandIdParameterName, paramOptions => paramOptions.MapFrom((src, context) => context.Items[CreativeEditCommandIdParameterName]));
    }
}