﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;

public sealed class CreativeFile : Entity<CreativeFileId>
{
    private CreativeFile (CreativeFileId id, CreativeFileName name, FileSize size, CreativeFileType type, CreativeFileMetadata metadata)
        : base(id)
    {
        Name = name;
        Size = size;
        Metadata = metadata;
        Type = type;
        ToBeDeleted = false;
    }

    public CreativeFileName Name { get; }

    public FileSize Size { get; }

    public CreativeFileType Type { get; }

    public CreativeFileMetadata Metadata { get; }

    public CreativeFileStorageMetadata? StorageMetadata { get; private set; }
    public bool ToBeDeleted { get; private set; }

    public void PromoteToUploaded (CreativeFileStorageMetadata storageMetadata)
    {
        StorageMetadata = storageMetadata;
    }

    public static Result<CreativeFile> PrepareToUpload (ICreativeFilesUploadPolicy creativeFilesUploadPolicy,
        IIdManager idManager, CreativeFileName name, Stream stream)
    {
        var result = new Result();

        if (string.IsNullOrWhiteSpace(name.NameWithoutExtension))
        {
            result.WithError(new RequiredValueMissingError("creative file name", nameof(CreativeFile)));
        }

        var assetSize = FileSize.From(stream);
        if (assetSize.SizeInMegaBytes > creativeFilesUploadPolicy.MaxSizeInMegabytesAllowed)
        {
            result.WithError(new CreativeFileSizeExceededError(name.Name, creativeFilesUploadPolicy.MaxSizeInMegabytesAllowed));
        }

        if (result.IsFailed)
        {
            return result;
        }

        var fileType = CreativeFileType.FromExtension(name.Extension);

        return new CreativeFile(new CreativeFileId(idManager.GetId()), name, assetSize, fileType, fileType.GenerateMetadata(stream));
    }
    
    public static Result CanBeMigrated (ICreativeFilesUploadPolicy creativeFilesMigrationPolicy, CreativeFileName name)
    {
        var result = new Result();

        if (string.IsNullOrWhiteSpace(name.NameWithoutExtension))
        {
            result.WithError(new RequiredValueMissingError("creative file name", nameof(CreativeFile)));
        }
        
        if(!creativeFilesMigrationPolicy.AllowedExtensions.Contains(name.Extension))
        {
            result.WithError(new CreativeFileExtensionNotAllowedError(name.Name, creativeFilesMigrationPolicy.AllowedExtensions));
        }

        return result;
    }

    public void PrepareToDelete ()
    {
        ToBeDeleted = true;
    }
}