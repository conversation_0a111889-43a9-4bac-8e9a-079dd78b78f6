﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
public class UpdateInformationTests
{
    [TestCase(null)]
    [TestCase("")]
    [TestCase("    ")]
    public void Update_information_cannot_be_created_without_updater_name (string? updaterName)
    {
        Result<UpdateInformation> lastApprovalResult = UpdateInformation.Create(
            updaterName,
            DateTime.UtcNow
        );

        lastApprovalResult.Should().BeFailure()
            .And.HaveReason<RequiredValueMissingError>(null);
    }

    [Test]
    public void Update_information_cannot_be_created_without_update_date_time ()
    {
        Result<UpdateInformation> lastApprovalResult = UpdateInformation.Create(
            "Updater Name",
            null
        );

        lastApprovalResult.Should().BeFailure()
            .And.HaveReason<RequiredValueMissingError>(null);
    }

    [Test]
    public void Update_information_can_be_created ()
    {
        var updateDateTime = DateTime.UtcNow;
        var updaterName = "Updater Name";

        Result<UpdateInformation> createUpdateInformationResult = UpdateInformation.Create(
            updaterName,
            updateDateTime
        );

        createUpdateInformationResult.Should().BeSuccess();

        UpdateInformation updateInformation = createUpdateInformationResult.Value;

        updateInformation.UpdaterName.Should().Be(updaterName);
        updateInformation.UpdateDateTime.Should().Be(updateDateTime);
    }
}