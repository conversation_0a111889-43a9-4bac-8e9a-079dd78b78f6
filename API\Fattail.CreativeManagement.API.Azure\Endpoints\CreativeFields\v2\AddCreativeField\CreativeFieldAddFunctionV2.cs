﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;
using Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;
using Fattail.CreativeManagement.API.Application.CreativeFields;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.v2.AddCreativeField;

public class CreativeFieldAddFunctionV2 : BaseFunction
{
    public CreativeFieldAddFunctionV2 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Add a creative field v2", 
        tags: new[] { "v2" },
        Deprecated = true,
        Summary = "Add a creative field v2")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeFieldAddRequest),
        Description = "The creative field to add", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeFieldResponse))]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeFieldAddFunctionV2))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v2/creative-fields")]
        HttpRequestData req)
    {
        CreativeFieldAddCommand creativeAddCommand =
            await FromRequest<CreativeFieldAddRequest, CreativeFieldAddCommand>(req);

        Result<CreativeFieldResult> result = await _mediator.Send(creativeAddCommand);

        HttpResponseData response =
            await FromResultWithLocation<CreativeFieldResult, CreativeFieldResponse>(req, result,
                result.ValueOrDefault?.Id);

        return response;
    }
}