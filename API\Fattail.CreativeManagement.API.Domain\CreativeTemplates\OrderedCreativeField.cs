using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using System.Collections.ObjectModel;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates;

public record DisplayOrderCreativeField
{
    public DisplayOrderCreativeField(CreativeFieldId Id, int? DisplayOrder, IReadOnlyDictionary<SpecialPropertyKey, object?>? SpecialProperties = null)
    {
        this.Id = Id;
        this.DisplayOrder = DisplayOrder;
        this.SpecialProperties = SpecialProperties ?? new ReadOnlyDictionary<SpecialPropertyKey, object?>(new Dictionary<SpecialPropertyKey, object?>());
    }

    public CreativeFieldId Id { get; }
    public int? DisplayOrder { get; }
    public IReadOnlyDictionary<SpecialPropertyKey, object?> SpecialProperties { get; }
}