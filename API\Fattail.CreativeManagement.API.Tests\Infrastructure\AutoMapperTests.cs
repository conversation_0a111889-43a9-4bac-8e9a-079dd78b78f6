﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;
using Fattail.CreativeManagement.API.Azure;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Infrastructure;

[TestFixture]
public class AutoMapperTests
{
    [Test]
    public void Auto_mapper_configuration_is_valid ()
    {
        var configuration = new MapperConfiguration(cfg =>
        {
            cfg.AddMaps(typeof(ICosmosDbContainer),
                typeof(CreativeAddCommand),
                typeof(FunctionExecutorHostBuilderExtensions),
                typeof(ODataQuery));
        });

        configuration.AssertConfigurationIsValid();
    }
}