﻿using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
[Category("Creative status")]
public class ApproveCreativeTests : CreativeTestsBase
{
    [Test]
    public async Task Creative_can_be_approved_from_pending_approval ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _,
            string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;
        ApprovalInformation approvalInformation = ApprovalInformation.Create("approver", DateTime.UtcNow).Value;

        Result approvalResult = creative.Approve(approvalInformation);

        approvalResult.Should().BeSuccess();
        creative.Status.Should().Be(CreativeStatus.Approved);
        creative.LastApproval.Should().Be(approvalInformation);
    }

    [Test]
    public async Task Creative_cannot_be_approved_twice ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _,
            string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;
        ApprovalInformation approvalInformation = ApprovalInformation.Create("approver", DateTime.UtcNow).Value;
        
        creative.Approve(approvalInformation);
        Result approvalResult = creative.Approve(approvalInformation);

        approvalResult.Should().BeFailure().And.HaveReason<CreativeInvalidStatusTransitionError>(null);
    }
}