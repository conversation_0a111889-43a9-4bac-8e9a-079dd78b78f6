﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.24.1" />
    <PackageReference Include="FluentResults" Version="3.16.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.6" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="SkiaSharp" Version="3.116.1" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="Yitter.IdGenerator" Version="1.0.15" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fattail.CreativeManagement.API.Application\Fattail.CreativeManagement.API.Application.csproj" />
  </ItemGroup>
</Project>