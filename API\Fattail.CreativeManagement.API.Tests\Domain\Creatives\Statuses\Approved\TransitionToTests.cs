﻿using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Statuses.Approved;

[TestFixture]
[Category("Creative status")]
public class TransitionToTests
{
    private static readonly List<CreativeStatus> _invalidStatuses = CreativeStatus.List.Where(status => status != CreativeStatus.PendingApproval).ToList();

    [TestCaseSource(nameof(_invalidStatuses))]
    public void Pending_approval_status_cannot_transition_to_invalid_statuses(CreativeStatus creativeStatus)
    {
        CreativeStatus approved = CreativeStatus.Approved;

        bool canTransition = approved.CanTransitionTo(creativeStatus);

        canTransition.Should().BeFalse();
    }
}
