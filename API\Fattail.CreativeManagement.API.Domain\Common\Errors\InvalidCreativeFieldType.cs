namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

public class InvalidCreativeFieldType : ErrorBase
{
    public InvalidCreativeFieldType (string fieldName, string entity) :
        base($"Invalid creative field '{fieldName}' is missing in '{entity}' entity.", ErrorType.RequiredValueMissing)
    {
        Metadata.Add(nameof(fieldName), fieldName);
        Metadata.Add(nameof(entity), entity);
    }
}