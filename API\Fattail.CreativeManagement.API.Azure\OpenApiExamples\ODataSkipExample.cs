﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Resolvers;
using Newtonsoft.Json.Serialization;

namespace Fattail.CreativeManagement.API.Azure.OpenApiExamples;

public class ODataSkipExample : OpenApiExample<int>
{
    public override IOpenApiExample<int> Build (NamingStrategy namingStrategy = null)
    {
        Examples.Add(OpenApiExampleResolver.Resolve("$skip",
            10,
            namingStrategy));
        return this;
    }
}