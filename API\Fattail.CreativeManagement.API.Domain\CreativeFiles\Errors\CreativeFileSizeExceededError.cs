﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;

internal sealed class CreativeFileSizeExceededError : ErrorBase
{
    public CreativeFileSizeExceededError (string creativeFileName, long maxSizeInMegabytes)
        : base(
            $"File size exceeded for file: '{creativeFileName}'. Max size for upload file should be less or equal than {maxSizeInMegabytes} MB.",
            ErrorType.CreativeFileSizeExceededError)
    {
        Metadata.Add(nameof(creativeFileName), creativeFileName);
        Metadata.Add(nameof(maxSizeInMegabytes), maxSizeInMegabytes);
    }
}