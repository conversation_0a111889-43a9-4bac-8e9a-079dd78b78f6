﻿namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;

public class CosmosDbSettings
{
    /// <summary>
    ///     CosmosDb Account - The Azure Cosmos DB endpoint
    /// </summary>
    public string EndpointUrl { get; set; }

    /// <summary>
    ///     Key - The primary key for the Azure DocumentDB account.
    /// </summary>
    public string PrimaryKey { get; set; }

    /// <summary>
    ///     Database name
    /// </summary>
    public string DatabaseName { get; set; }

    /// <summary>
    ///     List of containers in the database
    /// </summary>
    public ContainerInfo[] Containers { get; set; }

    /// <summary>
    ///     Test environment flag
    /// </summary>
    public bool IsDevOrTestEnvironment { get; set; }
}