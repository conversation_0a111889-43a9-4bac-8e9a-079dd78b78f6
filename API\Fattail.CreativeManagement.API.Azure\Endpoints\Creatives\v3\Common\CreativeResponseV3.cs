using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;

internal sealed record CreativeResponseV3
{
    [property: OpenApiProperty(Description = "Creative id")]
    public long Id { get; init; }

    [property: OpenApiProperty(Description = "Creative template id")]
    public long CreativeTemplateId { get; init; }

    [property: OpenApiProperty(Description = "Creative template name")]
    public string CreativeTemplateName { get; init; }

    [property: OpenApiProperty(Description = "Creative name")]
    public string? Name { get; init; }

    [property: OpenApiProperty(Description = "AdBook Client id")]
    public long? AdBookClientId { get; init; }

    [property: OpenApiProperty(Description = "Campaign id")]
    public long CampaignId { get; init; }
    
    [property: OpenApiProperty(Description = "Line item ids")]
    public IReadOnlyList<long> LineItemIds { get; init; }

    [property: OpenApiProperty(Description = "Creative fields")]
    public IReadOnlyList<CreativeFieldValueResponse> Fields { get; init; }

    [property: OpenApiProperty(Description = "Last time the creative was updated")]
    public DateTime LastUpdatedOn { get; init; }

    [property: OpenApiProperty(Description = "Creative updated by")]
    public string? LastUpdatedBy { get; init; }
}