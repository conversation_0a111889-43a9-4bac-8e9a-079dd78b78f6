﻿using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DataPopulationModels;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DevAndTestEnvDataPopulations
{
    [ExcludeFromCodeCoverage]
    public class DevAndTestEnvDataPopulationService : IDevAndTestEnvDataPopulationService
    {
#pragma warning disable 1998
        public async Task<ICollection<DataPopulationResponseModel>> RunDevAndTestDataPopulations (ICollection<DataPopulationModel> dataPopulationModels = null)
#pragma warning restore 1998
        {

            return new List<DataPopulationResponseModel>
            {
                new DataPopulationResponseModel
                {
                    Message = "No Dev and Test Data Populations for Creative Management Service"
                }
            };
            //If Dev and Test Data Populations are needed:
            //remove the above line and
            //remove the pragma around public async Task...
            //add something like - results.AddRange(await MarketDataPopulation.Run(marketRepository));
            //See documentation for more details: https://fattail.atlassian.net/wiki/spaces/CCPP/pages/1915584533/Data+Populations
        }
    }
}
