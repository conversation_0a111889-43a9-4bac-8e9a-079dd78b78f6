﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

public class CreativeFieldValidationRulePolicy
{
    private readonly CreativeFieldType _creativeFieldType;

    public CreativeFieldValidationRulePolicy (CreativeFieldType creativeFieldType)
    {
        _creativeFieldType = creativeFieldType;
    }

    public bool Supports (CreativeFieldValidationRuleType validationRuleType)
    {
        return (_creativeFieldType.EnumType, validationRuleType) switch
        {
            (CreativeFieldTypeEnum.SingleLineText, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldTypeEnum.MultiSelectOption, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldTypeEnum.SingleSelectOption, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldTypeEnum.MultiLineText, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldTypeEnum.MultiFileUpload, CreativeFieldValidationRuleType.FileUploadExtensions) => true,
            (CreativeFieldTypeEnum.MultiFileUpload, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldTypeEnum.MultiFileUpload, CreativeFieldValidationRuleType.FileSize) => true,
            (CreativeFieldTypeEnum.FileUpload, CreativeFieldValidationRuleType.FileUploadExtensions) => true,
            (CreativeFieldTypeEnum.FileUpload, CreativeFieldValidationRuleType.Required) => true,
            (CreativeFieldTypeEnum.FileUpload, CreativeFieldValidationRuleType.FileSize) => true,
            _ => false
        };
    }
}