﻿namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;

public abstract record ODataQuery
{
    public string? Filter { get; init; }
    public string? OrderBy { get; init; }
    public string? Top { get; init; }
    public string? Skip { get; init; }

    public void Deconstruct (out string? filter, out string? orderBy, out string? top, out string? skip)
    {
        filter = Filter;
        orderBy = OrderBy;
        top = Top;
        skip = Skip;
    }
}