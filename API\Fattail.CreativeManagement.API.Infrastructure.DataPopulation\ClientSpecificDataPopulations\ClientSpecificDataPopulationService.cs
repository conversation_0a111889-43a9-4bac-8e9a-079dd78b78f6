﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DataPopulationModels;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations
{
    [ExcludeFromCodeCoverage]
    public class ClientSpecificDataPopulationService : IClientSpecificDataPopulationService
    {
        private readonly IDataPopulationRepository<CreativeField> _creativeFieldItemResponseRepository;
        private readonly IDataPopulationRepository<CreativeTemplate> _creativeTemplateItemResponseRepository;

        public ClientSpecificDataPopulationService (
            IDataPopulationRepository<CreativeField> creativeFieldItemResponseRepository,
            IDataPopulationRepository<CreativeTemplate> creativeTemplateItemResponseRepository)
        {
            _creativeFieldItemResponseRepository = creativeFieldItemResponseRepository;
            _creativeTemplateItemResponseRepository = creativeTemplateItemResponseRepository;
        }

        public async Task<ICollection<DataPopulationResponseModel>> RunAllClientSpecificDataPopulations (ICollection<DataPopulationModel> dataPopulationModels = null)
        {
            var results = new List<DataPopulationResponseModel>();

            results.AddRange(await CreativeFieldDataPopulation.RunAsync(_creativeFieldItemResponseRepository));
            
            results.AddRange(await MigrationCreativeFieldDataPopulation.RunAsync(_creativeFieldItemResponseRepository));
            
            results.AddRange(await MigrationCreativeTemplateDataPopulation.RunAsync(_creativeTemplateItemResponseRepository));

            return results;
        }
    }
}
