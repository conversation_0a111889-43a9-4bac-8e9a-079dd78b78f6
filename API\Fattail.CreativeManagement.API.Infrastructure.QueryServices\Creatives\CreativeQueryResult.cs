﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;

public sealed record CreativeQueryResult
{
    public long Id { get; init; }
    public string CreativeTemplateId { get; init; }
    public string CreativeTemplateName { get; init; }
    public string Name { get; init; }
    public string? AdBookClientId { get; init; }
    public string CampaignId { get; init; }
    public IReadOnlyList<long> LineItemIds { get; init; }
    public IReadOnlyList<CreativeFieldValueResult> Fields { get; init; }
    public CreativeStatusResult Status { get; set; }
    public string LastUpdatedBy { get; init; }
    public DateTime LastUpdatedOn { get; init; }
    public ApprovalInformationResult LastApproval { get; set; }
}

public sealed record ApprovalInformationResult (string ApproverName, DateTime ApprovalDateTime);

public sealed record CreativeStatusResult (CreativeStatusEnum Value, string Description);

public abstract record CreativeFieldValueResult(long Id, string Name, CreativeFieldTypeEnum Type);

public abstract record CreativeFieldValueResult<TValue>(long Id, string Name, CreativeFieldTypeEnum Type, TValue Value)
    : CreativeFieldValueResult(Id, Name, Type);

public sealed record CreativeFileValueResult(
    long Id, 
    string Name, 
    string Extension, 
    string Location, 
    long Size,
    Dictionary<string, string> Metadata);

public sealed record FileUploadFieldValueResult(long Id, string Name, CreativeFileValueResult Value)
    : CreativeFieldValueResult<CreativeFileValueResult>(Id, Name, CreativeFieldTypeEnum.FileUpload, Value);

public sealed record MultiFileUploadFieldValueResult(long Id, string Name,
        IReadOnlyList<CreativeFileValueResult> Value)
    : CreativeFieldValueResult<IReadOnlyList<CreativeFileValueResult>>(Id, Name, CreativeFieldTypeEnum.MultiFileUpload,
        Value);

public sealed record SingleLineTextFieldValueResult(long Id, string Name, string Value)
    : CreativeFieldValueResult<string>(Id, Name, CreativeFieldTypeEnum.SingleLineText, Value);

public sealed record MultiSelectOptionFieldValueResult (long Id, string Name, IReadOnlyList<long> Value)
    : CreativeFieldValueResult<IReadOnlyList<long>>(Id, Name, CreativeFieldTypeEnum.MultiSelectOption, Value);

public sealed record SingleSelectOptionFieldValueResult(long Id, string Name, long? Value)
    : CreativeFieldValueResult<long?>(Id, Name, CreativeFieldTypeEnum.SingleSelectOption, Value);

public sealed record MultiLineTextFieldValueResult(long Id, string Name, string Value)
    : CreativeFieldValueResult<string>(Id, Name, CreativeFieldTypeEnum.MultiLineText, Value);