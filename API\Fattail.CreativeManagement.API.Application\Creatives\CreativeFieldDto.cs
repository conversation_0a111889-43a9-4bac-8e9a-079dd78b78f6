﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;

namespace Fattail.CreativeManagement.API.Application.Creatives;

public abstract record CreativeFieldDto (
    long Id,
    CreativeFieldTypeEnum Type)
{
    internal abstract object? GetValue ();
}

public abstract record CreativeFieldValue<TValue> (
    long Id,
    CreativeFieldTypeEnum Type,
    TValue Value) : CreativeFieldDto(Id, Type);

public sealed record MultiFileUploadFieldValue (
        long Id,
        IReadOnlyList<long> Value)
    : CreativeFieldValue<IReadOnlyList<long>>(Id, CreativeFieldTypeEnum.MultiFileUpload, Value)
{
    internal override object? GetValue ()
    {
        return Value.Select(creativeFileId => new CreativeFileId(creativeFileId));
    }
}

public sealed record FileUploadFieldValue (
        long Id,
        long Value)
    : CreativeFieldValue<long>(Id, CreativeFieldTypeEnum.FileUpload, Value)
{
    internal override object? GetValue ()
    {
        return Value > 0 ? (CreativeFileId)Value : null;
    }
}

public sealed record SingleLineTextFieldValue (
        long Id,
        string Value)
    : CreativeFieldValue<string>(Id, CreativeFieldTypeEnum.SingleLineText, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}

public sealed record MultiSelectOptionFieldValue (
        long Id,
        IReadOnlyList<long> Value)
    : CreativeFieldValue<IReadOnlyList<long>>(Id, CreativeFieldTypeEnum.MultiSelectOption, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}

public sealed record SingleSelectOptionFieldValue (
        long Id,
        long? Value)
    : CreativeFieldValue<long?>(Id, CreativeFieldTypeEnum.SingleSelectOption, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}

public sealed record MultiLineTextFieldValue (
        long Id,
        string Value)
    : CreativeFieldValue<string>(Id, CreativeFieldTypeEnum.MultiLineText, Value)
{
    internal override object? GetValue ()
    {
        return Value;
    }
}