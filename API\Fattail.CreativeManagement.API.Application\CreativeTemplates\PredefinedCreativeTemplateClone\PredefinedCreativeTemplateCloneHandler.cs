using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates.PredefinedCreativeTemplateClone;

public sealed class PredefinedCreativeTemplateCloneHandler (
    IIdManager idManager,
    IPredefinedCreativeTemplateRepository predefinedCreativeTemplateRepository,
    ICreativeTemplateRepository creativeTemplateRepository,
    IPredefinedCreativeFieldRepository predefinedCreativeFieldRepository,
    ICreativeFieldRepository creativeFieldRepository)
    : IRequestHandler<PredefinedCreativeTemplateCloneCommand, Result<CreativeTemplateResult>>
{
    public async Task<Result<CreativeTemplateResult>> Handle (
        PredefinedCreativeTemplateCloneCommand request,
        CancellationToken cancellationToken)
    {
        CreativeTemplate? predefinedTemplate = await predefinedCreativeTemplateRepository
            .FindByIdAsync(new CreativeTemplateId(request.PredefinedTemplateId));

        if (predefinedTemplate == null)
        {
            return Result.Fail(new EntityNotFoundError(request.PredefinedTemplateId.ToString(), nameof(CreativeTemplate)));
        }

        var creativeTemplateNameInUseSpecification = new CreativeTemplateNameInUseSpecification(null, request.Name);
        var creativeTemplateUniqueNameRequirement = new CreativeTemplateUniqueNameRequirement(await creativeTemplateRepository.FindAsync(creativeTemplateNameInUseSpecification) == null);
        Result<CreativeTemplateName> templateNameResult = CreativeTemplateName.Create(request.Name, creativeTemplateUniqueNameRequirement);

        if (templateNameResult.IsFailed)
        {
            return templateNameResult.ToResult();
        }

        await ClonePredefinedCreativeFields(predefinedTemplate.CreativeFields);

        var alreadyClonedSpec = new PredefinedTemplateAlreadyClonedSpecification(predefinedTemplate.CreativeType);

        var predefinedCreativeTemplateUniqueTypeRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(await creativeTemplateRepository.FindAsync(alreadyClonedSpec) == null);

        Result<CreativeTemplate> clonedTemplateResult = predefinedTemplate.Clone(
            new CreativeTemplateId(idManager.GetId()),
            templateNameResult.Value,
            predefinedCreativeTemplateUniqueTypeRequirement);

        if (clonedTemplateResult.IsFailed)
        {
            return clonedTemplateResult.ToResult();
        }

        return await creativeTemplateRepository.CreateAsync<CreativeTemplateResult>(clonedTemplateResult.Value);
    }

    private async Task ClonePredefinedCreativeFields (IReadOnlySet<CreativeTemplateCreativeField> templateFields)
    {
        foreach (CreativeTemplateCreativeField templateField in templateFields)
        {
            CreativeField? existingOrgField = await creativeFieldRepository.FindByIdAsync(templateField.Id);

            if (existingOrgField is not null)
            {
                continue;
            }

            CreativeField predefinedField = (await predefinedCreativeFieldRepository.FindByIdAsync(templateField.Id))!;

            await creativeFieldRepository.CreateAsync<CreativeFieldResult>(predefinedField);
        }
    }
}
