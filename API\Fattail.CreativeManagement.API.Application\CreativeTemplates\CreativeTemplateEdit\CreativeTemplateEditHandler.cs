﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateEdit;

public class CreativeTemplateEditHandler(ICreativeTemplateRepository creativeTemplateRepository)
    : IRequestHandler<CreativeTemplateEditCommand, Result<CreativeTemplateResult>>
{
    public async Task<Result<CreativeTemplateResult>> Handle(CreativeTemplateEditCommand request,
        CancellationToken cancellationToken)
    {
        CreativeTemplate? creativeTemplate = await creativeTemplateRepository.FindByIdAsync(new CreativeTemplateId(request.Id));

        if (creativeTemplate is null)
        {
            return Result.Fail(new EntityNotFoundError(request.Id.ToString(), nameof(CreativeTemplate)));
        }

        var creativeTemplateNameInUseSpecification = new CreativeTemplateNameInUseSpecification(creativeTemplate, request.Name);
        var creativeTemplateUniqueNameRequirement = new CreativeTemplateUniqueNameRequirement(await creativeTemplateRepository.FindAsync(creativeTemplateNameInUseSpecification) == null);

        Result<CreativeTemplateName> creativeTemplateNameCreateResult = CreativeTemplateName.Create(request.Name, creativeTemplateUniqueNameRequirement);

        if (creativeTemplateNameCreateResult.IsFailed)
        {
            return creativeTemplateNameCreateResult.ToResult();
        }

        creativeTemplate.EditName(creativeTemplateNameCreateResult.Value);

        if (request.Archive.HasValue)
        {
            if (request.Archive.Value)
            {
                Result archiveResult = creativeTemplate.Archive();

                if (archiveResult.IsFailed)
                {
                    return archiveResult;
                }
            }
            else
            {
                creativeTemplate.UnArchive();
            }
        }

        return await creativeTemplateRepository.UpdateAsync<CreativeTemplateResult>(creativeTemplate);
    }
}