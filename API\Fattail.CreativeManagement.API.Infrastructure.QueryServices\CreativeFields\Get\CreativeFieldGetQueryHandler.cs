using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields.Get;

internal class CreativeFieldGetQueryHandler : IRequestHandler<CreativeFieldGetQuery, QueryResult<CreativeFieldQueryResult>>
{
    private readonly CreativeFieldQueryService _creativeFieldsQueryService;

    public CreativeFieldGetQueryHandler (CreativeFieldQueryService creativeFieldsQueryService)
    {
        _creativeFieldsQueryService = creativeFieldsQueryService;
    }

    public async Task<QueryResult<CreativeFieldQueryResult>> Handle (CreativeFieldGetQuery request,
        CancellationToken cancellationToken)
    {
        return await _creativeFieldsQueryService.GetFrom<CreativeFieldQueryResult>(request);
    }
}