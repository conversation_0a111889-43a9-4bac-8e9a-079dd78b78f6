using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields.GetById;

internal sealed class CreativeFieldGetByIdQueryHandler : IRequestHandler<CreativeFieldGetByIdQuery, CreativeFieldQueryResult?>
{
    private readonly CreativeFieldQueryService _creativeFieldsQueryService;

    public CreativeFieldGetByIdQueryHandler (CreativeFieldQueryService creativeFieldsQueryService)
    {
        _creativeFieldsQueryService = creativeFieldsQueryService;
    }
    
    public async Task<CreativeFieldQueryResult?> Handle (CreativeFieldGetByIdQuery request, CancellationToken cancellationToken)
    {
        return await _creativeFieldsQueryService.FindByIdAsync<CreativeFieldQueryResult>(request.Id);
    }
}