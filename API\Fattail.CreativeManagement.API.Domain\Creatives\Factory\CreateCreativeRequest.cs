﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Factory;

public record CreateCreativeRequest(
    long Id,
    string? Name, 
    CreativeTemplate? CreativeTemplate, 
    long? AdBookClientId, 
    long? AdBookAdId, 
    long CampaignId, 
    HashSet<long>? LineItemIds,
    string? UpdatedBy, 
    IReadOnlyDictionary<CreativeFieldId, object?> InitialFieldValues,
    DateTime CreatedDate);