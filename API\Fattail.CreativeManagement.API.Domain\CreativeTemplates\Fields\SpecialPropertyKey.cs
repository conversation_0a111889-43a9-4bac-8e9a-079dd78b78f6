﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

public sealed class SpecialPropertyKey (string specialPropertyKey) : ValueObject
{
    public static readonly SpecialPropertyKey Content = new SpecialPropertyKey("Content");

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return specialPropertyKey.ToLower();
    }

    public override string ToString ()
    {
        return specialPropertyKey;
    }

    public static explicit operator SpecialPropertyKey (string specialPropertyKey)
    {
        return new SpecialPropertyKey(specialPropertyKey);
    }
}