﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;
using System.Collections.Generic;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields
{
    [TestFixture]
    public class CreativeTemplateMultiSelectCreativeFieldTests
    {
        private readonly string _fieldName = "fieldName";
        private readonly long _fieldId = 1234;
        private readonly List<SelectOption> _selectOptions =
        [
            SelectOption.Create(111, "option1"),
            SelectOption.Create(112, "option2")
        ];

        [Test]
        public void Creative_template_field_cant_be_created_with_verifier_fails ()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);
            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId,
                new HashSet<CreativeField>(), 1);

            creativeTemplateFieldResult.Should().BeFailure();
        }

        [Test]
        public void Creative_template_field_can_be_created ()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);
            var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult = CreativeTemplateCreativeField.Create(creativeFieldId,
                new HashSet<CreativeField>([CreativeField.Create(creativeFieldId, _fieldName, CreativeFieldType.MultiSelectOption, creativeFieldUniqueNameRequirement, false, null, new Dictionary<string, object> { { nameof(SelectCreativeFieldSettings.Options).ToLower(), _selectOptions } }).Value]), 1);

            creativeTemplateFieldResult.Should().BeSuccess();
            creativeTemplateFieldResult.Value.Should().NotBeNull();
            ((CreativeTemplateMultiSelectCreativeField)creativeTemplateFieldResult.Value).Options.Count.Should().Be(2);
            creativeTemplateFieldResult.Value.Name.Should().BeEquivalentTo(_fieldName);
        }
    }
}

