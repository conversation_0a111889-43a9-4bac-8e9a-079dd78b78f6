﻿using Fattail.CreativeManagement.API.Domain.CreativeFiles;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;

public static class RequiredRuleValidatorsFactory
{
    public static IRuleValidator<TFieldValue> GetValidator<TFieldValue> (CreativeFieldIdentifier creativeFieldIdentifier)
    {
        IRuleValidator<TFieldValue> validator = null!;

        if (typeof(TFieldValue) == typeof(string))
        {
            validator = (IRuleValidator<TFieldValue>)new StringRequiredRuleValidator(creativeFieldIdentifier);
        }
        else if (typeof(TFieldValue) == typeof(IReadOnlyList<CreativeFile?>))
        {
            validator = (IRuleValidator<TFieldValue>)new CollectionsRequiredRuleValidator<IReadOnlyList<CreativeFile?>, CreativeFile?>(creativeFieldIdentifier);
        }
        else if (typeof(TFieldValue) == typeof(IEnumerable<long>))
        {
            validator = (IRuleValidator<TFieldValue>)new CollectionsRequiredRuleValidator<IEnumerable<long>, long>(creativeFieldIdentifier);
        }
        else if (typeof(TFieldValue) == typeof(long?))
        {
            validator = (IRuleValidator<TFieldValue>)new LongRequiredRuleValidator(creativeFieldIdentifier);
        }
        else
        {
            throw new NotImplementedException();
        }

        return validator;
    }
}