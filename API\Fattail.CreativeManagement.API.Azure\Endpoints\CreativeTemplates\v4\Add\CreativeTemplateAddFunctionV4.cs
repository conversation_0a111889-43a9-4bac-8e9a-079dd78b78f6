using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateAdd;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2.Add;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v4.Add;

public class CreativeTemplateAddFunctionV4 (IMediator mediator, IMapper mapper)
    : BaseFunction(mediator, mapper)
{
    [OpenApiOperation(
        "Creative Templates Create v4",
        tags: new[] { "v4" },
        Summary = "Creates a new creative template v4")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody("application/json", typeof(CreativeTemplateAddRequestV2),
        Description = "Creative template to create", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeTemplateResponse))]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeTemplateAddFunctionV4))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v4/creative-templates")]
        HttpRequestData req)
    {
        CreativeTemplateAddCommand creativeTemplateAddCommand =
            await FromRequest<CreativeTemplateAddRequestV2, CreativeTemplateAddCommand>(req);

        Result<CreativeTemplateResult> creativeTemplateAddResult = await _mediator.Send(creativeTemplateAddCommand);

        return await FromResultWithLocation<CreativeTemplateResult, CreativeTemplateResponse>(req,
            creativeTemplateAddResult, creativeTemplateAddResult.ValueOrDefault?.Id);
    }
}