﻿
using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrate.Errors;

public sealed class CreativeFileNotFoundInAbOmsError : ErrorBase
{
    public CreativeFileNotFoundInAbOmsError (string creativeFileName, string fileLocation)
        : base($"The creative file {creativeFileName} was not found in ABOMS at location {fileLocation}",
            ErrorType.CreativeFileNotFoundInAbOms)
    {
        Metadata.Add(nameof(creativeFileName), creativeFileName);
        Metadata.Add(nameof(fileLocation), fileLocation);
    }
}