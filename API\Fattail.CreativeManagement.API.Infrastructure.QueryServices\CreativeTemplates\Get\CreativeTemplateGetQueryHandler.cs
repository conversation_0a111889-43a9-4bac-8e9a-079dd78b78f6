using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.Get;

internal class CreativeTemplateGetQueryHandler : IRequestHandler<CreativeTemplateGetQuery, QueryResult<CreativeTemplateQueryResult>>
{
    private readonly CreativeTemplateQueryService _creativeTemplatesQueryService;

    public CreativeTemplateGetQueryHandler (CreativeTemplateQueryService creativeTemplatesQueryService)
    {
        _creativeTemplatesQueryService = creativeTemplatesQueryService;
    }

    public async Task<QueryResult<CreativeTemplateQueryResult>> Handle (CreativeTemplateGetQuery request,
        CancellationToken cancellationToken)
    {
        return await _creativeTemplatesQueryService.GetFrom<CreativeTemplateQueryResult>(request);
    }
}