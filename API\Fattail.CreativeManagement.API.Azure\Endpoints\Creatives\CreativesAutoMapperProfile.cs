﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives;

internal sealed class CreativesAutoMapperProfile : Profile
{
    public CreativesAutoMapperProfile ()
    {
        CreateMap<CreativeQueryResult, CreativeResponse>();
        CreateMap<CreativeQueryResult, CreativeResponseV2>();
        CreateMap<CreativeQueryResult, CreativeResponseV3>();
        CreateMap<CreativeQueryResult, CreativeResponseV4>();
        CreateMap<CreativeFileValueResult, CreativeFileResponse>();
        CreateMap<CreativeFieldValueResult, CreativeFieldValueResponse>()
            .Include(typeof(CreativeFieldValueResult<>), typeof(CreativeFieldValueResponse<>));

        CreateMap(typeof(CreativeFieldValueResult<>), typeof(CreativeFieldValueResponse<>))
            .Include(typeof(MultiFileUploadFieldValueResult), typeof(MultiFileUploadCreativeFieldResponse))
            .Include(typeof(FileUploadFieldValueResult), typeof(FileUploadCreativeFieldResponse))
            .Include(typeof(SingleLineTextFieldValueResult), typeof(SingleLineTextCreativeFieldResponse))
            .Include(typeof(MultiSelectOptionFieldValueResult), typeof(MultiSelectOptionCreativeFieldResponse))
            .Include(typeof(SingleSelectOptionFieldValueResult), typeof(SingleSelectOptionCreativeFieldResponse))
            .Include(typeof(MultiLineTextFieldValueResult), typeof(MultiLineTextCreativeFieldResponse));

        CreateMap<MultiFileUploadFieldValueResult, MultiFileUploadCreativeFieldResponse>();
        CreateMap<SingleLineTextFieldValueResult, SingleLineTextCreativeFieldResponse>();
        CreateMap<FileUploadFieldValueResult, FileUploadCreativeFieldResponse>();
        CreateMap<MultiSelectOptionFieldValueResult, MultiSelectOptionCreativeFieldResponse>();
        CreateMap<SingleSelectOptionFieldValueResult, SingleSelectOptionCreativeFieldResponse>();
        CreateMap<MultiLineTextFieldValueResult, MultiLineTextCreativeFieldResponse>();

        CreateMap<CreativeStatusResult, CreativeStatusResponse>();
        CreateMap<ApprovalInformationResult, ApprovalInformationResponse>();
    }
}