﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

internal sealed class CreativeTemplateCommonAutoMapperProfile : Profile
{
    public CreativeTemplateCommonAutoMapperProfile ()
    {
        CreateMap<CreativeTemplateQueryResult, CreativeTemplateResponse>();
        CreateMap<CreativeTemplateResult, CreativeTemplateResponse>();
        CreateMap<CreativeTemplateCreativeFieldResult, CreativeTemplateCreativeFieldResponse>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldResult), typeof(CreativeTemplateMultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldResult), typeof(CreativeTemplateSingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldResult), typeof(CreativeTemplateSectionDividerCreativeFieldResponse));

        CreateMap<CreativeTemplateCreativeFieldResult, CreativeFieldResponse>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldResult), typeof(SingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldResult), typeof(SectionDividerCreativeFieldResponse));
        CreateMap<CreativeTemplateMultiSelectCreativeFieldResult, MultiSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSingleSelectCreativeFieldResult, SingleSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSectionDividerCreativeFieldResult, SectionDividerCreativeFieldResponse>();

        CreateMap<ValidationRuleResult, ValidationRuleResponse>();
        CreateMap<CreativeTemplateMultiSelectCreativeFieldResult, CreativeTemplateMultiSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSingleSelectCreativeFieldResult, CreativeTemplateSingleSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSectionDividerCreativeFieldResult, CreativeTemplateSectionDividerCreativeFieldResponse>();
        CreateMap<CreativeTemplateSelectOptionResult, SelectOptionResponse>();

        CreateMap<CreativeTemplateCreativeFieldQueryResult, CreativeFieldResponse>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeFieldQueryResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSingleSelectCreativeFieldQueryResult), typeof(SingleSelectCreativeFieldResponse))
            .Include(typeof(CreativeTemplateSectionDividerCreativeFieldQueryResult), typeof(SectionDividerCreativeFieldResponse));
        CreateMap<CreativeTemplateMultiSelectCreativeFieldQueryResult, MultiSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSingleSelectCreativeFieldQueryResult, SingleSelectCreativeFieldResponse>();
        CreateMap<CreativeTemplateSectionDividerCreativeFieldQueryResult, SectionDividerCreativeFieldResponse>();
    }
}