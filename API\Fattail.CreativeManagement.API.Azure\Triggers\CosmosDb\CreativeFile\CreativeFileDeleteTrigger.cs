using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.CreativeFiles.Unassign;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Azure.Triggers.CosmosDb.CreativeFile;

public class CreativeFileDeleteTrigger
{
    private readonly ILogger<CreativeFileDeleteTrigger> _logger;
    private readonly IMediator _mediator;
    private readonly ParallelExecutionSettings _parallelExecutionSettings;

    public CreativeFileDeleteTrigger (
        IMediator mediator,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        ILogger<CreativeFileDeleteTrigger> logger)
    {
        _mediator = mediator;
        _logger = logger;
        _parallelExecutionSettings = parallelExecutionSettings.Value;
    }

    [Function(nameof(CreativeFileDeleteTrigger))]
    public async Task Run ([CosmosDBTrigger(
            "CreativeManagement",
            "CreativeFiles",
            Connection = "CMS:ConnectionString",
            CreateLeaseContainerIfNotExists = true)]
        IReadOnlyList<CreativeFileDocument> creativeFileDocuments, FunctionContext context)
    {
        await Parallel.ForEachAsync(creativeFileDocuments, new ParallelOptions { MaxDegreeOfParallelism = _parallelExecutionSettings.MaxDegreeOfParallelismForDeletion }, async (creativeFileDocument, _) =>
        {
            _logger.LogInformation("Creative file id => {creativeFileId} ", creativeFileDocument.Id);

            switch (creativeFileDocument.LastAction)
            {
                case ActionType.Update:
                    if (creativeFileDocument.ToBeDeleted)
                    {
                        await _mediator.Publish(new CreativeFileUnassignedEvent(long.Parse(creativeFileDocument.Id), creativeFileDocument.BlobName, creativeFileDocument.Location));
                    }
                    break;
            }
        });
    }
}