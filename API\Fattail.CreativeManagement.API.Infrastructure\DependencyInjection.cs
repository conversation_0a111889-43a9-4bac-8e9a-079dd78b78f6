﻿using Fattail.CreativeManagement.API.Infrastructure.CreativeFilesManager;
using Fattail.CreativeManagement.API.Infrastructure.DateTimeProvider;
using Fattail.CreativeManagement.API.Infrastructure.ExecutionContext;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients;
using Fattail.CreativeManagement.API.Infrastructure.IdManager;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Infrastructure;

public static class DependencyInjection
{
    public static void AddInfrastructure (this IServiceCollection services)
    {
        services.AddOptions<ParallelExecutionSettings>()
            .Configure<IConfiguration>((settings, configuration) =>
                configuration.GetSection(nameof(ParallelExecutionSettings))
                    .Bind(settings));
        
        services.AddExecutionContext();
        services.AddSettings();
        services.AddHttpClients();
        services.AddAzureBlobStorage();
        services.AddIdManager();
        services.AddDateTimeProvider();
    }
}