using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields.Validators.RequiredValidators;

[TestFixture]
public class LongRequiredValidatorTests
{
    private CreativeFieldIdentifier _creativeFieldIdentifier;
    private LongRequiredRuleValidator _longRequiredRuleValidator = null!;

    [SetUp]
    public void SetUp()
    {
        var creativeFieldId = new CreativeFieldId(123456);
        CreativeFieldType creativeFieldType = CreativeFieldType.SingleSelectOption;
        _creativeFieldIdentifier = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);
    }

    [Test]
    public async Task Long_field_value_with_positive_value_is_valid()
    {
        long? fieldValue = 12345L;
        _longRequiredRuleValidator = new LongRequiredRuleValidator(_creativeFieldIdentifier);

        Result result = await _longRequiredRuleValidator.IsValid(fieldValue);

        result.Should().BeSuccess();
    }

    [Test]
    public async Task Long_field_value_with_zero_value_is_invalid()
    {
        long? fieldValue = 0L;
        _longRequiredRuleValidator = new LongRequiredRuleValidator(_creativeFieldIdentifier);

        Result result = await _longRequiredRuleValidator.IsValid(fieldValue);

        result.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldIdentifier.Id.ToString()));
    }

    [Test]
    public async Task Long_field_value_with_negative_value_is_invalid()
    {
        long? fieldValue = -1L;
        _longRequiredRuleValidator = new LongRequiredRuleValidator(_creativeFieldIdentifier);

        Result result = await _longRequiredRuleValidator.IsValid(fieldValue);

        result.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldIdentifier.Id.ToString()));
    }

    [Test]
    public async Task Long_field_value_with_null_value_is_invalid()
    {
        long? fieldValue = null;
        _longRequiredRuleValidator = new LongRequiredRuleValidator(_creativeFieldIdentifier);

        Result result = await _longRequiredRuleValidator.IsValid(fieldValue);

        result.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldIdentifier.Id.ToString()));
    }
}
