using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.GetPredefinedCreativeTemplate;

internal class PredefinedCreativeTemplateGetQueryHandler (
    PredefinedCreativeTemplateQueryService predefinedCreativeTemplatesQueryService)
    : IRequestHandler<PredefinedCreativeTemplateGetQuery, QueryResult<CreativeTemplateQueryResult>>
{
    public async Task<QueryResult<CreativeTemplateQueryResult>> Handle (PredefinedCreativeTemplateGetQuery request,
        CancellationToken cancellationToken)
    {
        return await predefinedCreativeTemplatesQueryService.GetFrom<CreativeTemplateQueryResult>(request);
    }
}