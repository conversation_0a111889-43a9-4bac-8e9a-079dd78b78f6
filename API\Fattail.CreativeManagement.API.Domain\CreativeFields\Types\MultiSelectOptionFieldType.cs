using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class MultiSelectOptionFieldType : CreativeFieldType
{
    internal MultiSelectOptionFieldType () : base(nameof(CreativeFieldTypeEnum.MultiSelectOption), (int)CreativeFieldTypeEnum.MultiSelectOption)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return SelectCreativeFieldSettings.CreateFromSettings(settings, nameof(MultiSelectOptionFieldType))
            .ToResult<CreativeFieldSettings>(selectSettings => selectSettings);
    }
}
