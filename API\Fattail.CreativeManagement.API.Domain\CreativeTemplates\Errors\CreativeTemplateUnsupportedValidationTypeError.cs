﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateUnsupportedValidationTypeError : ErrorBase
{
    public CreativeTemplateUnsupportedValidationTypeError (string creativeFieldId, string entity, string creativeFieldValidationRuleType)
        : base($"The creative field with id {creativeFieldId} does not support validation type {creativeFieldValidationRuleType}.",
            ErrorType.CreativeTemplateFieldUnsupportedValidationRuleType)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(creativeFieldValidationRuleType), creativeFieldValidationRuleType);
    }
}