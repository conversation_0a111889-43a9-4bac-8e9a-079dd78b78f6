﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields.GetById;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.v3.Get;

[ExcludeFromCodeCoverage]
public class CreativeFieldGetByIdFunctionV3 : BaseFunction
{
    public CreativeFieldGetByIdFunctionV3 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation("Creative Field Get By Id v3", tags: new[] { "v3" },
        Summary = "Get creative field by Id v3")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeFieldResponse))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound,
        Description = "Returned when the creative field does not exist.")]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeFieldGetByIdFunctionV3))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "v3/creative-fields/{id:long}")]
        HttpRequestData req,
        long id)
    {
        var getByIdQuery = new CreativeFieldGetByIdQuery(id.ToString());
        
        CreativeFieldQueryResult? getByIdResult = await _mediator.Send(getByIdQuery);
        
        return await FromSingleQueryResult<CreativeFieldQueryResult, CreativeFieldResponse>(req, getByIdResult);
    }
}