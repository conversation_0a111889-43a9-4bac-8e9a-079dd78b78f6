﻿using System.Collections.ObjectModel;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

internal sealed record CreativeTemplateCreativeFieldRequest
{
    public CreativeTemplateCreativeFieldRequest(long? id,
        int? displayOrder,
        IReadOnlyList<ValidationRuleRequest> validationRules,
        string? tooltip,
        IReadOnlyDictionary<string, object?>? specialProperties)
    {
        ValidationRules = validationRules;
        Tooltip = tooltip;
        DisplayOrder = displayOrder;
        Id = id;
        SpecialProperties = specialProperties ??  new ReadOnlyDictionary<string, object?>(new Dictionary<string, object?>());
    }
    
    public long? Id { get; init; }
    public int? DisplayOrder { get; init; }
    public IReadOnlyList<ValidationRuleRequest> ValidationRules { get; init; }
    public IReadOnlyDictionary<string, object?> SpecialProperties { get; init; }
    public string? Tooltip { get; init; }
}