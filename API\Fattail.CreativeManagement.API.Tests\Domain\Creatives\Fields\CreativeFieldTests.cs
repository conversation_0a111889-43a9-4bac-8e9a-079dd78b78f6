﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using FluentAssertions;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class CreativeFieldTests
{
    [Test]
    public void Creative_field_creation_without_id_fails ()
    {
        Invoking(() => CreativeFieldIdentifier.Create(null!, CreativeFieldType.MultiFileUpload))
            .Should().ThrowExactly<ArgumentNullException>();
    }

    [Test]
    public void Creative_field_can_be_created ()
    {
        var creativeFieldId = new CreativeFieldId(123456);
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;

        var creativeField = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);

        creativeField.Id.Should().Be(creativeFieldId);
        creativeField.Type.Should().Be(creativeFieldType);
    }
}