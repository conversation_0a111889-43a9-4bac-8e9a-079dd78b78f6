﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class SingleLineTextFieldValueTests
{
    private static readonly CreativeFieldType[] _invalidCreativeFieldTypes =
        CreativeFieldType.List
        .Where(creativeFieldType => creativeFieldType != CreativeFieldType.SingleLineText)
        .ToArray();

    [Test]
    public async Task Single_line_text_field_value_can_be_created ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleLineText);

        string text = "abcdefg";

        Result<SingleLineTextFieldValue> singleLineTextFieldValue = await SingleLineTextFieldValue.Create(creativeField, text, new List<ValidationRule>());

        singleLineTextFieldValue.Value.CreativeFieldIdentifier.Should().Be(creativeField);
        singleLineTextFieldValue.Value.Value.Should().Be(text);
    }

    [Test]
    public async Task Single_line_text_field_value_can_be_created_without_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleLineText);

        var singleLineTextFieldValueWithoutValue =
            (SingleLineTextFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        singleLineTextFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);
        singleLineTextFieldValueWithoutValue.Value.Should().BeNull();
    }

    [Test]
    public async Task Single_line_text_field_value_creation_with_invalid_value_type_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleLineText);

        await Invoking(async () =>
                await SingleLineTextFieldValue.Create(creativeField, new object(), null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [TestCaseSource(nameof(_invalidCreativeFieldTypes))]
    public async Task Single_line_text_field_value_creation_with_invalid_creative_field_type_fails (
        CreativeFieldType invalidCreativeFieldType)
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), invalidCreativeFieldType);

        await Invoking(async () =>
                await SingleLineTextFieldValue.Create(creativeField,
                    new CreativeFileId(12345), null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [Test]
    public async Task Single_line_text_field_value_generates_new_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.SingleLineText);

        var singleLineTextFieldValueWithoutValue =
            (SingleLineTextFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        string text = "abdefg";

        var singleLineTextFieldValueWithNewValue =
            (SingleLineTextFieldValue)(await singleLineTextFieldValueWithoutValue.GenerateNewValue(text, new List<ValidationRule>())).Value;

        singleLineTextFieldValueWithoutValue.Value.Should().BeNull();
        singleLineTextFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);

        singleLineTextFieldValueWithNewValue.CreativeFieldIdentifier.Should().Be(creativeField);
        singleLineTextFieldValueWithNewValue.Value.Should().Be(text);
    }
}