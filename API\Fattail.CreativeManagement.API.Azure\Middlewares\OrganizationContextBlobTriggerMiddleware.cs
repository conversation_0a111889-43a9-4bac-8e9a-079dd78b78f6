﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal sealed class OrganizationContextBlobTriggerMiddleware : IFunctionsWorkerMiddleware
{
    private readonly ILogger _logger;
    private readonly List<OrganizationSettings> _organizationsSettings;

    public OrganizationContextBlobTriggerMiddleware (IOptions<List<OrganizationSettings>> organizationsSettings,
        ILogger<OrganizationContextBlobTriggerMiddleware> iLogger)
    {
        _organizationsSettings = organizationsSettings.Value;
        _logger = iLogger;
    }

    public async Task Invoke (FunctionContext context, FunctionExecutionDelegate next)
    {
        string? orgId = context.BindingContext.BindingData["orgId"]?.ToString();

        if (_organizationsSettings.All(organizationSettings => organizationSettings.OrgId.ToString() != orgId))
        {
            _logger.LogError("Organization id passed in blob trigger does not exist");
            return;
        }

        IOrganizationContext organizationContext = context.InstanceServices.GetRequiredService<IOrganizationContext>();

        organizationContext.SetOrganizationId(long.Parse(orgId));

        await next(context);
        
    }
}