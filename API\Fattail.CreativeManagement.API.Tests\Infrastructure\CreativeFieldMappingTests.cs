using AutoMapper;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using FluentAssertions;
using NUnit.Framework;
using CreativeFieldEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using CreativeFieldDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeField;
using DefaultCreativeFieldSettingsEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.DefaultCreativeFieldSettings;
using SelectCreativeFieldSettingsEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectCreativeFieldSettings;
using SelectOptionEntity = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectOption;
using SelectOptionDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.Settings.SelectOption;

namespace Fattail.CreativeManagement.API.Tests.Infrastructure;

[TestFixture]
public class CreativeFieldMappingTests
{
    private IMapper _mapper = null!;

    private static IEnumerable<CreativeFieldType> SelectFieldTypes => [CreativeFieldType.SingleSelectOption, CreativeFieldType.MultiSelectOption];

    private static IEnumerable<CreativeFieldType> NonSelectFieldTypes => CreativeFieldType.List
        .Where(ft => ft != CreativeFieldType.SingleSelectOption && ft != CreativeFieldType.MultiSelectOption && ft != CreativeFieldType.None);

    [SetUp]
    public void SetUp ()
    {
        var configuration = new MapperConfiguration(cfg =>
        {
            cfg.AddMaps(typeof(ICosmosDbContainer));
        });

        _mapper = configuration.CreateMapper();
    }

    [Test]
    public void Should_map_non_select_creative_field_from_domain_to_cosmos ([ValueSource(nameof(NonSelectFieldTypes))] CreativeFieldType fieldType)
    {
        var domainField = CreateNonSelectDomainField(fieldType);

        var cosmosField = _mapper.Map<CreativeFieldEntity>(domainField);

        cosmosField.Should().NotBeNull();
        cosmosField.Id.Should().Be("123");
        cosmosField.Name.Should().Be($"Test {fieldType.Name} Field");
        cosmosField.Type.Should().Be(fieldType.EnumType);
        cosmosField.Predefined.Should().BeFalse();
        cosmosField.OmsExternalIdentifier.Should().Be("test-external-id");
        cosmosField.Options.Should().BeNullOrEmpty();
        cosmosField.Settings.Should().BeOfType<DefaultCreativeFieldSettingsEntity>();
    }

    [Test]
    public void Should_map_non_select_creative_field_from_cosmos_to_domain ([ValueSource(nameof(NonSelectFieldTypes))] CreativeFieldType fieldType)
    {
        var cosmosField = CreateNonSelectCosmosField(fieldType);

        var domainField = _mapper.Map<CreativeFieldDomain>(cosmosField);

        domainField.Should().NotBeNull();
        ((long)domainField.Id).Should().Be(123);
        domainField.Name.Should().Be($"Test {fieldType.Name} Field");
        domainField.Type.Should().Be(fieldType);
        domainField.Predefined.Should().BeFalse();
        domainField.OmsExternalIdentifier.Should().Be("test-external-id");
        domainField.Settings.Should().Be(DefaultCreativeFieldSettings.Instance);
    }

    [Test]
    public void Should_map_select_creative_field_from_domain_to_cosmos ([ValueSource(nameof(SelectFieldTypes))] CreativeFieldType fieldType)
    {
        var domainField = CreateSelectDomainField(fieldType);

        var cosmosField = _mapper.Map<CreativeFieldEntity>(domainField);

        cosmosField.Should().NotBeNull();
        cosmosField.Id.Should().Be("456");
        cosmosField.Name.Should().Be($"Test {fieldType.Name} Field");
        cosmosField.Type.Should().Be(fieldType.EnumType);
        cosmosField.Predefined.Should().BeTrue();
        cosmosField.OmsExternalIdentifier.Should().BeNull();
        cosmosField.Settings.Should().BeOfType<SelectCreativeFieldSettingsEntity>();
        cosmosField.Options.Should().NotBeNull();
        cosmosField.Options.Should().HaveCount(2);
        cosmosField.Options![0].Id.Should().Be("1");
        cosmosField.Options[0].Description.Should().Be("Option 1");
        cosmosField.Options[1].Id.Should().Be("2");
        cosmosField.Options[1].Description.Should().Be("Option 2");
    }

    private static CreativeFieldDomain CreateNonSelectDomainField (CreativeFieldType fieldType)
    {
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var result = CreativeFieldDomain.Create(
            new CreativeFieldId(123),
            $"Test {fieldType.Name} Field",
            fieldType,
            creativeFieldUniqueNameRequirement,
            false,
            "test-external-id",
            null);

        return result.Value;
    }

    private static CreativeFieldEntity CreateNonSelectCosmosField (CreativeFieldType fieldType)
    {
        return new CreativeFieldEntity
        {
            Id = "123",
            Name = $"Test {fieldType.Name} Field",
            Type = fieldType.EnumType,
            Predefined = false,
            OmsExternalIdentifier = "test-external-id",
            Options = null,
            Settings = new DefaultCreativeFieldSettingsEntity()
        };
    }

    private static CreativeFieldDomain CreateSelectDomainField (CreativeFieldType fieldType)
    {
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        IReadOnlyList<SelectOptionDomain> selectOptions = new List<SelectOptionDomain>
        {
            SelectOptionDomain.Create(1, "Option 1"),
            SelectOptionDomain.Create(2, "Option 2")
        }.AsReadOnly();

        var settings = new Dictionary<string, object>
        {
            { nameof(SelectCreativeFieldSettings.Options).ToLower(), selectOptions }
        };

        var result = CreativeFieldDomain.Create(
            new CreativeFieldId(456),
            $"Test {fieldType.Name} Field",
            fieldType,
            creativeFieldUniqueNameRequirement,
            true,
            null,
            settings);

        return result.Value;
    }


}
