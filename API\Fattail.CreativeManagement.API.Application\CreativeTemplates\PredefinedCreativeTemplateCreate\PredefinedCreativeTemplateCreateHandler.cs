﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;
using System.Collections.Generic;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates.PredefinedCreativeTemplateCreate;

public class PredefinedCreativeTemplateCreateHandler (IPredefinedCreativeTemplateRepository predefinedCreativeTemplateRepository,
    IPredefinedCreativeFieldRepository predefinedCreativeFieldRepository)
    : IRequestHandler<PredefinedCreativeTemplateCreateCommand, Result>
{
    public async Task<Result> Handle (PredefinedCreativeTemplateCreateCommand request, CancellationToken cancellationToken)
    {
        await TryCreateGamImageTemplate();

        return Result.Ok();
    }

    private async Task TryCreateGamImageTemplate ()
    {
        var gamImageTemplateId = new CreativeTemplateId(146503531073539);

        CreativeTemplate? existingTemplate = await predefinedCreativeTemplateRepository.FindByIdAsync(gamImageTemplateId);
        if (existingTemplate != null)
        {
            return;
        }

        CreativeField adSizeCreativeField = await GetOrCreateSingleSelectOptionPredefinedCreativeField(new(146343531073536), "Ad Size", ["160x600", "300x100", "300x250", "300x600", "320x50", "468x60", "640x480", "728x90", "970x66"], "CL_Ad_Size");
        CreativeField fileUploadCreativeField = await GetOrCreatePredefinedCreativeField(new(146503034353537), "File Upload", CreativeFieldType.FileUpload, "CL_Image_File");
        CreativeField clickThroughUrlCreativeField = await GetOrCreatePredefinedCreativeField(new(146503034353107), "Click-through URL", CreativeFieldType.SingleLineText, "CL_Click_Through_URL");

        var gamImageTemplateCreateRequest = new CreativeTemplateCreateRequest(
            gamImageTemplateId,
            CreativeTemplateName.Create("GAM Image Template", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Image,
            [
                new DisplayOrderCreativeField(adSizeCreativeField.Id, 1),
                new DisplayOrderCreativeField(fileUploadCreativeField.Id, 2),
                new DisplayOrderCreativeField(clickThroughUrlCreativeField.Id, 3)
            ],
            new HashSet<CreativeField>([adSizeCreativeField, fileUploadCreativeField, clickThroughUrlCreativeField]),
            true
        );

        CreativeTemplate gamImageTemplate = CreativeTemplateFactory.Create(gamImageTemplateCreateRequest).Value;

        CreativeTemplateCreativeField adSizeCreativeTemplateCreativeField = gamImageTemplate.GetFieldByCreativeFieldId(adSizeCreativeField.Id)!;
        adSizeCreativeTemplateCreativeField.AddValidationRule(CreativeFieldValidationRuleType.Required, []);

        CreativeTemplateCreativeField fileUploadCreativeTemplateCreativeField = gamImageTemplate.GetFieldByCreativeFieldId(fileUploadCreativeField.Id)!;
        fileUploadCreativeTemplateCreativeField.AddValidationRule(CreativeFieldValidationRuleType.Required, []);
        fileUploadCreativeTemplateCreativeField.AddValidationRule(CreativeFieldValidationRuleType.FileUploadExtensions, [".gif", ".jpg", ".png"]);
        fileUploadCreativeTemplateCreativeField.AddValidationRule(CreativeFieldValidationRuleType.FileSize, ["10"]);
        fileUploadCreativeTemplateCreativeField.SetTooltip("Allowed File Types: Only GIF, JPG, PNG | Allowable File Size: 10 MB");

        CreativeTemplateCreativeField clickThroughUrlCreativeTemplateCreativeField = gamImageTemplate.GetFieldByCreativeFieldId(clickThroughUrlCreativeField.Id)!;
        clickThroughUrlCreativeTemplateCreativeField.AddValidationRule(CreativeFieldValidationRuleType.Required, []);

        await predefinedCreativeTemplateRepository.CreateAsync(gamImageTemplate);
    }

    private async Task<CreativeField> GetOrCreatePredefinedCreativeField(CreativeFieldId id, string name, CreativeFieldType type, string omsExternalIdentifier)
    {
        CreativeField? existingCreativeField = await predefinedCreativeFieldRepository.FindByIdAsync(id);

        if (existingCreativeField != null)
        {
            return existingCreativeField;
        }

        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        CreativeField creativeField = CreativeField.Create(id, name, type, creativeFieldUniqueNameRequirement, true, omsExternalIdentifier, null).Value;

        await predefinedCreativeFieldRepository.CreateAsync(creativeField);

        return creativeField;
    }

    private async Task<CreativeField> GetOrCreateSingleSelectOptionPredefinedCreativeField(CreativeFieldId id, string name, IReadOnlyList<string> options, string omsExternalIdentifier)
    {
        CreativeField? existingCreativeField = await predefinedCreativeFieldRepository.FindByIdAsync(id);

        if (existingCreativeField != null)
        {
            return existingCreativeField;
        }

        long autoGeneratedId = 1;
        var selectOptions = options.Select(option => SelectOption.Create(autoGeneratedId++, option)).ToList();

        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        CreativeField creativeField = CreativeField.Create(id, name, CreativeFieldType.SingleSelectOption, creativeFieldUniqueNameRequirement, true, omsExternalIdentifier, new Dictionary<string, object> { { nameof(SelectCreativeFieldSettings.Options).ToLower(), selectOptions } }).Value;

        await predefinedCreativeFieldRepository.CreateAsync(creativeField);

        return creativeField;
    }
}
