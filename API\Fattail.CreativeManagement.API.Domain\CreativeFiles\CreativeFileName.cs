﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;

public sealed class CreativeFileName : ValueObject
{
    private CreativeFileName (string fileName)
    {
        Name = fileName;
        Extension = FileExtension.From(fileName);
        NameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
    }
    public string Name { get; }

    public FileExtension Extension { get; }

    public string NameWithoutExtension { get; }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return Name;
    }

    public static CreativeFileName From (string fileName)
    {
        Guard.Argument(fileName, nameof(fileName)).NotNull();
        return new CreativeFileName(fileName);
    }

    public override string ToString ()
    {
        return Name;
    }

    public static explicit operator CreativeFileName (string fileName)
    {
        return From(fileName);
    }
}