﻿using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Infrastructure.TriggerServices;

public static class DependencyInjection
{
    public static void AddTriggerServices (this IServiceCollection services)
    {
        services.AddScoped<CreativeEventHandlerService>();
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly));
    }
}