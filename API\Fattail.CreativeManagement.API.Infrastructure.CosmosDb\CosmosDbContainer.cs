﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb;

internal sealed class CosmosDbContainer : ICosmosDbContainer
{
    public CosmosDbContainer (CosmosClient cosmosClient,
        string databaseName,
        string containerName)
    {
        Container = cosmosClient.GetContainer(databaseName, containerName);
    }

    public Container Container { get; }
}