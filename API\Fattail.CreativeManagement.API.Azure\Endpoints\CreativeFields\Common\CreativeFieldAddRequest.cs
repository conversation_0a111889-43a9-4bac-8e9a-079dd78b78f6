﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;

internal record CreativeFieldAddRequest(
    string Name,
    CreativeFieldType? Type,
    IReadOnlyList<SelectOption> Options);

internal sealed record SelectOption(
    long Id,
    string Description);

[JsonConverter(typeof (StringEnumConverter))]
public enum CreativeFieldType
{
    SingleLineText = 0,
    MultiFileUpload = 1,
    FileUpload = 2,
    MultiSelectOption = 3,
    MultiLineText = 4,
    SectionDivider = 5,
    SingleSelectOption = 6
}
