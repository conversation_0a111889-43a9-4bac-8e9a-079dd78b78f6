﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper.CustomTypeConverters;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Creative = Fattail.CreativeManagement.API.Domain.Creatives.Creative;
using CreativeFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.CreativeFieldValue;
using FileUploadFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.FileUploadFieldValue;
using MultiFileUploadFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.MultiFileUploadFieldValue;
using SingleLineTextFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.SingleLineTextFieldValue;
using MultiLineTextFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.MultiLineTextFieldValue;
using MultiSelectOptionFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.MultiSelectOptionFieldValue;
using SingleSelectOptionFieldValue = Fattail.CreativeManagement.API.Domain.Creatives.Fields.SingleSelectOptionFieldValue;
using Fattail.CreativeManagement.API.Application.Creatives;
using CreativeStatus = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeStatus;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper;

internal sealed class CreativesProfile : Profile
{
    public CreativesProfile ()
    {
        CreateMap<CreativeFieldIdentifier, Entities.CreativeFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<CreativeFieldValue, Entities.CreativeFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .IncludeMembers(creativeFieldValue => creativeFieldValue.CreativeFieldIdentifier)
            .Include(typeof(Domain.Creatives.Fields.CreativeFieldValue<>), typeof(Entities.CreativeFieldValue<>))
            .ReverseMap();
        
        CreateMap(typeof(Domain.Creatives.Fields.CreativeFieldValue<>), typeof(Entities.CreativeFieldValue<>))
            .Include(typeof(MultiFileUploadFieldValue), typeof(Entities.MultiFileUploadFieldValue))
            .Include(typeof(FileUploadFieldValue), typeof(Entities.FileUploadFieldValue))
            .Include(typeof(SingleLineTextFieldValue), typeof(Entities.SingleLineTextFieldValue))
            .Include(typeof(MultiSelectOptionFieldValue), typeof(Entities.MultiSelectOptionFieldValue))
            .Include(typeof(SingleSelectOptionFieldValue), typeof(Entities.SingleSelectOptionFieldValue))
            .Include(typeof(MultiLineTextFieldValue), typeof(Entities.MultiLineTextFieldValue))
            .ReverseMap();

        CreateMap<Domain.Creatives.Statuses.CreativeStatus, CreativeStatus>()
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.EnumType));

        CreateMap<Domain.Creatives.ApprovalInformation, ApprovalInformation>()
            .ReverseMap();

        CreateMap<CreativeStatus, Domain.Creatives.Statuses.CreativeStatus?>().ConvertUsing(cs => cs == null ? null : Domain.Creatives.Statuses.CreativeStatus.FromName(cs.Value.ToString(), true));

        CreateMap<IEnumerable<CreativeFileId>, CreativeFileValue[]>()
            .ConvertUsing<CreativeFileIdTypeConverter>();

        CreateMap<MultiFileUploadFieldValue, Entities.MultiFileUploadFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("creativeFieldIdentifier", opt => opt.MapFrom(src => src));

        CreateMap<CreativeFileId?, CreativeFileValue?>()
            .ConvertUsing<CreativeFileIdTypeConverter>();

        CreateMap<FileUploadFieldValue, Entities.FileUploadFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("creativeFieldIdentifier", opt => opt.MapFrom(src => src));

        CreateMap<SingleLineTextFieldValue, Entities.SingleLineTextFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("creativeFieldIdentifier", opt => opt.MapFrom(src => src));
        
        CreateMap<MultiLineTextFieldValue, Entities.MultiLineTextFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("creativeFieldIdentifier", opt => opt.MapFrom(src => src));

        CreateMap<MultiSelectOptionFieldValue, Entities.MultiSelectOptionFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("creativeFieldIdentifier", opt => opt.MapFrom(src => src));
        
        CreateMap<SingleSelectOptionFieldValue, Entities.SingleSelectOptionFieldValue>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("creativeFieldIdentifier", opt => opt.MapFrom(src => src));
        
        CreateMap<Creative, Entities.Creative>()
            .ConvertUsing<CreativeTypeConverter>();
        
        CreateMap<Entities.Creative, CreativeResult>();
        CreateMap<Entities.CreativeStatus, CreativeStatusResult>();
        CreateMap<Entities.ApprovalInformation, ApprovalInformationResult>();

        CreateMap<Entities.CreativeFieldValue, CreativeFieldValueResult>()
            .Include(typeof(Entities.CreativeFieldValue<>), typeof(CreativeFieldValueResult<>));

        CreateMap(typeof(Entities.CreativeFieldValue<>), typeof(CreativeFieldValueResult<>))
            .Include(typeof(Entities.MultiFileUploadFieldValue), typeof(MultiFileUploadFieldValueResult))
            .Include(typeof(Entities.FileUploadFieldValue), typeof(FileUploadFieldValueResult))
            .Include(typeof(Entities.SingleLineTextFieldValue), typeof(SingleLineTextFieldValueResult))
            .Include(typeof(Entities.MultiLineTextFieldValue), typeof(MultiLineTextFieldValueResult))
            .Include(typeof(Entities.MultiSelectOptionFieldValue), typeof(MultiSelectOptionFieldValueResult))
            .Include(typeof(Entities.SingleSelectOptionFieldValue), typeof(SingleSelectOptionFieldValueResult));

        CreateMap<Entities.MultiFileUploadFieldValue, MultiFileUploadFieldValueResult>();
        CreateMap<Entities.FileUploadFieldValue, FileUploadFieldValueResult>();
        CreateMap<CreativeFileValue, CreativeFileResult>();
        CreateMap<Entities.SingleLineTextFieldValue, SingleLineTextFieldValueResult>();
        CreateMap<Entities.MultiLineTextFieldValue, MultiLineTextFieldValueResult>();
        CreateMap<Entities.MultiSelectOptionFieldValue, MultiSelectOptionFieldValueResult>();
        CreateMap<Entities.SingleSelectOptionFieldValue, SingleSelectOptionFieldValueResult>();

        CreateMap<Entities.Creative, Creative>()
            .ForCtorParam("fields", opt => opt.MapFrom(src => src.Fields.ToDictionary(key => key)));
    }
}