﻿
namespace Fattail.CreativeManagement.API.Infrastructure.CreativeFilesManager;

internal sealed class AdBookCreativeSettings
{
    internal string? AdBookCreativeBaseUri { private get; set; }
    internal string? AdBookEWScrippsCreativeBaseUri { private get; set; }
    
    private const long EwScrippsOrgId = 400710;
    
    public string GetAdBookCreativeBaseUri (long organizationId)
    {
        if (organizationId == EwScrippsOrgId)
        {
            return AdBookEWScrippsCreativeBaseUri;
        }
        
        long adBookInstanceNumber = organizationId / 100000;
        return string.Format(AdBookCreativeBaseUri, adBookInstanceNumber);
    }
}