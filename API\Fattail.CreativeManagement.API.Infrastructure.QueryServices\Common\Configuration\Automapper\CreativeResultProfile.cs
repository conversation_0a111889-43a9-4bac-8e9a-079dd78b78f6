﻿using AutoMapper;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common.Configuration.AutoMapper;

internal sealed class CreativeResultProfile : Profile
{
    public CreativeResultProfile ()
    {
        CreateMap<Creative, CreativeQueryResult>()
            .ForMember(src => src.Status, opt => opt.MapFrom((src, dest, creativeStatusResult, context) =>
            {
                if (src.Status == null)
                {
                    CreativeStatus creativeStatus = context.Mapper.Map<CreativeStatus>(Domain.Creatives.Statuses.CreativeStatus.PendingApproval);
                    return context.Mapper.Map<CreativeStatusResult>(creativeStatus);
                }

                return context.Mapper.Map<CreativeStatusResult>(src.Status);
            }));

        CreateMap<CreativeStatus, CreativeStatusResult>();
        CreateMap<ApprovalInformation, ApprovalInformationResult>();

        CreateMap<CreativeFileValue, CreativeFileValueResult>();

        CreateMap<CreativeFieldValue, CreativeFieldValueResult>()
            .Include(typeof(CreativeFieldValue<>), typeof(CreativeFieldValueResult<>));

        CreateMap(typeof(CreativeFieldValue<>), typeof(CreativeFieldValueResult<>))
            .Include(typeof(MultiFileUploadFieldValue), typeof(MultiFileUploadFieldValueResult))
            .Include(typeof(FileUploadFieldValue), typeof(FileUploadFieldValueResult))
            .Include(typeof(SingleLineTextFieldValue), typeof(SingleLineTextFieldValueResult))
            .Include(typeof(MultiLineTextFieldValue), typeof(MultiLineTextFieldValueResult))
            .Include(typeof(MultiSelectOptionFieldValue), typeof(MultiSelectOptionFieldValueResult))
            .Include(typeof(SingleSelectOptionFieldValue), typeof(SingleSelectOptionFieldValueResult));

        CreateMap<MultiFileUploadFieldValue, MultiFileUploadFieldValueResult>();
        CreateMap<FileUploadFieldValue, FileUploadFieldValueResult>();
        CreateMap<SingleLineTextFieldValue, SingleLineTextFieldValueResult>();
        CreateMap<MultiLineTextFieldValue, MultiLineTextFieldValueResult>();
        CreateMap<MultiSelectOptionFieldValue, MultiSelectOptionFieldValueResult>();
        CreateMap<SingleSelectOptionFieldValue, SingleSelectOptionFieldValueResult>();
    }
}