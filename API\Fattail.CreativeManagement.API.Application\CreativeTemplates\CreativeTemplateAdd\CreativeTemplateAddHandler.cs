﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateAdd;

public class CreativeTemplateAddHandler (
    IIdManager idManager,
    ICreativeTemplateRepository creativeTemplateRepository,
    ICreativeFieldRepository creativeFieldRepository)
    : IRequestHandler<CreativeTemplateAddCommand, Result<CreativeTemplateResult>>
{
    public async Task<Result<CreativeTemplateResult>> Handle (CreativeTemplateAddCommand request,
        CancellationToken cancellationToken)
    {
        var creativeTemplateNameInUseSpecification = new CreativeTemplateNameInUseSpecification(null, request.Name);
        var creativeTemplateUniqueNameRequirement = new CreativeTemplateUniqueNameRequirement(await creativeTemplateRepository.FindAsync(creativeTemplateNameInUseSpecification) == null);

        Result<CreativeTemplateName> creativeTemplateNameCreateResult = CreativeTemplateName.Create(request.Name, creativeTemplateUniqueNameRequirement);

        if (creativeTemplateNameCreateResult.IsFailed)
        {
            return creativeTemplateNameCreateResult.ToResult();
        }

        var orderedCreativeFieldIds =
            request.CreativeFields.Select(c => new DisplayOrderCreativeField((CreativeFieldId)c.Id, c.DisplayOrder, c.SpecialProperties.ToDictionary(k => (SpecialPropertyKey)k.Key, v => v.Value))).ToList();

        var creativeTemplateId = new CreativeTemplateId(idManager.GetId());

        IReadOnlyList<CreativeField> existingCreativeFields =
            await creativeFieldRepository.FindManyByIdAsync<CreativeField>(orderedCreativeFieldIds.Select(ocf => ocf.Id));

        var creativeTemplateCreateRequest = new CreativeTemplateCreateRequest(
            creativeTemplateId,
            creativeTemplateNameCreateResult.Value,
            CreativeType.Undefined,
            orderedCreativeFieldIds,
            existingCreativeFields.ToHashSet(),
            false);

        Result<CreativeTemplate> creativeTemplateCreateResult = CreativeTemplateFactory.Create(creativeTemplateCreateRequest);

        if (creativeTemplateCreateResult.IsFailed)
        {
            return creativeTemplateCreateResult.ToResult();
        }

        CreativeTemplate creativeTemplate = creativeTemplateCreateResult.Value;

        var result = Result.Ok();

        foreach (CreativeTemplateCreativeFieldDto creativeTemplateCreativeFieldDto in request.CreativeFields)
        {
            CreativeTemplateCreativeField? creativeTemplateField =
                creativeTemplate.GetFieldByCreativeFieldId((CreativeFieldId)creativeTemplateCreativeFieldDto.Id);

            if (creativeTemplateField is not null)
            {
                foreach (ValidationRuleDto validationRuleDto in creativeTemplateCreativeFieldDto.ValidationRules)
                {
                    result = Result.Merge(result,
                        creativeTemplateField.AddValidationRule(validationRuleDto.Type, validationRuleDto.Options));
                }

                creativeTemplateField.SetTooltip(creativeTemplateCreativeFieldDto.Tooltip);
            }
        }

        if (result.IsFailed)
        {
            return result;
        }

        return await creativeTemplateRepository.CreateAsync<CreativeTemplateResult>(creativeTemplateCreateResult
            .Value);
    }
}