﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateFieldSpecialPropertyInvalidTypeError : ErrorBase
{
    public CreativeTemplateFieldSpecialPropertyInvalidTypeError (CreativeFieldId creativeFieldId, string specialPropertyName, string expectedType, string entity)
        : base($"The creative field with id {creativeFieldId} expects that {specialPropertyName} value to be {expectedType}.",
            ErrorType.CreativeTemplateFieldUnsupportedValidationRuleType)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(specialPropertyName), specialPropertyName);
        Metadata.Add(nameof(expectedType), expectedType);
    }
}