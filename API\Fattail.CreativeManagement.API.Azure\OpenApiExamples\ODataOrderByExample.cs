﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Resolvers;
using Newtonsoft.Json.Serialization;

namespace Fattail.CreativeManagement.API.Azure.OpenApiExamples;

public class ODataOrderByExample : OpenApiExample<string>
{
    public override IOpenApiExample<string> Build (NamingStrategy namingStrategy = null)
    {
        Examples.Add(OpenApiExampleResolver.Resolve("$orderby",
            "name asc",
            namingStrategy));
        return this;
    }
}