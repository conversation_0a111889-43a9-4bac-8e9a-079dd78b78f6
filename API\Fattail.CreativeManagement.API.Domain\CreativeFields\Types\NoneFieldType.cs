using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class NoneFieldType : CreativeFieldType
{
    internal NoneFieldType () : base(nameof(CreativeFieldTypeEnum.None), (int)CreativeFieldTypeEnum.None)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return DefaultCreativeFieldSettings.Instance;
    }
}
