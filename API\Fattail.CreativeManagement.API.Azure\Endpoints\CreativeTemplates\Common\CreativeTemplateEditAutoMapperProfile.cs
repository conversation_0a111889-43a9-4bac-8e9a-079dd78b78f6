﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateEdit;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;

internal sealed class CreativeTemplateEditAutoMapperProfile : Profile
{
    internal const string CreativeTemplateEditCommandIdParameterName = "Id";

    internal const string CreativeTemplateEditCommandArchiveParameterName = "Archive";

    public CreativeTemplateEditAutoMapperProfile ()
    {
        CreateMap<CreativeTemplateEditRequest, CreativeTemplateEditCommand>()
            .ForCtorParam(CreativeTemplateEditCommandIdParameterName,
                paramOptions =>
                    paramOptions.MapFrom((src, context) => context.Items[CreativeTemplateEditCommandIdParameterName]))
            .ForCtorParam(CreativeTemplateEditCommandArchiveParameterName,
               paramOptions =>
                   paramOptions.MapFrom((src, context) => context.Items[CreativeTemplateEditCommandArchiveParameterName]));

    }
}