﻿using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.CreativeFileSanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;

internal static class DependencyInjection
{
    public static void AddSanitizers (this IServiceCollection services)
    {
        services.AddScoped<ISanitizerStrategyProvider, SanitizerStrategyProvider>();

        services.AddScoped<ISanitizer, FileUploadSanitizer>();
        services.AddScoped<ISanitizer, MultiFileUploadSanitizer>();
    }
}