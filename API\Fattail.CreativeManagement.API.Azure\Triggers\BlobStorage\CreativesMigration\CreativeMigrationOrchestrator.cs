using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Azure.Triggers.BlobStorage.CreativesMigration;

public class CreativeMigrationOrchestrator
{
    private readonly ILogger<CreativeMigrationOrchestrator> _logger;

    public CreativeMigrationOrchestrator (ILogger<CreativeMigrationOrchestrator> logger)
    {
        _logger = logger;
    }

    [Function(nameof(CreativeMigrationOrchestrator))]
    public async Task Run (
        [OrchestrationTrigger] TaskOrchestrationContext context)
    {
        ILogger safeLogger = context.CreateReplaySafeLogger(nameof(CreativeMigrationOrchestrator));
        CreativeMigrationOrchestratorModel? creativeMigrationOrchestratorModel =
            context.GetInput<CreativeMigrationOrchestratorModel>();
        safeLogger.LogTrace(
            "Creative migration:  Starting {Date}: Total creatives: {Creatives}",
            context.CurrentUtcDateTime, creativeMigrationOrchestratorModel.CreativeMigrations.Count());

        var adBookCreatives = creativeMigrationOrchestratorModel.CreativeMigrations
            .GroupBy(creative => new { creative.CreativeId, creative.CampaignId, creative.ClientId }).ToList();

        IEnumerable<AdBookCreativeMigrateModel> creativesList = adBookCreatives
            .Select(adBookCreative => new AdBookCreativeMigrateModel(adBookCreative.Key.CreativeId, adBookCreative));

        var migrateCreativesActivityModel =
            new MigrateCreativesActivityTriggerModel(creativeMigrationOrchestratorModel.OrgId, creativesList);

        List<MigrationResult> migrationResults = await context.CallActivityAsync<List<MigrationResult>>(nameof(MigrateCreativesActivityTrigger),
            migrateCreativesActivityModel);

        safeLogger.LogTrace("Creative migration: Finished {Date}: Total creatives: {Creatives}",
            context.CurrentUtcDateTime, creativeMigrationOrchestratorModel.CreativeMigrations.Count());

        await context.CallActivityAsync(nameof(StoreMigrationResultsActivityTrigger),
            new StoreMigrationResultsModel(creativeMigrationOrchestratorModel.OrgId,
                creativeMigrationOrchestratorModel.Name, migrationResults));
    }
}