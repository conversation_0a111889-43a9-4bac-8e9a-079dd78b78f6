﻿using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Repositories;

public interface ICreativeRepository : IRepository<Creative, long>
{
    Task<IReadOnlyList<TResult>> FindManyByCreativeTemplateIdAsync<TResult> (CreativeTemplateId id);
    
    Task<Result<Creative>> FindMigratedAdBookCreativeAsync (long adBookClientId, long adBookCampaignId, long adBookAdId);
    
    Task<Result> DeletePartitionAsync ();
    
    Task<Result> DeleteMigratedAsync ();
    
    Task<Result> UpdateCreativeTemplateNameReferenceAsync (CreativeTemplateId id, string creativeTemplateName, long orgId);

}