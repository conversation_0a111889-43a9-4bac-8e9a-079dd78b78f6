﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using FluentResults;

namespace Fattail.CreativeManagement.API.Azure.Common.Configuration.AutoMapper.TypeConverters;

internal sealed class
    ResultTypeConverter<TResult, TResponse> : ITypeConverter<Result<TResult>, ResultResponse<TResponse>>
{
    public ResultResponse<TResponse> Convert (Result<TResult> source, ResultResponse<TResponse> destination,
        ResolutionContext context)
    {
        if (source == null)
        {
            return null;
        }

        if (source.IsSuccess)
        {
            return new ResultResponse<TResponse>(context.Mapper.Map<TResponse>(source.Value));
        }

        return new ResultResponse<TResponse>(default,
            context.Mapper.Map<IReadOnlyList<ErrorInformation>>(source.Errors), source.IsSuccess);
    }
}