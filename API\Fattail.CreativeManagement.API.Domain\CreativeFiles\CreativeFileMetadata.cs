﻿using Fattail.CreativeManagement.API.Domain.Common;
using System.Collections.Immutable;
using System.Collections.ObjectModel;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;

public sealed class CreativeFileMetadata : ValueObject
{
    public static readonly CreativeFileMetadata Empty = new(new ReadOnlyDictionary<string, string>(new Dictionary<string, string>().ToImmutableDictionary()));

    private readonly IReadOnlyDictionary<string, string> _metadata;

    internal CreativeFileMetadata (IReadOnlyDictionary<string, string> metadata)
    {
        _metadata = metadata;
    }

    public string this[string key] => _metadata[key];
    
    public static implicit operator Dictionary<string, string> (CreativeFileMetadata metadata)
    {
        return new Dictionary<string, string>(metadata._metadata.ToImmutableDictionary());
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return _metadata;
    }
}
