﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DatabaseAndContainerSetupService;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.DevAndTestEnvDataPopulations;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation
{
    [ExcludeFromCodeCoverage]
    public static class DependencyInjection
    {
        public static void AddDataPopulation (this IServiceCollection services)
        {
            services.AddOptions<CosmosDbSettings>()
            .Configure<IConfiguration>((settings, configuration)
                => configuration.GetSection("ConnectionStrings:CMS").Bind(settings));

            services.AddTransient<IDataPopulationService, DataPopulationService>();
            services.AddTransient<IClientSpecificDataPopulationService, ClientSpecificDataPopulationService>();
            services.AddTransient<IDatabaseAndContainerSetupService, DatabaseAndContainerSetupService.DatabaseAndContainerSetupService>();
            services.AddTransient<IDevAndTestEnvDataPopulationService, DevAndTestEnvDataPopulationService>();
            services.AddTransient<IDataPopulationRepository<CreativeField>, CreativeFieldDataPopulationRepository>();
            services.AddTransient<IDataPopulationRepository<CreativeTemplate>, CreativeTemplateDataPopulationRepository>();
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly));
        }
    }
}
