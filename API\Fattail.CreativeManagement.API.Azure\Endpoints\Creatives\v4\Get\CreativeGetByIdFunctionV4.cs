﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives.GetById;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Get;

[ExcludeFromCodeCoverage]
public class CreativeGetByIdFunctionV4 : BaseFunction
{
    public CreativeGetByIdFunctionV4 (IMediator mediator, IMapper mapper): base(mediator, mapper)
    {
    }

    [OpenApiOperation("Creative Get By Id v4", "v4", Summary = "Get creative by Id v4")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV3))]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<Error>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound)]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeGetByIdFunctionV4))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "v4/creatives/{id:long}")]
        HttpRequestData req,
        long id)
    {
        var getByIdQuery = new CreativeGetByIdQuery(id.ToString());

        CreativeQueryResult? getByIdResult = await _mediator.Send(getByIdQuery);

        return await FromSingleQueryResult<CreativeQueryResult, CreativeResponseV4>(req, getByIdResult);
    }
}