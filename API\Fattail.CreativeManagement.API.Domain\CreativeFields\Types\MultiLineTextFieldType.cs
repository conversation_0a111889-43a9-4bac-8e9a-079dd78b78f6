using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class MultiLineTextFieldType : CreativeFieldType
{
    internal MultiLineTextFieldType () : base(nameof(CreativeFieldTypeEnum.MultiLineText), (int)CreativeFieldTypeEnum.MultiLineText)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return DefaultCreativeFieldSettings.Instance;
    }
}
