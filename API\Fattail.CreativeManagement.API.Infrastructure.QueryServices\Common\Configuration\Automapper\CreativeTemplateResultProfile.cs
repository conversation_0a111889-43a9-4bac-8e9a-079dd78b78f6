﻿using AutoMapper;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common.Configuration.AutoMapper;

internal sealed class CreativeTemplateResultProfile : Profile
{
    public CreativeTemplateResultProfile ()
    {
        CreateMap<CreativeTemplate, CreativeTemplateQueryResult>();
        CreateMap<ValidationRule, ValidationRuleQueryResult>();
        CreateMap<CreativeTemplateCreativeField, CreativeTemplateCreativeFieldQueryResult>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeField), typeof(CreativeTemplateMultiSelectCreativeFieldQueryResult))
            .Include(typeof(CreativeTemplateSingleSelectCreativeField), typeof(CreativeTemplateSingleSelectCreativeFieldQueryResult))
            .Include(typeof(CreativeTemplateSectionDividerCreativeField), typeof(CreativeTemplateSectionDividerCreativeFieldQueryResult));

        CreateMap<CreativeTemplateSelectOption, SelectOptionQueryResult>();
        CreateMap<CreativeTemplateMultiSelectCreativeField, CreativeTemplateMultiSelectCreativeFieldQueryResult>();
        CreateMap<CreativeTemplateSingleSelectCreativeField, CreativeTemplateSingleSelectCreativeFieldQueryResult>();
        CreateMap<CreativeTemplateSectionDividerCreativeField, CreativeTemplateSectionDividerCreativeFieldQueryResult>();
    }
}