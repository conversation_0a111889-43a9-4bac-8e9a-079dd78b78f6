using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.Creatives;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates.CreativeTemplateDelete;

public class CreativeTemplateInUseVerifier : ICreativeTemplateInUseVerifier
{
    private readonly ICreativeRepository _creativeRepository;

    public CreativeTemplateInUseVerifier (ICreativeRepository creativeRepository)
    {
        _creativeRepository = creativeRepository;
    }

    public async Task<bool> IsTemplateInUse (long creativeTemplateId)
    {
        //TODO: Explore a better solution in order to avoid high RUs calls when db grows
        IReadOnlyList<CreativeResult>? creativeResultItems = await _creativeRepository.FindManyByCreativeTemplateIdAsync<CreativeResult>(new CreativeTemplateId(creativeTemplateId));

        return creativeResultItems.Any();
    }
}