﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeSetLineItems;

internal sealed class CreativeSetLineItemsHandler (
    ICreativeRepository creativeRepository,
    IDateTimeProvider dateTimeProvider,
    IAdBookWorkflowHttpClient adBookWorkflowHttpClient)
    : IRequestHandler<CreativeSetLineItemsCommand, Result<CreativeResult>>
{
    public async Task<Result<CreativeResult>> Handle (CreativeSetLineItemsCommand request, CancellationToken cancellationToken)
    {
        Creative? creative = await creativeRepository.FindByIdAsync(request.Id);

        if (creative is null)
        {
            return Result.Fail(new EntityNotFoundError(request.Id.ToString(), nameof(Creative)));
        }

        Result<UpdateInformation> createUpdateInformationResult = UpdateInformation.Create(request.UpdatedBy, dateTimeProvider.CurrentTime);

        if (createUpdateInformationResult.IsFailed)
        {
            return createUpdateInformationResult.ToResult();
        }
        
        Result<LineItemSetResult> lineItemsSetResult = creative.SetLineItemAssignments(request.LineItemIds, createUpdateInformationResult.Value);
        
        if (lineItemsSetResult.IsFailed)
        {
            return lineItemsSetResult.ToResult();
        }

        CreativeResult updateCreativeResult = await creativeRepository.UpdateAsync<CreativeResult>(creative);

        if (lineItemsSetResult.Value.AssignedLineItemIds.Any())
        { 
            _ = Task.Run(() => adBookWorkflowHttpClient.NotifyLineItemAssignment( 
                creative.Id,
                creative.CampaignId,
                lineItemsSetResult.Value.AssignedLineItemIds), cancellationToken);
        }

        return Result.Ok(updateCreativeResult);
    }
}
