﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates;
public sealed class CreativeTemplateName : ValueObject
{
    private readonly string _value;

    private CreativeTemplateName (string value)
    {
        Guard.Argument(value).NotNull().NotEmpty();

        _value = value;
    }

    public static Result<CreativeTemplateName> Create (
        string? name,
        CreativeTemplateUniqueNameRequirement creativeTemplateUniqueNameRequirement)
    {
        if (string.IsNullOrWhiteSpace(name))
        {
            return Result.Fail(new RequiredValueMissingError("creative template name", nameof(CreativeTemplate)));
        }

        return creativeTemplateUniqueNameRequirement.IsSatisfied
            ? new CreativeTemplateName(name.Trim())
            : Result.Fail(new CreativeTemplateNameInUseError("creative template name", nameof(CreativeTemplate)));
    }

    public override string ToString ()
    {
        return _value;
    }

    public static implicit operator string (CreativeTemplateName creativeTemplateName)
    {
        return creativeTemplateName._value;
    }

    public static explicit operator CreativeTemplateName (string name)
    {
        return new CreativeTemplateName(name);
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return _value;
    }
}
