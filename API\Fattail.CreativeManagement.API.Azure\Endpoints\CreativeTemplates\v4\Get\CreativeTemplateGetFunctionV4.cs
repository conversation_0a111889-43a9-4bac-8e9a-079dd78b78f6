﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v2;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Azure.OpenApiExamples;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.Get;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v4.Get;

public class CreativeTemplateGetFunctionV4 (IMediator mediator, IMapper mapper) : BaseFunction(mediator, mapper)
{
    [OpenApiOperation(
        "Creative templates Get v4",
        tags: new[] { "v4" },
        Summary = "Get a list of creative templates v4. Supports the specified OData v4.0 expressions.")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("$filter",
        Description =
            "OData [$filter](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$filter_System) expression.",
        In = ParameterLocation.Query, Example = typeof(ODataFilterExample))]
    [OpenApiParameter("$orderBy",
        Description =
            "OData [$orderby](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$orderby_System) expression.",
        In = ParameterLocation.Query, Example = typeof(ODataOrderByExample))]
    [OpenApiParameter("$top",
        Description =
            "Number of items to be included in the result. Follow [$top](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$top_System_1) for more information.",
        In = ParameterLocation.Query, Example = typeof(ODataTopExample))]
    [OpenApiParameter("$skip",
        Description =
            "Number of items to be skipped in the result. Follow [$skip](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$skip_System) for more information.",
        In = ParameterLocation.Query, Example = typeof(ODataSkipExample))]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(List<CreativeTemplateResponse>))]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeTemplateGetFunctionV4))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "v4/creative-templates")]
        HttpRequestData req)
    {
        CreativeTemplateGetQuery creativeTemplateGetQuery = await ODataQueryFromRequest<CreativeTemplateGetQuery>(req);
        
        QueryResult<CreativeTemplateQueryResult> creativeTemplateQueryResult =
            await _mediator.Send(creativeTemplateGetQuery);

        return await FromQueryResult<CreativeTemplateQueryResult, CreativeTemplateResponse>(req,
            creativeTemplateQueryResult);
    }
}