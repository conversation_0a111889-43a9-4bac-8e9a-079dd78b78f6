using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;

internal sealed record CreativeResponseV4
{
    [property: OpenApiProperty(Description = "Creative id")]
    public long Id { get; init; }

    [property: OpenApiProperty(Description = "Creative template id")]
    public long CreativeTemplateId { get; init; }

    [property: OpenApiProperty(Description = "Creative template name")]
    public string CreativeTemplateName { get; init; }

    [property: OpenApiProperty(Description = "Creative name")]
    public string? Name { get; init; }

    [property: OpenApiProperty(Description = "AdBook Client id")]
    public long? AdBookClientId { get; init; }

    [property: OpenApiProperty(Description = "Campaign id")]
    public long CampaignId { get; init; }
    
    [property: OpenApiProperty(Description = "Line item ids")]
    public IReadOnlyList<long> LineItemIds { get; init; }

    [property: OpenApiProperty(Description = "Creative fields")]
    public IReadOnlyList<CreativeFieldValueResponse> Fields { get; init; }

    [property: OpenApiProperty(Description = "Creative status")]
    public CreativeStatusResponse Status { get; set; }

    [property: OpenApiProperty(Description = "Last time the creative was updated")]
    public DateTime LastUpdatedOn { get; init; }

    [property: OpenApiProperty(Description = "Creative updated by")]
    public string? LastUpdatedBy { get; init; }

    [property: OpenApiProperty(Description = "Last approval")]
    public ApprovalInformationResponse LastApproval { get; set; }
}

internal record CreativeStatusResponse
{
    [property: OpenApiProperty(Description = "Creative status value")]
    public CreativeStatusEnum Value { get; init; }
    
    [property: OpenApiProperty(Description = "Creative status description")]
    public string Description { get; init; }
}

internal record ApprovalInformationResponse
{
    [property: OpenApiProperty(Description = "Last approver name")]
    public string ApproverName { get; init; }
    
    [property: OpenApiProperty(Description = "Last approval date and time")]
    public DateTime ApprovalDateTime { get; init; }
}