﻿using Ardalis.SmartEnum;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;

public abstract class CreativeFileType : SmartEnum<CreativeFileType>
{
    public static readonly CreativeFileType Other = new OtherFileType();
    public static readonly CreativeFileType Image = new ImageFileType();

    protected CreativeFileType(string name, int value) : base(name, value)
    {
        EnumType = (CreativeFileTypeEnum)Value;
    }

    public CreativeFileTypeEnum EnumType { get; }

    protected abstract bool MatchesExtension (FileExtension extension);

    public abstract CreativeFileMetadata GenerateMetadata (Stream creativeFileStream);

    public static CreativeFileType FromExtension (FileExtension extension)
    {
        return List.FirstOrDefault(creativeFileType => creativeFileType.MatchesExtension(extension)) ?? Other;
    }
}