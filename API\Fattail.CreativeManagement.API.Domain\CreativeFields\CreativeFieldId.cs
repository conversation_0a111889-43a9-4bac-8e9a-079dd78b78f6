﻿using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields;

public sealed class CreativeFieldId (long? creativeFieldId) : ValueObject
{
    public static readonly CreativeFieldId Transient = new CreativeFieldId(null);

    private readonly long _value = creativeFieldId ?? 0;

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return _value;
    }

    public override string ToString ()
    {
        return _value.ToString();
    }

    public static implicit operator long (CreativeFieldId creativeFieldId)
    {
        return creativeFieldId._value;
    }

    public static explicit operator CreativeFieldId (long? creativeFieldId)
    {
        return new CreativeFieldId(creativeFieldId);
    }

    public static explicit operator CreativeFieldId (string creativeFieldId)
    {
        return new CreativeFieldId(long.Parse(creativeFieldId));
    }
}