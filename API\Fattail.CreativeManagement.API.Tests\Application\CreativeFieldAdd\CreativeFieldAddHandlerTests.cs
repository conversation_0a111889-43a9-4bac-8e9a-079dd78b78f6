﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;
using CreativeField = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeField;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeFieldAdd;

[TestFixture]
public class CreativeFieldAddHandlerTests
{
    private CreativeFieldAddHandler _creativeFieldAddHandler = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFieldRepository> _creativeFieldRepositoryMock = null!;
    private Mock<ICreativeFieldCreationFactory> _creativeFieldCreationFactoryMock = null!;

    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));

        _creativeFieldRepositoryMock = new Mock<ICreativeFieldRepository>();
        _creativeFieldCreationFactoryMock = new Mock<ICreativeFieldCreationFactory>();
    }

    [Test]
    public async Task Invalid_creative_field_is_not_persisted ()
    {
        CreativeFieldAddCommand addFieldCommand = new AutoFaker<CreativeFieldAddCommand>()
            .Generate();

        _creativeFieldCreationFactoryMock.Setup(creativeFieldRepositoryMock =>
            creativeFieldRepositoryMock.CreateField(_idManagerMock.Object, _creativeFieldRepositoryMock.Object,
                addFieldCommand)).ReturnsAsync(Result.Fail<CreativeField>("Error"));

        _creativeFieldAddHandler = new CreativeFieldAddHandler(
            _creativeFieldRepositoryMock.Object,
            _idManagerMock.Object,
            _creativeFieldCreationFactoryMock.Object
        );

        Result<CreativeFieldResult> result =
            await _creativeFieldAddHandler.Handle(addFieldCommand, CancellationToken.None);

        result.Should().BeFailure();
        _creativeFieldRepositoryMock.Verify(
            creativeFieldRepository =>
                creativeFieldRepository.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()),
            Times.Never());
    }

    [Test]
    public async Task Valid_creative_field_is_persisted ()
    {
        CreativeFieldAddCommand addFieldCommand = new AutoFaker<CreativeFieldAddCommand>()
            .Generate();

        _creativeFieldCreationFactoryMock.Setup(creativeFieldRepositoryMock =>
            creativeFieldRepositoryMock.CreateField(_idManagerMock.Object, _creativeFieldRepositoryMock.Object,
                addFieldCommand)).ReturnsAsync(new Result<CreativeField>());

        _creativeFieldAddHandler = new CreativeFieldAddHandler(
            _creativeFieldRepositoryMock.Object,
            _idManagerMock.Object,
            _creativeFieldCreationFactoryMock.Object
        );

        Result<CreativeFieldResult> result =
            await _creativeFieldAddHandler.Handle(addFieldCommand, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeFieldRepositoryMock.Verify(
            creativeFieldRepository =>
                creativeFieldRepository.CreateAsync<CreativeFieldResult>(It.IsAny<CreativeField>()),
            Times.Once());
    }
}