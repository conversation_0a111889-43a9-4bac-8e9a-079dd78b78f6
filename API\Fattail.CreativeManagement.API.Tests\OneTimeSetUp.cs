﻿using AutoBogus;
using AutoBogus.Conventions;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests;

[SetUpFixture]
public class OneTimeSetUp
{
    [OneTimeSetUp]
    public void RunBeforeAnyTests ()
    {
        AutoFaker.Configure(builder =>
        {
            builder.WithConventions();
        });

        FluentResultAssertionsConfig.MessageComparison = (_, _) => true;
    }
}