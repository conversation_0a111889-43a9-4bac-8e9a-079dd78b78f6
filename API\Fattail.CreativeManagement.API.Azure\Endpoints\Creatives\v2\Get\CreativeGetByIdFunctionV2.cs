﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives.GetById;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.Get;

[ExcludeFromCodeCoverage]
public class CreativeGetByIdFunctionV2 : BaseFunction
{
    public CreativeGetByIdFunctionV2 (IMediator mediator, IMapper mapper): base(mediator, mapper)
    {
    }

    [OpenApiOperation("Creative Get By Id v2", new[] { "v2" },
        Deprecated = true,
        Summary = "Get creative by Id v2")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV2))]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<Error>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound)]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeGetByIdFunctionV2))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "v2/creatives/{id:long}")]
        HttpRequestData req,
        long id)
    {
        var getByIdQuery = new CreativeGetByIdQuery(id.ToString());

        CreativeQueryResult? getByIdResult = await _mediator.Send(getByIdQuery);

        return await FromSingleQueryResult<CreativeQueryResult, CreativeResponseV2>(req, getByIdResult);
    }
}