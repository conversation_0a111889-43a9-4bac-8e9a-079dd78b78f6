﻿using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields.ValidationRuleValidatorStrategy;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

public static class ValidationRuleValidationStrategyFactory
{
    internal static IValidationRuleValidatorStrategy Create (CreativeFieldValidationRuleType type)
    {
        return type switch
        {
            CreativeFieldValidationRuleType.FileSize => new FileSizeValidationRuleValidatorStrategy(),
            CreativeFieldValidationRuleType.FileUploadExtensions => new FileUploadExtensionsValidationRuleValidatorStrategy(),
            _ => new BaseValidationRuleValidatorStrategy()
        };
    }
}