﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeApprove;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.ApproveCreative;

public sealed class CreativeApproveFunctionV4 (
    IMediator mediator,
    IMapper mapper) : BaseFunction(mediator, mapper)
{
    [OpenApiOperation(
        "Approve an existing creative v4", tags: new[] { "v4" },
        Summary = "Approve an existing creative v4")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeApproveRequestV4),
        Description = "The creative to approve", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV4))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound, Description = "If the creative doesn't exist.")]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeApproveFunctionV4))]
    public async Task<HttpResponseData?> ApproveCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v4/creatives/{id:long}/approve")]
        HttpRequestData req,
        long id)
    {
        CreativeApproveCommand creativeApproveCommand = await FromRequest<CreativeApproveRequestV4, CreativeApproveCommand>(req,
            new Dictionary<string, object>
            {
                { CreativeApproveV4AutoMapperProfile.CreativeApproveCommandIdParameterName, id }
            });
        
        Result<CreativeResult> result = await _mediator.Send(creativeApproveCommand);
        
        HttpResponseData response =
            await FromResult<CreativeResult, CreativeResponseV4>(req, result);
        
        return response;
    }
}