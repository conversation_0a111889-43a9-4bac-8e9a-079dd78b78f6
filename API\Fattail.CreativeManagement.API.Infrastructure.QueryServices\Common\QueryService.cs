﻿using AutoMapper;
using Community.OData.Linq;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using System.Net;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;

internal abstract class QueryService<TEntity> : IContainerContext where TEntity : Entity
{
    private readonly IConfigurationProvider _configurationProvider;
    private readonly Container _container;
    private readonly IMapper _mapper;
    protected readonly IOrganizationContext _organizationContext;

    public QueryService (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IConfigurationProvider configurationProvider,
        IOrganizationContext organizationContext,
        IMapper mapper)
    {
        _container = cosmosDbContainerFactory.GetContainer(ContainerName).Container;
        _configurationProvider = configurationProvider;
        _organizationContext = organizationContext;
        _mapper = mapper;
    }

    public abstract string ContainerName { get; }

    public abstract PartitionKey ResolvePartitionKey ();

    public async Task<TProjection?> FindByIdAsync<TProjection> (string id)
    {
        try
        {
            ItemResponse<TEntity>? itemResponse = await _container.ReadItemAsync<TEntity>(id, ResolvePartitionKey());
            return _mapper.Map<TProjection>(itemResponse.Resource);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return default;
        }
    }

    public async Task<QueryResult<TProjection>> GetFrom<TProjection> (ODataQuery query)
    {
        var items = new List<TProjection>();
        (string? filter, string? orderBy, string? top, string? skip) = query;
        try
        {
            ODataQuery<TEntity> oDataQuery = _container.GetItemLinqQueryable<TEntity>(
                    requestOptions: new QueryRequestOptions
                    {
                        PartitionKey = ResolvePartitionKey(), MaxItemCount = int.MaxValue
                    },
                    linqSerializerOptions: new CosmosLinqSerializerOptions()
                    {
                        PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                    })
                .OData();

            if (!string.IsNullOrWhiteSpace(filter))
            {
                oDataQuery = oDataQuery.Filter(filter);
            }

            Response<int> countItems = await oDataQuery.CountAsync();
            int totalCount = countItems.Resource;

            if (!string.IsNullOrWhiteSpace(orderBy))
            {
                oDataQuery = oDataQuery.OrderBy(orderBy);
            }

            if (!string.IsNullOrWhiteSpace(top) ||
                !string.IsNullOrWhiteSpace(skip))
            {
                oDataQuery = oDataQuery
                    .TopSkip(top, skip);
            }

            IQueryable<TEntity> cosmosDbQuery = oDataQuery.ToOriginalQuery();

            using (var resultSet = cosmosDbQuery.ToFeedIterator())
            {
                while (resultSet.HasMoreResults)
                {
                    FeedResponse<TEntity> response = await resultSet.ReadNextAsync();
                    items.AddRange(_mapper.Map<IReadOnlyList<TProjection>>(response.Resource));
                }
            }

            return new QueryResult<TProjection>(items, (uint)totalCount);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return new QueryResult<TProjection>(items, 0);
        }
    }
}