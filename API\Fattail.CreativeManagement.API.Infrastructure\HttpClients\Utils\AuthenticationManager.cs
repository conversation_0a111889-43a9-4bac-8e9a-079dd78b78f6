﻿using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Models;

namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.Utils;

internal class AuthenticationManager
{
    private readonly IAuthenticationHttpClient _authenticationHttpClient;
    private ActiveJwt? _activeJwt;

    public AuthenticationManager (IAuthenticationHttpClient authenticationHttpClient)
    {
        _authenticationHttpClient = authenticationHttpClient;
    }

    public async Task<string> GetActiveJwt ()
    {
        if (_activeJwt == null || _activeJwt.IsExpired())
        {
            Jwt jwt = await _authenticationHttpClient.LoginAsync();
            _activeJwt = new ActiveJwt(jwt.AccessToken, jwt.ExpiresIn);
        }

        return _activeJwt.AccessToken;
    }
}