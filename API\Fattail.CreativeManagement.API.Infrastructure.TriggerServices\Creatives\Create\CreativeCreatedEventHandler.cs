﻿using Fattail.CreativeManagement.API.Application;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives.Create;
internal sealed class CreativeCreatedEventHandler : INotificationHandler<CreativeCreatedEvent>
{
    private readonly ILogger<CreativeCreatedEventHandler> _logger;
    private readonly IAdBookWorkflowHttpClient _adBokWorkflowHttpClient;

    public CreativeCreatedEventHandler (
        ILogger<CreativeCreatedEventHandler> logger,
        IAdBookWorkflowHttpClient adBokWorkflowHttpClient)
    {
        _logger = logger;
        _adBokWorkflowHttpClient = adBokWorkflowHttpClient;
    }

    public async Task Handle (CreativeCreatedEvent creativeCreatedEvent, CancellationToken cancellationToken)
    {
        await _adBokWorkflowHttpClient.NotifyCampaignCreativeCreation(creativeCreatedEvent.CreativeId, creativeCreatedEvent.CampaignId);
    }
}
