﻿using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

public abstract class ErrorBase : Error
{
    public const string ErrorCodeKey = "Code";
    public const string ErrorTypeKey = "Type";

    protected ErrorBase (string message, ErrorType errorType)
        : base(message)
    {
        Metadata.Add(ErrorTypeKey, errorType.ToString());
        Metadata.Add(ErrorCodeKey, (int)errorType);
    }
}