using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesGenerateZip;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFiles.v2.CreativeFilesGenerateZip;

[ExcludeFromCodeCoverage]
public class CreativeFilesGenerateZipFunctionV2 : BaseFunction
{
    public CreativeFilesGenerateZipFunctionV2 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Creative Files Generate Zip v2",
        tags: new[] { "v2" },
        Deprecated = true,
        Summary = "Creative files Generate Zip file v2",
        Description = "Creative files Generate Zip file v2")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody("application/json", typeof(List<long>), Description = "List of creative file ids")]
    [OpenApiResponseWithoutBody(HttpStatusCode.NoContent,
        Description = "Zip file generated correctly, location header contains azure file url")]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<string>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeFilesGenerateZipFunctionV2))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v2/creative-files/generate-zip")]
        HttpRequestData req)
    {
        IReadOnlyList<CreativeFileId> creativeFieldValueIds =
            await req.ReadFromJsonAsync<IReadOnlyList<CreativeFileId>>();

        var creativeFieldGenerateZipCommand = new CreativeFilesGenerateZipCommand(creativeFieldValueIds);

        Result<CreativeFilesGenerateZipResult> result = await _mediator.Send(creativeFieldGenerateZipCommand);

        if (result.IsFailed)
        {
            HttpResponseData badRequestResponse = req.CreateResponse(HttpStatusCode.BadRequest);
            await badRequestResponse.WriteAsJsonAsync(_mapper.Map<IReadOnlyList<ErrorInformation?>>(result.Errors));
            return badRequestResponse;
        }

        HttpResponseData noContentResponse = req.CreateResponse(HttpStatusCode.NoContent);
        noContentResponse.Headers.Add("location", result.Value?.Location);
        return noContentResponse;
    }
}