﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields.ValidationRuleValidatorStrategy;

public class BaseValidationRuleValidatorStrategy : IValidationRuleValidatorStrategy
{
    public Result IsValid (CreativeFieldId creativeFieldId, CreativeFieldValidationRuleType type,
        IReadOnlyList<string> options)
    {
        return Result.Ok();
    }
}