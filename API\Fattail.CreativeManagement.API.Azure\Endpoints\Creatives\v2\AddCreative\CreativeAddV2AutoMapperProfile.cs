﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.AddCreative;

internal sealed class CreativeAddV2AutoMapperProfile : Profile
{
    public CreativeAddV2AutoMapperProfile ()
    {
        CreateMap<CreativeAddRequestV2, CreativeAddCommand>()
            .ForCtorParam("AdBookClientId", paramOptions =>
                paramOptions.MapFrom(src => (long?)null));
    }
}