﻿using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;
public class FileExtension : ValueObject
{
    private readonly string _value;

    internal FileExtension(string extension)
    {
        _value = extension;
    }

    public static FileExtension From (string fileName)
    {
        return new FileExtension(Path.GetExtension(fileName));
    }

    public override string ToString ()
    {
        return _value;
    }

    public static implicit operator string (FileExtension fileExtension)
    {
        return fileExtension._value;
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return _value.ToLower();
    }
}
