﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.Common;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Azure.OpenApiExamples;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives.Get;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Diagnostics.CodeAnalysis;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Get;

[ExcludeFromCodeCoverage]
public class CreativeGetFunctionV4 : BaseFunction
{

    public CreativeGetFunctionV4 (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation("Creative Get v4", "v4",
        Summary = "Get a list of creatives preview endpoint v4. Supports the specified OData v4.0 expressions."
    )]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("$filter",
        Description =
            "OData [$filter](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$filter_System) expression.",
        In = ParameterLocation.Query, Example = typeof(ODataFilterExample))]
    [OpenApiParameter("$orderBy",
        Description =
            "OData [$orderby](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$orderby_System) expression.",
        In = ParameterLocation.Query, Example = typeof(ODataOrderByExample))]
    [OpenApiParameter("$top",
        Description =
            "Number of items to be included in the result. Follow [$top](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$top_System_1) for more information.",
        In = ParameterLocation.Query, Example = typeof(ODataTopExample))]
    [OpenApiParameter("$skip",
        Description =
            "Number of items to be skipped in the result. Follow [$skip](http://docs.oasis-open.org/odata/odata/v4.0/errata03/os/complete/part1-protocol/odata-v4.0-errata03-os-part1-protocol-complete.html#_The_$skip_System) for more information.",
        In = ParameterLocation.Query, Example = typeof(ODataSkipExample))]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(List<CreativeResponseV3>))]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<string>))]
    [Function(nameof(CreativeGetFunctionV4))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "v4/creatives")]
        HttpRequestData req)
    {
        CreativeGetQuery creativeGetQuery = await ODataQueryFromRequest<CreativeGetQuery>(req);

        QueryResult<CreativeQueryResult> creativeQueryResult = await _mediator.Send(creativeGetQuery);

        return await FromQueryResult<CreativeQueryResult, CreativeResponseV4>(req, creativeQueryResult);
    }
}