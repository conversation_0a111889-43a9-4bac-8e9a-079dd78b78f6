﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using CreativeField = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeField;
using SelectOption = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.SelectOption;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeFields;

public class MigrationCreativeFieldList
{
    public readonly List<CreativeField> CreativeFields;
    private const string CreativeFieldFileId = "574650397348901";
    private const string CreativeFieldDestinationUrlId = "574650397348902";
    private const string CreativeFieldForDigitalProductsId = "574650397348903";
    private const string CreativeFieldCommentsId = "574650397348904";
    private const string ApplyCreativeToFuturePlacementFieldOptionId = "1";

    public MigrationCreativeFieldList (List<string> orgIdList)
    {
        CreativeFields = new List<CreativeField> { };

        foreach (string orgid in orgIdList)
        {
            CreativeFields.Add(
                new CreativeField
                {
                    OrgId = orgid,
                    Id = CreativeFieldFileId,
                    Name = "File",
                    Type = CreativeFieldTypeEnum.MultiFileUpload
                });

            CreativeFields.Add(
                new CreativeField
                {
                    OrgId = orgid,
                    Id = CreativeFieldDestinationUrlId,
                    Name = "Destination URL",
                    Type = CreativeFieldTypeEnum.SingleLineText
                });
            CreativeFields.Add(
                new CreativeField
                {
                    OrgId = orgid,
                    Id = CreativeFieldForDigitalProductsId,
                    Name = "For Digital products only - Apply creative to future placement as applicable",
                    Type = CreativeFieldTypeEnum.MultiSelectOption,
                    Options = new[]
                    {
                        new SelectOption
                        {
                            Description = "Yes", Id = ApplyCreativeToFuturePlacementFieldOptionId
                        }
                    }
                });
            CreativeFields.Add(
                new CreativeField
                {
                    OrgId = orgid,
                    Id = CreativeFieldCommentsId,
                    Name = "Comments",
                    Type = CreativeFieldTypeEnum.MultiLineText
                });
        }

    }
}