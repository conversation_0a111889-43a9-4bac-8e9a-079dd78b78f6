﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields
{
    public class CreativeFieldDataPopulationRepository : DataPopulationRepository<CreativeField>
    {
        public CreativeFieldDataPopulationRepository (ICosmosDbContainerFactory cosmosDbContainerFactory,
        IOrganizationContext organizationContext) :
        base(cosmosDbContainerFactory, organizationContext)
        {
        }

        public override string ContainerName => "CreativeFields";

        public override PartitionKey ResolvePartitionKey ()
        {
            return new PartitionKey($"{_organizationContext.OrganizationId}");
        }
    }
}
