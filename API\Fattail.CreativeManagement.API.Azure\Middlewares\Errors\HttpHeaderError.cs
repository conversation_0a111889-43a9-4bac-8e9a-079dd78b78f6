﻿using FluentResults;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Fattail.CreativeManagement.API.Azure.Middlewares.Errors;

internal class HttpHeaderError : Error
{
    public HttpHeaderError (string headerName) : base($"Required header is missing or contains an invalid value. Header: '{headerName}'")
    {
        Metadata.Add("Code", ApiErrorType.RequiredHeaderIsWrongOrMissing);
        Metadata.Add("Type", ApiErrorType.RequiredHeaderIsWrongOrMissing.ToString());
    }
}
