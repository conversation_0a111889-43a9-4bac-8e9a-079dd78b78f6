﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using System.Net;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;

internal abstract class EventHandlerService : IContainerContext
{
    internal readonly Container _container;
    protected readonly IOrganizationContext _organizationContext;

    public EventHandlerService (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IOrganizationContext organizationContext)
    {
        _container = cosmosDbContainerFactory.GetContainer(ContainerName).Container;
        _organizationContext = organizationContext;
    }

    public abstract string ContainerName { get; }

    public abstract PartitionKey ResolvePartitionKey ();
}