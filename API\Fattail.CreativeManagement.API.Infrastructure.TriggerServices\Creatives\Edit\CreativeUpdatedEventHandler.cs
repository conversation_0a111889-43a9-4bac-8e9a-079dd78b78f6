﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.Settings;
using Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives.Events;
using MediatR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Fattail.CreativeManagement.API.Infrastructure.TriggerServices.Creatives.Edit;

internal sealed class CreativeUpdatedEventHandler : INotificationHandler<CreativeUpdatedEvent>
{
    private readonly ILogger<CreativeUpdatedEventHandler> _logger;
    private readonly ICreativeRepository _creativeRepository;
    private readonly ICreativeFileRepository _creativeFileRepository;
    private readonly ParallelExecutionSettings _parallelExecutionSettings;

    public CreativeUpdatedEventHandler (
        ILogger<CreativeUpdatedEventHandler> logger,
        ICreativeRepository creativeRepository,
        ICreativeFileRepository creativeFileRepository,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings)
    {
        _logger = logger;
        _creativeRepository = creativeRepository;
        _creativeFileRepository = creativeFileRepository;
        _parallelExecutionSettings = parallelExecutionSettings.Value;
    }

    public async Task Handle (CreativeUpdatedEvent creativeUpdatedEvent, CancellationToken cancellationToken)
    {
        Creative? creative = await _creativeRepository.FindByIdAsync(creativeUpdatedEvent.CreativeId);

        if (creative is not null)
        {
            IEnumerable<CreativeFieldValue> creativeFileFields = creative.Fields.Where(f =>
                f.CreativeFieldIdentifier.Type.EnumType is CreativeFieldTypeEnum.MultiFileUpload or CreativeFieldTypeEnum.FileUpload);

            foreach (CreativeFieldValue creativeFileField in creativeFileFields)
            {
                switch (creativeFileField.CreativeFieldIdentifier.Type.EnumType)
                {
                    case CreativeFieldTypeEnum.MultiFileUpload:
                        var multiFileUploadFieldValue = (MultiFileUploadFieldValue)await creative.GetCreativeFieldValue(
                            CreativeFieldIdentifier.Create(
                                new CreativeFieldId(creativeFileField.CreativeFieldIdentifier.Id),
                                CreativeFieldType.MultiFileUpload));
                        await Parallel.ForEachAsync(multiFileUploadFieldValue.Value,
                            new ParallelOptions { MaxDegreeOfParallelism = _parallelExecutionSettings.MaxDegreeOfParallelismForCreation }, async (creativeFileId, _) =>
                            {
                                CreativeFile? creativeFile =
                                    await _creativeFileRepository.FindByIdAsync(new CreativeFileId(creativeFileId));

                                if (creativeFile is not null)
                                {
                                    await _creativeFileRepository.UpdateAsync<CreativeFilesUploadResult>(creativeFile);
                                }
                            });

                        break;
                    case CreativeFieldTypeEnum.FileUpload:
                        var fileUploadFieldValue = (FileUploadFieldValue)await creative.GetCreativeFieldValue(
                            CreativeFieldIdentifier.Create(
                                new CreativeFieldId(creativeFileField.CreativeFieldIdentifier.Id),
                                CreativeFieldType.FileUpload));

                        CreativeFile? singleCreativeFile =
                            await _creativeFileRepository.FindByIdAsync(new CreativeFileId(fileUploadFieldValue.Value));

                        if (singleCreativeFile is not null)
                        {
                            await _creativeFileRepository.UpdateAsync<CreativeFilesUploadResult>(singleCreativeFile);
                        }

                        break;
                }
            }
        }
    }
}
