using Azure.Storage.Blobs;

namespace Fattail.CreativeManagement.API.Infrastructure.CreativeFilesManager;

internal class ContainerClients
{
    public ContainerClients (Lazy<BlobContainerClient> creativeContainerClient, Lazy<BlobContainerClient> zipCreativeContainerClient)
    {
        CreativeContainerClient = creativeContainerClient;
        ZipCreativeContainerClient = zipCreativeContainerClient;
    }
    public Lazy<BlobContainerClient> CreativeContainerClient { get; }
    public Lazy<BlobContainerClient> ZipCreativeContainerClient { get; }
}