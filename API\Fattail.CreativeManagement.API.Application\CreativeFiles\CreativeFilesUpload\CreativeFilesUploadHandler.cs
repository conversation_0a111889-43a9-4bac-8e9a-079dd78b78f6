﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload.Exceptions;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using FluentResults;
using MediatR;
using System.Net;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;

internal sealed class
    CreativeFilesUploadHandler : IRequestHandler<CreativeFilesUploadCommand,
        Result<IReadOnlyList<Result<CreativeFilesUploadResult>>>>
{
    private readonly ICreativeFileRepository _creativeFileRepository;
    private readonly ICreativeFileStorageManager _creativeFileStorageManager;
    private readonly ICreativeFilesUploadPolicy _creativeFilesUploadPolicy;
    private readonly IIdManager _idManager;

    public CreativeFilesUploadHandler (
        IIdManager idManager,
        ICreativeFilesUploadPolicy creativeFileUploadPolicy,
        ICreativeFileStorageManager creativeFileStorageManager,
        ICreativeFileRepository creativeFileRepository)
    {
        _creativeFilesUploadPolicy = creativeFileUploadPolicy;
        _idManager = idManager;
        _creativeFileStorageManager = creativeFileStorageManager;
        _creativeFileRepository = creativeFileRepository;
    }

    public async Task<Result<IReadOnlyList<Result<CreativeFilesUploadResult>>>> Handle (
        CreativeFilesUploadCommand request,
        CancellationToken cancellationToken)
    {
        const int MaxCreativeFilesPerUpload = 200;

        if (request.CreativeFilesToUpload.Count > MaxCreativeFilesPerUpload)
        {
            throw new CreativeFilesUploadFileLimitExceededException(
                $"Can't handle more than ${MaxCreativeFilesPerUpload} files at once.");
        }

        var result = new List<Result<CreativeFilesUploadResult>>();

        foreach (CreativeFileToUpload creativeFileToUpload in request.CreativeFilesToUpload)
        {
            Result<CreativeFile> creativeFilePrepareForUploadResult = CreativeFile.PrepareToUpload(
                _creativeFilesUploadPolicy,
                _idManager,
                CreativeFileName.From(WebUtility.UrlDecode(creativeFileToUpload.FileName)),
                creativeFileToUpload.Content);

            if (creativeFilePrepareForUploadResult.IsFailed)
            {
                result.Add(creativeFilePrepareForUploadResult.ToResult());
                continue;
            }

            CreativeFile creativeFile = creativeFilePrepareForUploadResult.Value;

            Result<CreativeFileStorageMetadata> creativeFileStoredMetaDataResult =
                await _creativeFileStorageManager.StoreCreativeFile(creativeFile.Name,
                    creativeFileToUpload.Content);

            if (creativeFileStoredMetaDataResult.IsFailed)
            {
                result.Add(creativeFileStoredMetaDataResult.ToResult());
                continue;
            }

            creativeFile.PromoteToUploaded(creativeFileStoredMetaDataResult.Value);

            CreativeFilesUploadResult creativeFilesUploadResult =
                await _creativeFileRepository.CreateAsync<CreativeFilesUploadResult>(creativeFile);

            result.Add(creativeFilesUploadResult);
        }

        return result;
    }
}