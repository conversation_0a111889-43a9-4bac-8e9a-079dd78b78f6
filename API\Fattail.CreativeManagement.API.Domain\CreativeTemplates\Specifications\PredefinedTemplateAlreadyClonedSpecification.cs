using System.Linq.Expressions;
using Fattail.CreativeManagement.API.Domain.Repositories;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;

public class PredefinedTemplateAlreadyClonedSpecification (CreativeType creativeType) : Specification<CreativeTemplate>
{
    public override Expression<Func<CreativeTemplate, bool>> ToExpression ()
    {
        return template => template.CreativeType.ToString() == creativeType.ToString() &&
                           template.Predefined;
    }
}
