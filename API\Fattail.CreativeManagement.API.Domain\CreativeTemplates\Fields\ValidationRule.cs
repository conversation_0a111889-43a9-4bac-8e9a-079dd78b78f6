﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields.ValidationRuleValidatorStrategy;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields
{
    public sealed class ValidationRule : ValueObject
    {
        private ValidationRule (CreativeFieldValidationRuleType type, IReadOnlyList<string> options)
        {
            Options = options;
            Type = type;
        }

        public CreativeFieldValidationRuleType Type { get; }

        public IReadOnlyList<string> Options { get; }

        protected override IEnumerable<object?> GetEqualityComponents ()
        {
            yield return Type;
            yield return Options;
        }

        public static Result<ValidationRule> Create (CreativeFieldValidationRuleType type,
            IReadOnlyList<string> options, CreativeFieldId id)
        {
            IValidationRuleValidatorStrategy validator =
                ValidationRuleValidationStrategyFactory.Create(type);
            Result result = validator.IsValid(id, type, options);

            if (result.IsFailed)
            {
                return result;
            }

            return new ValidationRule(type, options);
        }
    }
}