namespace Fattail.CreativeManagement.API.Azure.Triggers.BlobStorage.CreativesMigration;

public class AdBookCreative
{
    public long CampaignId { get; set; }
    public long ClientId { get; set; }
    public string ClientName { get; set; } = null!;
    public long LineItemId { get; set; }
    public long CreativeId { get; set; }
    public string CreativeName { get; set; } = null!;
    public string CreativePath { get; set; } = null!;
    public string DestinationUrl { get; set; } = null!;
    public bool ApplyCreativeToFuturePlacement { get; set; }
    public string Comments { get; set; } = null!;
    public bool Active { get; set; }
    public DateTime CreatedAt { get; set; }
    public bool OriginatedInAbOms { get; set; }
}