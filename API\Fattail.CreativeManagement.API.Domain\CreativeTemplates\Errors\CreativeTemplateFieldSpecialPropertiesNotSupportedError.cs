﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateFieldSpecialPropertiesNotSupportedError : ErrorBase
{
    public CreativeTemplateFieldSpecialPropertiesNotSupportedError (CreativeFieldId creativeFieldId, string entity)
        : base($"The creative field with id {creativeFieldId} does not support special properties.",
            ErrorType.CreativeTemplateFieldUnsupportedValidationRuleType)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
    }
}