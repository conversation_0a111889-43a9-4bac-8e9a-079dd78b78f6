﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Specifications;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using SelectOptionDomain = Fattail.CreativeManagement.API.Domain.CreativeFields.Settings.SelectOption;

namespace Fattail.CreativeManagement.API.Application.CreativeFields.CreativeFieldAdd;

internal class CreativeFieldCreationFactory : ICreativeFieldCreationFactory
{
    public async Task<Result<CreativeField>> CreateField (IIdManager idManager,
        ICreativeFieldRepository creativeFieldRepository,
        CreativeFieldAddCommand requestModel)
    {
        var creativeFieldId = new CreativeFieldId(idManager.GetId());
        CreativeFieldType creativeFieldType = CreativeFieldType.FromValue((int)(requestModel.Type ?? CreativeFieldTypeEnum.None));
        var creativeFieldNameInUseSpecification = new CreativeFieldNameInUseSpecification(requestModel.Name, creativeFieldType);
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(await creativeFieldRepository.FindAsync(creativeFieldNameInUseSpecification) == null);

        var settings = new Dictionary<string, object>();

        if (requestModel.Options.Any())
        {
            settings.Add(nameof(SelectCreativeFieldSettings.Options).ToLower(),
                requestModel.Options.Select(x => SelectOptionDomain.Create(x.Id, x.Description)).ToList().AsReadOnly());
        }

        return CreativeField.Create(creativeFieldId, requestModel.Name, creativeFieldType, creativeFieldUniqueNameRequirement, false, null, settings);
    }
}