using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrate;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrate;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace Fattail.CreativeManagement.API.Azure.Triggers.BlobStorage.CreativesMigration;

public class MigrateCreativesActivityTrigger
{
    private readonly IMediator _mediator;
    private readonly ILogger<MigrateCreativesActivityTrigger> _logger;
    private readonly ParallelExecutionSettings _parallelExecutionSettings;
    private const long CreativeTemplateId = 574650397348900;
    private const long CreativeFieldFileId = 574650397348901;
    private const long CreativeFieldDestinationUrlId = 574650397348902;
    private const long CreativeFieldApplyCreativeToFuturePlacementId = 574650397348903;
    private const long CreativeFieldCommentsId = 574650397348904;
    private const long ApplyCreativeToFuturePlacementFieldOptionId = 1;

    public MigrateCreativesActivityTrigger (IMediator mediator,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        ILogger<MigrateCreativesActivityTrigger> logger)
    {
        _mediator = mediator;
        _parallelExecutionSettings = parallelExecutionSettings.Value;
        _logger = logger;
    }

    [Function(nameof(MigrateCreativesActivityTrigger))]
    public async Task<List<MigrationResult>> Run (
        [ActivityTrigger] MigrateCreativesActivityTriggerModel migrateCreativesActivityModel,
        FunctionContext context)
    {
        var migrationResults = new ConcurrentBag<MigrationResult>();

        await Parallel.ForEachAsync(migrateCreativesActivityModel.AdBookCreatives,
            new ParallelOptions { MaxDegreeOfParallelism = _parallelExecutionSettings.MaxDegreeOfParallelismForCreation },
            async (migrateCreativesActivityTriggerModel, cancellationToken) =>
            {
                string fileLocation = migrateCreativesActivityTriggerModel.AdBookCreatives.First().CreativePath
                    .Replace('\\', '/');
                string fileName = Path.GetFileName(fileLocation);

                var creativeFilesMigrateCommand =
                    new CreativeFilesMigrateCommand(new CreativeFileToMigrate(fileName, fileLocation));

                Result<CreativeFilesMigrateResult> creativeFileMigrateResult =
                    await _mediator.Send(creativeFilesMigrateCommand);

                AdBookCreative creativeDetail = migrateCreativesActivityTriggerModel.AdBookCreatives.First();

                if (creativeFileMigrateResult.IsFailed)
                {
                    migrationResults.Add(new MigrationResult(false,
                        creativeFileMigrateResult.Errors.Select(e => e.Message).ToList()));
                    return;
                }

                var lineItemIds = migrateCreativesActivityTriggerModel.AdBookCreatives
                    .Select(creative => creative.LineItemId)
                    .ToHashSet();

                IReadOnlyList<CreativeFieldDto> creativeFieldValues =
                    AssignCreativeFieldValues(creativeDetail,
                        creativeFileMigrateResult.Value.Id);

                var creativeMigrateCommand = new CreativeMigrateCommand(CreativeTemplateId, CreativeFieldFileId,
                    creativeDetail.CreativeId,
                    creativeDetail.ClientId,
                    creativeDetail.CreativeName,
                    creativeDetail.CampaignId, lineItemIds, creativeFieldValues,
                    creativeDetail.ClientName);

                Result<CreativeResult> creativeMigrateResult = await _mediator.Send(creativeMigrateCommand);

                if (creativeMigrateResult.IsFailed)
                {
                    migrationResults.Add(new MigrationResult(false,
                        creativeMigrateResult.Errors.Select(e => e.Message).ToList()));
                    return;
                }

                migrationResults.Add(new MigrationResult(true, new List<string>()));

            });

        return migrationResults.ToList();

    }

    private IReadOnlyList<CreativeFieldDto> AssignCreativeFieldValues (AdBookCreative creativeMigration,
        long creativeFileId)
    {
        var creativeFieldDtoList = new List<CreativeFieldDto>
        {
            new SingleLineTextFieldValue(CreativeFieldDestinationUrlId, creativeMigration.DestinationUrl),
            new MultiSelectOptionFieldValue(CreativeFieldApplyCreativeToFuturePlacementId,
                new List<long> { ApplyCreativeToFuturePlacementFieldOptionId }),
            new MultiLineTextFieldValue(CreativeFieldCommentsId, creativeMigration.Comments),
            new MultiFileUploadFieldValue(CreativeFieldFileId, new List<long> { creativeFileId })
        };
        return creativeFieldDtoList;
    }
}