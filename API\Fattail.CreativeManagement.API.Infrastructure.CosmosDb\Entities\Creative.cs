using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

public class Creative : Entity
{
    public string? AdBookAdId { get; set; }
    public string Name { get; set; }
    public string CreativeTemplateId { get; set; }
    public string CreativeTemplateName { get; set; }
    public string? AdBookClientId { get; set; }
    public string CampaignId { get; set; }
    public List<string> LineItemIds { get; set; } = [];
    public DateTime LastUpdatedOn { get; set; }
    public string LastUpdatedBy { get; set; }
    public CreativeStatus? Status { get; set; }
    public CreativeFieldValue[] Fields { get; set; }
    public ApprovalInformation? LastApproval { get; set; }
}

public class ApprovalInformation
{
    public string ApproverName { get; set; }
    public DateTime ApprovalDateTime { get; set; }
}

public class CreativeStatus
{
    public CreativeStatusEnum Value { get; set; }
    public string Description { get; set; }
}