﻿using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrateRollback;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrateRollback;

public class CreativeMigrateRollbackHandler : IRequestHandler<CreativeMigrateRollbackCommand, Result>
{
    private readonly ICreativeRepository _creativeRepository;
    private readonly ICreativeFileRepository _creativeFileRepository;

    public CreativeMigrateRollbackHandler (
        ICreativeRepository creativeRepository)
    {
        _creativeRepository = creativeRepository;
    }

    public async Task<Result> Handle (CreativeMigrateRollbackCommand request,
        CancellationToken cancellationToken)
    {
        switch (request.RollbackAction)
        {
            case RollbackAction.Full:
                return await _creativeRepository.DeletePartitionAsync();
            case RollbackAction.Partial:
                return await _creativeRepository.DeleteMigratedAsync();
        }
        
        return Result.Fail(new Error("Invalid rollback action"));
    }
}