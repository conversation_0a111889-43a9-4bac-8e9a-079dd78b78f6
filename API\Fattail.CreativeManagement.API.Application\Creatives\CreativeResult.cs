﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;

namespace Fattail.CreativeManagement.API.Application.Creatives;

public sealed record CreativeResult (
    long Id,
    long? AdBookAdId,
    long CreativeTemplateId,
    string CreativeTemplateName,
    string Name,
    long? AdBookClientId,
    long CampaignId,
    IReadOnlyList<long> LineItemIds,
    IReadOnlyList<CreativeFieldValueResult> Fields,
    CreativeStatusResult Status,
    string LastUpdatedBy,
    DateTime LastUpdatedOn,
    ApprovalInformationResult? LastApproval);

public record CreativeStatusResult (CreativeStatusEnum Value, string Description);

public record ApprovalInformationResult (string ApproverName, DateTime ApprovalDateTime);

public abstract record CreativeFieldValueResult (
    long Id,
    string Name,
    CreativeFieldType Type);

public abstract record CreativeFieldValueResult<TValue> (
    long Id,
    string Name,
    CreativeFieldType Type,
    TValue Value) : CreativeFieldValueResult(Id, Name, Type);


public sealed record MultiFileUploadFieldValueResult (
        long Id,
        string Name,
        IReadOnlyList<CreativeFileResult> Value)
    : CreativeFieldValueResult<IReadOnlyList<CreativeFileResult>>(Id, Name, CreativeFieldType.MultiFileUpload, Value);

public sealed record FileUploadFieldValueResult (
        long Id,
        string Name,
        CreativeFileResult Value)
    : CreativeFieldValueResult<CreativeFileResult>(Id, Name, CreativeFieldType.FileUpload, Value);

public sealed record CreativeFileResult (
    long Id,
    string Name,
    string Extension,  
    string Location, 
    long Size,
    Dictionary<string, string> Metadata);

public sealed record SingleLineTextFieldValueResult (
        long Id,
        string Name,
        string Value)
    : CreativeFieldValueResult<string>(Id, Name, CreativeFieldType.SingleLineText, Value);

public sealed record MultiSelectOptionFieldValueResult (
        long Id,
        string Name,
        IReadOnlyList<long> Value)
    : CreativeFieldValueResult<IReadOnlyList<long>>(Id, Name, CreativeFieldType.MultiSelectOption, Value);

public sealed record SingleSelectOptionFieldValueResult (
        long Id,
        string Name,
        long? Value)
    : CreativeFieldValueResult<long?>(Id, Name, CreativeFieldType.SingleSelectOption, Value);

public sealed record MultiLineTextFieldValueResult (
    long Id,
    string Name,
    string Value)
    : CreativeFieldValueResult<string>(Id, Name, CreativeFieldType.MultiLineText, Value);