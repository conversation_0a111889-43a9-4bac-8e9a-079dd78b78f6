using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Specifications;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Specifications;

[TestFixture]
public class CreativeTemplateNameInUseSpecificationTests
{
    [Test]
    public void Creative_template_name_in_use_when_name_matches_and_different_id ()
    {
        var currentTemplate = new CreativeTemplate(
            new CreativeTemplateId(1),
            CreativeTemplateName.Create("Template1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );
        var otherTemplate = new CreativeTemplate(
            new CreativeTemplateId(2),
            CreativeTemplateName.Create("Template1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );

        var spec = new CreativeTemplateNameInUseSpecification(currentTemplate, "Template1");

        spec.IsSatisfiedBy(otherTemplate).Should().BeTrue();
    }

    [Test]
    public void Creative_template_name_not_in_use_when_name_does_not_match ()
    {
        var currentTemplate = new CreativeTemplate(
            new CreativeTemplateId(1),
            CreativeTemplateName.Create("Template1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );
        var otherTemplate = new CreativeTemplate(
            new CreativeTemplateId(2),
            CreativeTemplateName.Create("OtherName", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );

        var spec = new CreativeTemplateNameInUseSpecification(currentTemplate, "Template1");

        spec.IsSatisfiedBy(otherTemplate).Should().BeFalse();
    }

    [Test]
    public void Creative_template_name_not_in_use_when_same_id ()
    {
        var currentTemplate = new CreativeTemplate(
            new CreativeTemplateId(1),
            CreativeTemplateName.Create("Template1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );

        var spec = new CreativeTemplateNameInUseSpecification(currentTemplate, "Template1");

        spec.IsSatisfiedBy(currentTemplate).Should().BeFalse();
    }

    [Test]
    public void Creative_template_name_in_use_when_current_template_is_null_and_name_matches ()
    {
        var otherTemplate = new CreativeTemplate(
            new CreativeTemplateId(2),
            CreativeTemplateName.Create("Template1", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );

        var spec = new CreativeTemplateNameInUseSpecification(null, "Template1");

        spec.IsSatisfiedBy(otherTemplate).Should().BeTrue();
    }

    [Test]
    public void Creative_template_name_not_in_use_when_current_template_is_null_and_name_does_not_match ()
    {
        var otherTemplate = new CreativeTemplate(
            new CreativeTemplateId(2),
            CreativeTemplateName.Create("OtherName", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            new HashSet<CreativeTemplateCreativeField>(),
            false
        );

        var spec = new CreativeTemplateNameInUseSpecification(null, "Template1");

        spec.IsSatisfiedBy(otherTemplate).Should().BeFalse();
    }
}
