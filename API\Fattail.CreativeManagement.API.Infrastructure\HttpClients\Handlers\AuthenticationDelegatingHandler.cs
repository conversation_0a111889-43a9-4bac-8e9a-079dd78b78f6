﻿using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Utils;
using System.Net.Http.Headers;

namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.Handlers;

internal sealed class AuthenticationDelegatingHandler : DelegatingHandler
{
    private readonly AuthenticationManager _authenticationManager;

    public AuthenticationDelegatingHandler (AuthenticationManager authenticationManager)
    {
        _authenticationManager = authenticationManager;
    }

    protected override async Task<HttpResponseMessage> SendAsync (HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        string accessToken = await _authenticationManager.GetActiveJwt();

        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        return await base.SendAsync(request, cancellationToken);
    }
}