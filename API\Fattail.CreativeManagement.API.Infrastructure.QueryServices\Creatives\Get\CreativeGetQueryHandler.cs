using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives.Get;

internal class CreativeGetQueryHandler : IRequestHandler<CreativeGetQuery, QueryResult<CreativeQueryResult>>
{
    private readonly CreativeQueryService _creativesQueryService;

    public CreativeGetQueryHandler (CreativeQueryService creativesQueryService)
    {
        _creativesQueryService = creativesQueryService;
    }

    public async Task<QueryResult<CreativeQueryResult>> Handle (CreativeGetQuery request,
        CancellationToken cancellationToken)
    {
        return await _creativesQueryService.GetFrom<CreativeQueryResult>(request);
    }
}