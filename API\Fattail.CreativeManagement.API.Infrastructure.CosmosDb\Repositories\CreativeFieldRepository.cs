using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using System.Net;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Options;
using System.Linq.Expressions;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class CreativeFieldRepository :
    CosmosDbRepository<CreativeField, CreativeFieldId, Entities.CreativeField>,
    ICreativeFieldRepository
{
    public CreativeFieldRepository (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IMapper mapper,
        IOrganizationContext organizationContext,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings) : base(cosmosDbContainerFactory, mapper,
        organizationContext, parallelExecutionSettings)
    {
    }

    public override string ContainerName => "CreativeFields";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }

    public async Task<IReadOnlyList<CreativeField>> GetAllAsync ()
    {
        var items = new List<CreativeField>();

        try
        {
            IQueryable<Entities.CreativeField>? feedResponse =
                _container.GetItemLinqQueryable<Entities.CreativeField>();

            using (var resultSet = feedResponse.ToFeedIterator())
            {
                while (resultSet.HasMoreResults)
                {
                    FeedResponse<Entities.CreativeField> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                    items.AddRange(_mapper.Map<IReadOnlyList<CreativeField>>(response.Resource));
                }
            }

            return items;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return new List<CreativeField>();
        }

    }

    public override async Task<CreativeField?> FindAsync (Expression<Func<CreativeField, bool>> predicate)
    {
        try
        {
            IQueryable<Entities.CreativeField> dbQuery = BuildQueryFromPredicate(predicate);

            using var resultSet = dbQuery.ToFeedIterator();
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Entities.CreativeField> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                return response.Any() ? _mapper.Map<CreativeField>(response.Resource.First()) : null;
            }
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
        }

        return default;
    }

    public override async Task<CreativeField?> FindAsync (Specification<CreativeField> specification)
    {
        try
        {
            IQueryable<Entities.CreativeField> dbQuery = BuildQueryFromPredicate(specification.ToExpression());

            using var resultSet = dbQuery.ToFeedIterator();
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Entities.CreativeField> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                return response.Any() ? _mapper.Map<CreativeField>(response.Resource.First()) : null;
            }
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
        }

        return null;
    }

    public override async Task<IReadOnlyList<CreativeField>> FindManyAsync (Expression<Func<CreativeField, bool>> predicate)
    {
        var items = new List<CreativeField>();
        IQueryable<Entities.CreativeField> dbQuery = BuildQueryFromPredicate(predicate);

        using var resultSet = dbQuery.ToFeedIterator();
        while (resultSet.HasMoreResults)
        {
            FeedResponse<Entities.CreativeField> response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
            items.AddRange(_mapper.Map<List<CreativeField>>(response.Resource));
        }

        return items;
    }

    private IQueryable<Entities.CreativeField> BuildQueryFromPredicate (Expression<Func<CreativeField, bool>> predicate)
    {
        // Create a simple query on infrastructure entities
        var entityQuery = _container
            .GetItemLinqQueryable<Entities.CreativeField>(
                requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                });

        // Translate the domain predicate to work with infrastructure entities
        // This is a targeted fix for the CreativeFieldNameInUseSpecification issue
        var translatedPredicate = TranslateDomainPredicateToEntityPredicate(predicate);

        return entityQuery.Where(translatedPredicate);
    }

    private Expression<Func<Entities.CreativeField, bool>> TranslateDomainPredicateToEntityPredicate (Expression<Func<CreativeField, bool>> domainPredicate)
    {
        // For the specific case of CreativeFieldNameInUseSpecification, we can translate manually
        // This avoids the complex AutoMapper ProjectTo that was causing the issue

        // Extract the lambda body
        var body = domainPredicate.Body;

        // Create parameter for entity type
        var entityParam = Expression.Parameter(typeof(Entities.CreativeField), "entity");

        // Use a visitor to translate the expression
        var visitor = new DomainToEntityExpressionVisitor(entityParam, domainPredicate.Parameters[0]);
        var translatedBody = visitor.Visit(body);

        return Expression.Lambda<Func<Entities.CreativeField, bool>>(translatedBody, entityParam);
    }

    private class DomainToEntityExpressionVisitor : ExpressionVisitor
    {
        private readonly ParameterExpression _entityParameter;
        private readonly ParameterExpression _domainParameter;

        public DomainToEntityExpressionVisitor (ParameterExpression entityParameter, ParameterExpression domainParameter)
        {
            _entityParameter = entityParameter;
            _domainParameter = domainParameter;
        }

        protected override Expression VisitParameter (ParameterExpression node)
        {
            // Replace domain parameter with entity parameter
            return node == _domainParameter ? _entityParameter : base.VisitParameter(node);
        }

        protected override Expression VisitMember (MemberExpression node)
        {
            // Handle property access on domain entities
            if (node.Expression == _domainParameter)
            {
                // Map domain properties to entity properties
                var propertyName = node.Member.Name;

                // Handle specific property mappings for CreativeField
                if (propertyName == "Type")
                {
                    // For CreativeField.Type, we need to handle the SmartEnum to enum conversion
                    // Domain: CreativeFieldType (SmartEnum) -> Entity: CreativeFieldTypeEnum (enum)
                    var entityProperty = typeof(Entities.CreativeField).GetProperty("Type");
                    if (entityProperty != null)
                    {
                        return Expression.Property(_entityParameter, entityProperty);
                    }
                }

                // Default mapping: same property name
                var entityPropertyInfo = typeof(Entities.CreativeField).GetProperty(propertyName);
                if (entityPropertyInfo != null)
                {
                    return Expression.Property(_entityParameter, entityPropertyInfo);
                }
            }

            return base.VisitMember(node);
        }

        protected override Expression VisitMethodCall (MethodCallExpression node)
        {
            // Handle method calls like ToString()
            if (node.Method.Name == "ToString" && node.Object != null)
            {
                var visitedObject = Visit(node.Object);

                // For CreativeFieldType.ToString(), we need special handling
                if (node.Object is MemberExpression memberExpr &&
                    memberExpr.Expression == _domainParameter &&
                    memberExpr.Member.Name == "Type")
                {
                    // Convert enum to string for comparison
                    return Expression.Call(visitedObject, typeof(object).GetMethod("ToString"));
                }

                return Expression.Call(visitedObject, node.Method);
            }

            return base.VisitMethodCall(node);
        }
    }
}