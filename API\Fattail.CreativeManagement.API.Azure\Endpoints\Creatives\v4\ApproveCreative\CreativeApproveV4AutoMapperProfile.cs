﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeApprove;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.ApproveCreative;

internal sealed class CreativeApproveV4AutoMapperProfile : Profile
{
    internal const string CreativeApproveCommandIdParameterName = "Id";

    public CreativeApproveV4AutoMapperProfile ()
    {
        CreateMap<CreativeApproveRequestV4, CreativeApproveCommand>()
            .ForMember(dest => dest.Id,
                opt => opt.MapFrom((src, dest, opt, context) =>
                    (long)context.Items[CreativeApproveCommandIdParameterName]))
            .ConstructUsing((src, context) =>
                new CreativeApproveCommand(
                    Id: (long)context.Items[CreativeApproveCommandIdParameterName]!,
                    ApproverName: src.ApproverName
                )
            );
    }
}