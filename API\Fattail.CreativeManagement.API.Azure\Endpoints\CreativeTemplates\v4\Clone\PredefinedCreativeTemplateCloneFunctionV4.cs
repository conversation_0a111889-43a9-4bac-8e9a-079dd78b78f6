using System.Net;
using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.PredefinedCreativeTemplateClone;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v4.Clone;

public class PredefinedCreativeTemplateCloneFunctionV4 (IMediator mediator, IMapper mapper)
    : BaseFunction(mediator, mapper)
{
    [OpenApiOperation(
        "Predefined Creative Templates Clone v4",
        tags: new[] { "v4" },
        Summary = "Clones a predefined creative template for organization use")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, In = ParameterLocation.Path, Type = typeof(long),
        Description = "The ID of the predefined creative template to clone")]
    [OpenApiRequestBody("application/json", typeof(PredefinedCreativeTemplateCloneRequest),
        Description = "Clone request with new template name", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeTemplateResponse))]
    [OpenApiResponseWithBody(HttpStatusCode.BadRequest, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(List<ErrorInformation>))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(PredefinedCreativeTemplateCloneFunctionV4))]
    public async Task<HttpResponseData> RunAsync (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v4/creative-templates/predefined/{id:long}/clone")]
        HttpRequestData req,
        long id)
    {
        PredefinedCreativeTemplateCloneCommand creativeTemplateCloneCommand =
            await FromRequest<PredefinedCreativeTemplateCloneRequest, PredefinedCreativeTemplateCloneCommand>(req,
                new Dictionary<string, object>
                {
                    { "PredefinedTemplateId", id }
                });

        Result<CreativeTemplateResult> creativeTemplateCloneResult = await _mediator.Send(creativeTemplateCloneCommand);

        return await FromResultWithLocation<CreativeTemplateResult, CreativeTemplateResponse>(req,
            creativeTemplateCloneResult, creativeTemplateCloneResult.ValueOrDefault?.Id);
    }
}
