﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Abstractions;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Resolvers;
using Newtonsoft.Json.Serialization;

namespace Fattail.CreativeManagement.API.Azure.OpenApiExamples;

public class ODataFilterExample : OpenApiExample<string>
{
    public override IOpenApiExample<string> Build (NamingStrategy namingStrategy = null)
    {
        Examples.Add(OpenApiExampleResolver.Resolve("$filter",
            "id eq 1234",
            namingStrategy));
        return this;
    }
}