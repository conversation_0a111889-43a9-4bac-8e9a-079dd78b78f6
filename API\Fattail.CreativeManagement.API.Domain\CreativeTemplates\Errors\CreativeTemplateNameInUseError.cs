using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

internal sealed class CreativeTemplateNameInUseError : ErrorBase
{
    public CreativeTemplateNameInUseError (string name, string entity) :
        base($"A creative template with same name already exists in the system",
            ErrorType.DuplicateCreativeTemplateName)
    {
        Metadata.Add(nameof(name), name);
        Metadata.Add(nameof(entity), entity);
    }
}