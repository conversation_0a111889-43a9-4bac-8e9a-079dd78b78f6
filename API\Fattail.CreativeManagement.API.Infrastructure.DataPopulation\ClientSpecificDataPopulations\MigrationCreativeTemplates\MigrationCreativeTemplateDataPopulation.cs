﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Utilities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeTemplates;

[ExcludeFromCodeCoverage]
public class MigrationCreativeTemplateDataPopulation
{
    private static readonly List<string> _listOrgid = new() { "100687" };
    private static readonly MigrationCreativeTemplateList _migrationCreativeTemplateList = new(_listOrgid);

    public async static Task<ICollection<DataPopulationResponseModel>> RunAsync (
        IDataPopulationRepository<CreativeTemplate> creativeTemplateItemResponseRepository)
    {
        var results = new List<DataPopulationResponseModel>();

        foreach (CreativeTemplate creativeTemplate in _migrationCreativeTemplateList.CreativeTemplates)
        {
            results.Add(
                await DataPopulationUtilities.ExecuteUpsertAsync(
                    creativeTemplateItemResponseRepository, creativeTemplate, creativeTemplate.OrgId)
            );
        }

        return results;
    }
}

