﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.AddCreative;

internal sealed class CreativeAddV4AutoMapperProfile : Profile
{
    public CreativeAddV4AutoMapperProfile ()
    {
        CreateMap<CreativeAddRequestV4, CreativeAddCommand>()
            .ForMember(dest => dest.LineItemIds,
                opt => opt.Ignore()) // LineItemIds are not used in this version
            .ConstructUsing((src, context) =>
                new CreativeAddCommand(
                    CreativeTemplateId: src.CreativeTemplateId,
                    Name: src.Name,
                    AdBookClientId: src.AdBookClientId,
                    CampaignId: src.CampaignId,
                    LineItemIds: null!, // LineItemIds are not used in this version
                    Fields: context.Mapper.Map<IReadOnlyList<CreativeFieldDto>>(src.<PERSON>),
                    UpdatedBy: src.UpdatedBy
                )
            );
    }
}