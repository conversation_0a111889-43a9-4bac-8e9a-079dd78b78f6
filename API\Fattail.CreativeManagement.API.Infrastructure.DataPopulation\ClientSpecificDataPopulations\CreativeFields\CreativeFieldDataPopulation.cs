﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Utilities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.CreativeFields
{
    [ExcludeFromCodeCoverage]
    public class CreativeFieldDataPopulation
    {
        private static readonly List<string> _listOrgid = new() { 
            "300741", "900999", "300821", "300846",
            "200715", "200725", "200730", "300707",
            "300719", "300722", "300731", "300746",
            "300751", "300754", "300759", "300785",
            "300794", "300799", "300800", "300807",
            "300811", "300819", "300833", "300834",
            "300835", "300836", "100696", "100719",
            "300739", "300745", "300750", "300791",
            "300846", "300847", "400710", "300766",
            "100687", "400902", "100721", "300786",
            "200716", "300831", "200709", "300747",
            "300844", "100716", "300709", "400903",
            "300813", "300730"
        };
        private static readonly CreativeFieldList _creativeFieldList = new(_listOrgid);


        public async static Task<ICollection<DataPopulationResponseModel>> RunAsync (
            IDataPopulationRepository<CreativeField> creativeFieldItemResponseRepository
            )
        {
            var results = new List<DataPopulationResponseModel>();

            foreach (CreativeField creativeField in _creativeFieldList._creativeFields)
            {
                results.Add(
                    await DataPopulationUtilities.ExecuteUpsertAsync(
                    creativeFieldItemResponseRepository, creativeField, creativeField.OrgId)
                );
            }

            return results;
        }

    }
}

