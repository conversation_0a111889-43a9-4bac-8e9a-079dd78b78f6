﻿using AutoMapper;
using Fattail.CreativeManagement.API.Azure.Common.Configuration.AutoMapper.TypeConverters;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares.Errors;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields.Get;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives.Get;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.Get;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.GetPredefinedCreativeTemplate;
using FluentResults;
using System.Collections.Specialized;

namespace Fattail.CreativeManagement.API.Azure.Common.Configuration.AutoMapper;

internal sealed class CommonProfile : Profile
{
    public CommonProfile ()
    {
        CreateMap<string, long>().ConvertUsing(src => long.Parse(src));
        CreateMap(typeof(Result<>), typeof(ResultResponse<>)).ConvertUsing(typeof(ResultTypeConverter<,>));
        CreateMap<Error, ErrorInformation>()
            .ForCtorParam("Code", opt => opt.MapFrom(src => src.Metadata.GetValueOrDefault(ErrorBase.ErrorCodeKey)))
            .ForCtorParam("Type", opt => opt.MapFrom(src => src.Metadata.GetValueOrDefault(ErrorBase.ErrorTypeKey)))
            .ForCtorParam("Metadata",
                opt => opt.MapFrom(src => src.Metadata.Where(metadata =>
                    metadata.Key != ErrorBase.ErrorCodeKey && metadata.Key != ErrorBase.ErrorTypeKey)));

        CreateMap<NameValueCollection, ODataQuery>()
            .ForMember(dest => dest.Filter, opt =>
                opt.MapFrom(src => src["$filter"]))
            .ForMember(dest => dest.OrderBy, opt =>
                opt.MapFrom(src => src["$orderby"]))
            .ForMember(dest => dest.Top, opt =>
                opt.MapFrom(src => src["$top"]))
            .ForMember(dest => dest.Skip, opt =>
                opt.MapFrom(src => src["$skip"]))
            .Include<NameValueCollection, CreativeGetQuery>()
            .Include<NameValueCollection, CreativeTemplateGetQuery>()
            .Include<NameValueCollection, PredefinedCreativeTemplateGetQuery>()
            .Include<NameValueCollection, CreativeFieldGetQuery>();

        CreateMap<NameValueCollection, CreativeGetQuery>();
        CreateMap<NameValueCollection, CreativeTemplateGetQuery>();
        CreateMap<NameValueCollection, PredefinedCreativeTemplateGetQuery>();
        CreateMap<NameValueCollection, CreativeFieldGetQuery>();

        CreateMap<Result<string>, ErrorInformation>()
            .ForMember(dest => dest.Message, opt => opt.Ignore())
            .ForMember(dest => dest.Code, opt => opt.Ignore())
            .ForMember(dest => dest.Type, opt => opt.Ignore())
            .ForMember(dest => dest.Metadata, opt => opt.Ignore())
            .ConstructUsing(result => new ErrorInformation(
                result.Reasons.First().Message,
                (int)result.Reasons.First().Metadata.GetValueOrDefault("Code"),
                result.Reasons.First().Metadata.GetValueOrDefault("Type").ToString(),
                new Dictionary<string, string>()
            ));

    }
}