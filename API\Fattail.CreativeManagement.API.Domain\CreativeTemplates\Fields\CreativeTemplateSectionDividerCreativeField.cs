﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
public class CreativeTemplateSectionDividerCreativeField : CreativeTemplateCreativeField
{
    private static readonly IReadOnlySet<SpecialPropertyKey> _specialPropertiesKeys = new HashSet<SpecialPropertyKey>([SpecialPropertyKey.Content]);

    internal CreativeTemplateSectionDividerCreativeField(CreativeFieldId id, string name, CreativeFieldType type, int displayOrder, string? tooltip, string? content) 
        : base(id, name, type, new Dictionary<CreativeFieldValidationRuleType, ValidationRule>(), displayOrder, tooltip)
    {
        Content = content;
    }

    public static Result<CreativeTemplateCreativeField> Create (CreativeFieldId id, string name, CreativeFieldType type, int displayOrder, string? tooltip, IReadOnlyDictionary<SpecialPropertyKey, object?>? specialProperties)
    {
        var creativeTemplateSectionDividerCreativeField = new CreativeTemplateSectionDividerCreativeField(id, name, type, displayOrder, tooltip, null);

        if (specialProperties != null)
        {
            Result result = creativeTemplateSectionDividerCreativeField.SetSpecialPropertiesValues(specialProperties);

            if(result.IsFailed)
            {
                return result;
            }
        }

        return creativeTemplateSectionDividerCreativeField;
    }

    public string? Content { get; private set; }

    private void SetContent(string? content)
    {
        Content = content;
    }

    public override Result SetSpecialPropertiesValues (IReadOnlyDictionary<SpecialPropertyKey, object?> specialProperties)
    {
        IReadOnlySet<SpecialPropertyKey> invalidSpecialPropertiesKeys = specialProperties.Keys.Except(_specialPropertiesKeys).ToHashSet();

        if (invalidSpecialPropertiesKeys.Any())
        {
            return Result.Fail(new CreativeTemplateFieldInvalidSpecialPropertiesError(Id, invalidSpecialPropertiesKeys,
                nameof(CreativeTemplateSectionDividerCreativeField)));
        }

        if (specialProperties.TryGetValue(SpecialPropertyKey.Content, out object? content))
        {
            if (content is not string contentValue)
            {
                return Result.Fail(new CreativeTemplateFieldSpecialPropertyInvalidTypeError(Id, nameof(Content),
                    nameof(String), nameof(CreativeTemplateSectionDividerCreativeField)));
            }
            
            SetContent(contentValue);
        }

        return Result.Ok();
    }
}
