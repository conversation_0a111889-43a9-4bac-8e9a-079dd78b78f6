﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrate;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using CreativeFile = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeFile;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper;

internal sealed class CreativeFilesProfile : Profile
{
    public CreativeFilesProfile ()
    {
        CreateMap<CreativeFileStorageMetadata, CreativeFile>()
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ForMember(dest => dest.Extension, opt => opt.Ignore())
            .ForMember(dest => dest.Size, opt => opt.Ignore())
            .ForMember(dest => dest.Metadata, opt => opt.Ignore())
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.OrgId, opt => opt.Ignore())
            .ForMember(dest => dest.LastAction, opt => opt.Ignore())
            .ForMember(dest => dest.Type, opt => opt.Ignore())
            .ForMember(dest => dest.ToBeDeleted, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<FileSize, CreativeFile>()
            .ForMember(dest => dest.Size, opt => opt.MapFrom(src => src.SizeInBytes))
            .ForMember(dest => dest.Name, opt => opt.Ignore())
            .ForMember(dest => dest.Extension, opt => opt.Ignore())
            .ForMember(dest => dest.BlobName, opt => opt.Ignore())
            .ForMember(dest => dest.Location, opt => opt.Ignore())
            .ForMember(dest => dest.Metadata, opt => opt.Ignore())
            .ForMember(dest => dest.UploadedDate, opt => opt.Ignore())
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.OrgId, opt => opt.Ignore())
            .ForMember(dest => dest.LastAction, opt => opt.Ignore())
            .ForMember(dest => dest.Type, opt => opt.Ignore())
            .ForMember(dest => dest.ToBeDeleted, opt => opt.Ignore())
            .ReverseMap()
            .ForCtorParam("sizeInBytes", opt => opt.MapFrom(src => src.Size));

        CreateMap<CreativeFileMetadata, Dictionary<string, string>?>()
            .ConvertUsing(value => value == null ? null : (Dictionary<string, string>)value);

        CreateMap<Domain.CreativeFiles.CreativeFile, CreativeFile>()
            .IncludeMembers(creativeFile => creativeFile.StorageMetadata, creativeFile => creativeFile.Size)
            .ForMember(dest => dest.Extension, opt => opt.MapFrom(src => src.Name.Extension))
            .ForMember(dest => dest.OrgId, opt => opt.Ignore())
            .ForMember(dest => dest.LastAction, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<CreativeFile?, CreativeFileId?>().ConvertUsing(value => value == null || string.IsNullOrWhiteSpace(value.Id) ? null : (CreativeFileId)value.Id);
        CreateMap<CreativeFileValue?, CreativeFileId?>().ConvertUsing(value => value == null || string.IsNullOrWhiteSpace(value.Id) ? null : (CreativeFileId)value.Id);

        CreateMap<CreativeFileType, CreativeFileTypeEnum>().ConvertUsing(value => value.EnumType);
        CreateMap<CreativeFileTypeEnum, CreativeFileType>().ConvertUsing(value => CreativeFileType.FromValue((int)value, CreativeFileType.Other));

        CreateMap<CreativeFile, CreativeFileValue>();

        CreateMap<CreativeFile, CreativeFilesUploadResult>();
        CreateMap<CreativeFile, CreativeFilesMigrateResult>();
    }
}