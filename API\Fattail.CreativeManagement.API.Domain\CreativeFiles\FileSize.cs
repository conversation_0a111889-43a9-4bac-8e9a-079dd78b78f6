﻿using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles;

public sealed class FileSize : ValueObject
{
    private FileSize (long sizeInBytes)
    {
        SizeInBytes = sizeInBytes;
        SizeInKilobytes = SizeInBytes / 1024.0;
        SizeInMegaBytes = SizeInKilobytes / 1024.0;
    }
    
    public long SizeInBytes { get; }
    public double SizeInKilobytes { get; }
    public double SizeInMegaBytes { get; }

    public static FileSize From (Stream stream)
    {
        return new FileSize(stream.Length);
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return SizeInBytes;
    }

    public static implicit operator long (FileSize megaBytes)
    {
        return megaBytes.SizeInBytes;
    }
}