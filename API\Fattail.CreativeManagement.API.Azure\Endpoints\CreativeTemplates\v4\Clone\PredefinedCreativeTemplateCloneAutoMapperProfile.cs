using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.PredefinedCreativeTemplateClone;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeTemplates.v4.Clone;

public class PredefinedCreativeTemplateCloneAutoMapperProfile : Profile
{
    public const string CreativeTemplateCloneCommandIdParameterName = "PredefinedTemplateId";
    
    public PredefinedCreativeTemplateCloneAutoMapperProfile()
    {
        CreateMap<PredefinedCreativeTemplateCloneRequest, PredefinedCreativeTemplateCloneCommand>()
            .ForMember(dest => dest.PredefinedTemplateId, opt => opt.Ignore())
            .ConstructUsing((src, context) =>
                new PredefinedCreativeTemplateCloneCommand(
                    PredefinedTemplateId: (long)context.Items[CreativeTemplateCloneCommandIdParameterName]!,
                    Name: src.Name
                )
            );;
    }
}
