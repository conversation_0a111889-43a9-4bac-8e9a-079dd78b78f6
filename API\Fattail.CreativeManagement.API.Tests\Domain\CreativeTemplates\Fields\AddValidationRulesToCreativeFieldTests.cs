﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;
using NUnit.Framework;
using FluentAssertions;
using FluentResults.Extensions.FluentAssertions;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates.Fields
{
    [TestFixture]
    public class AddValidationRulesToCreativeFieldTests
    {
        [SetUp]
        public void SetUp ()
        {
            _creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        }

        private CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = null!;
        private readonly string _fieldName = "fieldName";
        private readonly long _fieldId = 1234;


        [Test]
        public void Can_not_add_duplicated_validation_rules ()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);
            CreativeField creativeField = CreativeField.Create(creativeFieldId, _fieldName, CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value;
            
            CreativeTemplateCreativeField creativeTemplateField = CreativeTemplateCreativeField.Create(creativeFieldId,
                new HashSet<CreativeField>([creativeField]), 1).Value;

            creativeTemplateField.AddValidationRule(CreativeFieldValidationRuleType.FileSize, new List<string> { "10" });
            Result addValidationRuleResult = creativeTemplateField.AddValidationRule(CreativeFieldValidationRuleType.FileSize, new List<string> { "10" });

            addValidationRuleResult.Should().BeFailure().And
                .HaveReason(new CreativeTemplateDuplicateValidationRuleError(creativeFieldId, nameof(CreativeField), CreativeFieldValidationRuleType.FileSize));
        }

        [Test]
        public void Creative_template_field_cant_be_created_with_unsupported_validation_rules_verifier_for_text_fields_fails ()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);

            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult =
                CreativeTemplateCreativeField.Create(creativeFieldId,
                    new HashSet<CreativeField>([CreativeField.Create(creativeFieldId, _fieldName, CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, false, null, null).Value]), 1);
            
            Result validationResult = creativeTemplateFieldResult.Value.AddValidationRule(CreativeFieldValidationRuleType.FileSize, new List<string>{"10"});
            validationResult = Result.Merge(validationResult, creativeTemplateFieldResult.Value.AddValidationRule(CreativeFieldValidationRuleType.FileUploadExtensions, new List<string>{".png"}));

            validationResult.Should().BeFailure().And
                .HaveReason(new CreativeTemplateUnsupportedValidationTypeError(creativeFieldId.ToString(),
                    nameof(CreativeField), CreativeFieldValidationRuleType.FileUploadExtensions.ToString())).And.HaveReason(new CreativeTemplateUnsupportedValidationTypeError(creativeFieldId.ToString(),
                    nameof(CreativeField), CreativeFieldValidationRuleType.FileSize.ToString()));

            validationResult.Errors.Should().HaveCount(2);
        }

        [Test]
        public void Creative_template_field_cant_be_created_with_invalid_file_size_verifier_fails ()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);

            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult =
                CreativeTemplateCreativeField.Create(creativeFieldId,
                    new HashSet<CreativeField>([CreativeField.Create(creativeFieldId, _fieldName, CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value]), 1);
            
            Result validationResult = creativeTemplateFieldResult.Value.AddValidationRule(CreativeFieldValidationRuleType.FileSize, new List<string>{"asd10"});

            validationResult.Should().BeFailure().And
                .HaveReason(new CreativeTemplateInvalidFileSizeError(creativeFieldId.ToString(),
                    nameof(CreativeField), CreativeFieldValidationRuleType.FileSize.ToString()));
        }

        [Test]
        public void
            Creative_template_field_cant_be_created_with_invalid_file_extension_verifier_fails ()
        {
            var creativeFieldId = new CreativeFieldId(_fieldId);

            Result<CreativeTemplateCreativeField> creativeTemplateFieldResult =
                CreativeTemplateCreativeField.Create(creativeFieldId,
                    new HashSet<CreativeField>([CreativeField.Create(creativeFieldId, _fieldName, CreativeFieldType.MultiFileUpload, _creativeFieldUniqueNameRequirement, false, null, null).Value]), 1);
            
            Result validationResult = creativeTemplateFieldResult.Value.AddValidationRule(CreativeFieldValidationRuleType.FileUploadExtensions, new List<string>{"asd10"});

            validationResult.Should().BeFailure().And
                .HaveReason(new CreativeTemplateInvalidFileExtensionError(creativeFieldId.ToString(),
                    nameof(CreativeField), CreativeFieldValidationRuleType.FileUploadExtensions.ToString()));
        }
    }
}

