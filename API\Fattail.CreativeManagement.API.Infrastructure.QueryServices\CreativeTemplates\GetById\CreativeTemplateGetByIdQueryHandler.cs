using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.GetById;

internal sealed class CreativeTemplateGetByIdQueryHandler : IRequestHandler<CreativeTemplateGetByIdQuery, CreativeTemplateQueryResult?>
{
    private readonly CreativeTemplateQueryService _creativeTemplatesQueryService;

    public CreativeTemplateGetByIdQueryHandler (CreativeTemplateQueryService creativeTemplatesQueryService)
    {
        _creativeTemplatesQueryService = creativeTemplatesQueryService;
    }
    
    public async Task<CreativeTemplateQueryResult?> Handle (CreativeTemplateGetByIdQuery request, CancellationToken cancellationToken)
    {
        return await _creativeTemplatesQueryService.FindByIdAsync<CreativeTemplateQueryResult>(request.Id);
    }
}