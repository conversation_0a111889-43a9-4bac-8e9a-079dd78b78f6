﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper.CustomTypeConverters;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using CreativeTemplate = Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.CreativeTemplate;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper;

internal sealed class CreativeTemplatesProfile : Profile
{
    public CreativeTemplatesProfile ()
    {
        CreateMap<CreativeTemplate, Domain.CreativeTemplates.CreativeTemplate>()
            //This is only needed because there is a typo in the property name in the Cosmos DB entity
            .ForCtorParam("archived", opt => opt.MapFrom(src => src.Archive));

        CreateMap<Domain.CreativeTemplates.CreativeTemplate, CreativeTemplate>()
            .ForMember(dest => dest.OrgId, opt => opt.Ignore())
            .ForMember(dest => dest.LastAction, opt => opt.Ignore())
            //This is only needed because there is a typo in the property name in the Cosmos DB entity
            .ForMember(dest => dest.Archive, opt => opt.MapFrom(src => src.Archived))
            .ForMember(dest => dest.ClonedFrom, opt => opt.MapFrom(src => (long?)src.ClonedFrom));

        CreateMap<CreativeTemplateCreativeField, Domain.CreativeTemplates.Fields.CreativeTemplateCreativeField>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeField), typeof(Domain.CreativeTemplates.Fields.CreativeTemplateMultiSelectCreativeField))
            .Include(typeof(CreativeTemplateSingleSelectCreativeField), typeof(Domain.CreativeTemplates.Fields.CreativeTemplateSingleSelectCreativeField))
            .Include(typeof(CreativeTemplateSectionDividerCreativeField), typeof(Domain.CreativeTemplates.Fields.CreativeTemplateSectionDividerCreativeField))
            .ReverseMap();

        CreateMap<CreativeTemplateMultiSelectCreativeField, Domain.CreativeTemplates.Fields.CreativeTemplateMultiSelectCreativeField>().ReverseMap();
        CreateMap<CreativeTemplateSingleSelectCreativeField, Domain.CreativeTemplates.Fields.CreativeTemplateSingleSelectCreativeField>().ReverseMap();
        CreateMap<CreativeTemplateSectionDividerCreativeField, Domain.CreativeTemplates.Fields.CreativeTemplateSectionDividerCreativeField>()
            .ForMember(dm => dm.ValidationRules, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<CreativeTemplateSelectOption, Domain.CreativeFields.Settings.SelectOption>().ReverseMap();
        CreateMap<ValidationRule, Domain.CreativeTemplates.Fields.ValidationRule>().ReverseMap();
        CreateMap<Dictionary<CreativeFieldValidationRuleType, Domain.CreativeTemplates.Fields.ValidationRule>, ValidationRule[]>().ConvertUsing<ValidationRuleTypeConverter>();
        CreateMap<ValidationRule[], Dictionary<CreativeFieldValidationRuleType, Domain.CreativeTemplates.Fields.ValidationRule>>().ConvertUsing<ValidationRuleTypeConverter>();

        CreateMap<CreativeTemplate, CreativeTemplateResult>()
            .ForMember(dest => dest.ClonedFrom, opt => opt.MapFrom(src => src.ClonedFrom));
        CreateMap<CreativeTemplateCreativeField, CreativeTemplateCreativeFieldResult>()
            .Include(typeof(CreativeTemplateMultiSelectCreativeField), typeof(CreativeTemplateMultiSelectCreativeFieldResult))
            .Include(typeof(CreativeTemplateSingleSelectCreativeField), typeof(CreativeTemplateSingleSelectCreativeFieldResult))
            .Include(typeof(CreativeTemplateSectionDividerCreativeField), typeof(CreativeTemplateSectionDividerCreativeFieldResult));

        CreateMap<CreativeTemplateSelectOption, CreativeTemplateSelectOptionResult>();
        CreateMap<CreativeTemplateMultiSelectCreativeField, CreativeTemplateMultiSelectCreativeFieldResult>();
        CreateMap<CreativeTemplateSingleSelectCreativeField, CreativeTemplateSingleSelectCreativeFieldResult>();
        CreateMap<CreativeTemplateSectionDividerCreativeField, CreativeTemplateSectionDividerCreativeFieldResult>();
        CreateMap<ValidationRule, ValidationRuleResult>();
    }
}