﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow.Models;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow.Models.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow;

public sealed class AdBookWorkflowHttpClient : IAdBookWorkflowHttpClient
{
    private readonly string _createAction = "creation";
    private readonly string _presaveCampaignAction = "presave_campaign_saved";
    private readonly HttpClient _httpClient;
    private readonly ILogger<AdBookWorkflowHttpClient> _logger;

    public AdBookWorkflowHttpClient (
        HttpClient httpClient,
        IOptions<AdBookWorkflowHttpClientConfiguration> adBookWorkflowHttpClientOptions,
        IOrganizationContext organizationContext,
        ILogger<AdBookWorkflowHttpClient> logger)
    {
        _httpClient = httpClient;
        _httpClient.BaseAddress =
            new Uri(adBookWorkflowHttpClientOptions.Value.GetAdBookWorkflowBaseUri(organizationContext.OrganizationId));
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        _logger = logger;
    }

    public async Task NotifyCampaignCreativeCreation (long creativeId, long campaignId)
    {
        using var campaignWorkflowRequest = new HttpRequestMessage(HttpMethod.Post, "campaign-creatives");

        var campaignCreativeWorkflow = new CampaignCreativeWorkflow
        {
            Trigger = new Trigger { TriggerType = _createAction }
        };

        campaignCreativeWorkflow.CampaignCreatives.Add(new CampaignCreative(creativeId, campaignId));
        campaignWorkflowRequest.Content = JsonContent.Create(campaignCreativeWorkflow);

        try
        {
            using HttpResponseMessage campaignWorkflowResponse = await _httpClient.SendAsync(campaignWorkflowRequest);

            if (!campaignWorkflowResponse.IsSuccessStatusCode)
            {
                _logger.LogError(
                     "AdBookWorkflowHttpClient.NotifyCampaignCreativeCreation - Failed to notify campaign creative creation.\n Creative Id: {CreativeId} Campaign Id: {CampaignId} Status code: {StatusCode}",
                     creativeId, campaignId, campaignWorkflowResponse.StatusCode);
            }
        }
        catch (Exception ex)
        {
            throw new AdBookWorkflowException(campaignWorkflowRequest.RequestUri?.ToString(), creativeId, campaignId, ex);
        }
    }

    public async Task NotifyLineItemAssignment (long creativeId, long campaignId, IEnumerable<long> lineItemIds)
    {
        using var lineItemWorkflowRequest = new HttpRequestMessage(HttpMethod.Post, "trigger");

        var lineItemAssignmentWorkflow = new LineItemAssignmentWorkflow(_presaveCampaignAction, campaignId);

        foreach (long lineItemId in lineItemIds)
        {
            lineItemAssignmentWorkflow.TriggerInfo.CreativeAssignmentRelations.Add(
                new CreativeAssignmentRelationship(creativeId, lineItemId));
        }

        lineItemWorkflowRequest.Content = JsonContent.Create(lineItemAssignmentWorkflow);

        try
        {
            using HttpResponseMessage lineItemWorkflowResponse = await _httpClient.SendAsync(lineItemWorkflowRequest);
            if (!lineItemWorkflowResponse.IsSuccessStatusCode)
            {
                _logger.LogError(
                    "AdBookWorkflowHttpClient.NotifyLineItemAssignment - Failed to notify line item assignment.\n Creative Id: {CreativeId} Line Item Ids: {LineItemIds} Status code: {StatusCode}",
                    creativeId, string.Join(", ", lineItemIds), lineItemWorkflowResponse.StatusCode);
            }
        }
        catch (Exception ex)
        {
            throw new LineItemAssignmentWorkflowException(lineItemWorkflowRequest.RequestUri?.ToString(), creativeId,
                lineItemIds, ex);
        }
    }
}