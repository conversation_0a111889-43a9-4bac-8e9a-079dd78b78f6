using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;

namespace Fattail.CreativeManagement.API.Azure.Triggers.BlobStorage.CreativesMigration;

public sealed class AdBookCreativeCsvMap : ClassMap<AdBookCreative>
{
    const string CampaignId = "Campaign_ID";
    const string ClientId = "Client_ID";
    const string ClientName = "Client_Name";
    const string LineItemId = "LineItemID";
    const string CreativeId = "AD_ID";
    const string CreativeName = "Ad_Name";
    const string CreativePath = "Creat_Path";
    const string DestinationUrl = "Click_Through_URL";
    const string ApplyCreativeToFuturePlacement = "Apply_Creative_To_Future_Placement";
    const string Comments = "Notes";
    const string Active = "Active_Flag";
    const string CreatedAt = "date_stamp";
    const string OriginatedInAbOms = "Originate_In_AdBook";

    public AdBookCreativeCsvMap ()
    {
        Map(m => m.CampaignId).Name(CampaignId);
        Map(m => m.ClientId).Name(ClientId);
        Map(m => m.ClientName).Name(ClientName);
        Map(m => m.LineItemId).Name(LineItemId);
        Map(m => m.CreativeId).Name(CreativeId);
        Map(m => m.CreativeName).Name(CreativeName);
        Map(m => m.CreativePath).Name(CreativePath);
        Map(m => m.DestinationUrl).Name(DestinationUrl);
        Map(m => m.ApplyCreativeToFuturePlacement).Name(ApplyCreativeToFuturePlacement).TypeConverter<EmptyToBoolConverter>();
        Map(m => m.Comments).Name(Comments);
        Map(m => m.Active).Name(Active);
        Map(m => m.CreatedAt).Name(CreatedAt);
        Map(m => m.OriginatedInAbOms).Name(OriginatedInAbOms);
    }

    private class EmptyToBoolConverter : ITypeConverter  
    {  
        public object? ConvertFromString (string? text, IReaderRow row, MemberMapData memberMapData)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return false;
            }

            return text.Trim().ToUpper() == "TRUE";
        }
        
        public string ConvertToString (object? value, IWriterRow row, MemberMapData memberMapData)
        {
            if (value is null)
            {
                return string.Empty;
            }

            return (bool)value ? "TRUE" : "FALSE";
        }
    }  
}