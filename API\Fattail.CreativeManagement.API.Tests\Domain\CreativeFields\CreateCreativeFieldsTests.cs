﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeFields;

[TestFixture]
public class CreateCreativeFieldsTests
{
    private static IEnumerable<CreativeFieldType> AllCreativeFieldTypes => CreativeFieldType.List;

    private static IEnumerable<CreativeFieldType> ValidCreativeFieldTypes => CreativeFieldType.List
        .Where(ft => ft != CreativeFieldType.None);

    private static IEnumerable<CreativeFieldType> SelectFieldTypes => [CreativeFieldType.SingleSelectOption, CreativeFieldType.MultiSelectOption];

    private static IEnumerable<CreativeFieldType> NonSelectFieldTypes => CreativeFieldType.List
        .Where(ft => ft != CreativeFieldType.SingleSelectOption && ft != CreativeFieldType.MultiSelectOption && ft != CreativeFieldType.None);

    [Test]
    public void Creative_field_cant_be_created_without_type ()
    {
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            "Creative field name",
            CreativeFieldType.None, 
            creativeFieldUniqueNameRequirement, false, null, null);

        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative field type", nameof(CreativeField)));
    }

    [Test]
    public void Creative_field_cant_be_created_without_name ([Values("", " ", null)] string? name, [ValueSource(nameof(AllCreativeFieldTypes))] CreativeFieldType fieldType)
    {
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            name,
            fieldType,
            creativeFieldUniqueNameRequirement, false, null, null);

        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative field name", nameof(CreativeField)));
    }

    [Test]
    public void Creative_field_cant_be_created__when_unique_name_requirement_is_not_satisfied ([ValueSource(nameof(AllCreativeFieldTypes))] CreativeFieldType creativeFieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(false);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, false, null, null);

        creativeFieldResult.IsFailed.Should().BeTrue();
        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new CreativeFieldNameInUseError(creativeFieldName, creativeFieldType.ToString(),
                nameof(CreativeField)));
    }

    [Test]
    public void Creative_field_can_be_created ([ValueSource(nameof(NonSelectFieldTypes))] CreativeFieldType creativeFieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, false, null, null);

        creativeFieldResult.Should().BeSuccess();
        creativeFieldResult.Value.Should().NotBeNull();
        creativeFieldResult.Value.Name.Should().BeEquivalentTo(creativeFieldName);
        creativeFieldResult.Value.Type.Should().Be(creativeFieldType);
        creativeFieldResult.Value.Predefined.Should().BeFalse();
    }

    [Test]
    public void Create_field_without_system_field_name_can_be_created ([ValueSource(nameof(NonSelectFieldTypes))] CreativeFieldType creativeFieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, true,
            null, null);

        creativeFieldResult.Should().BeSuccess();
        creativeFieldResult.Value.OmsExternalIdentifier.Should().BeNull();
    }

    [Test]
    public void Create_field_with_system_field_name_can_be_created ([ValueSource(nameof(NonSelectFieldTypes))] CreativeFieldType creativeFieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        string omsExternalIdentifier = "oms external identifier";

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            creativeFieldType,
            creativeFieldUniqueNameRequirement, true,
            omsExternalIdentifier, null);

        creativeFieldResult.Should().BeSuccess();
        creativeFieldResult.Value.OmsExternalIdentifier.Should().Be(omsExternalIdentifier);
    }

    [Test]
    public void Select_field_types_cant_be_created_without_settings ([ValueSource(nameof(SelectFieldTypes))] CreativeFieldType selectFieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            selectFieldType,
            creativeFieldUniqueNameRequirement, false, null, null);

        creativeFieldResult.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("options", selectFieldType.GetType().Name));
    }

    [Test]
    public void Non_select_field_types_can_be_created_without_settings ([ValueSource(nameof(NonSelectFieldTypes))] CreativeFieldType fieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            fieldType,
            creativeFieldUniqueNameRequirement, false, null, null);

        creativeFieldResult.Should().BeSuccess();
        creativeFieldResult.Value.Should().NotBeNull();
        creativeFieldResult.Value.Settings.Should().Be(DefaultCreativeFieldSettings.Instance);
    }

    [Test]
    public void Select_field_types_can_be_created_without_settings ([ValueSource(nameof(SelectFieldTypes))] CreativeFieldType selectFieldType)
    {
        string creativeFieldName = "Creative field name";
        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        IReadOnlyList<SelectOption> selectOptions = new List<SelectOption>
        {
            SelectOption.Create(1, "Option 1"),
            SelectOption.Create(2, "Option 2")
        }.AsReadOnly();

        var settings = new Dictionary<string, object>
        {
            { nameof(SelectCreativeFieldSettings.Options).ToLower(), selectOptions }
        };

        Result<CreativeField> creativeFieldResult = CreativeField.Create(new CreativeFieldId(1234),
            creativeFieldName,
            selectFieldType,
            creativeFieldUniqueNameRequirement, false, null, settings);

        creativeFieldResult.Should().BeSuccess();
        creativeFieldResult.Value.Should().NotBeNull();
        creativeFieldResult.Value.Settings.Should().BeOfType<SelectCreativeFieldSettings>();
        var selectSettings = (SelectCreativeFieldSettings)creativeFieldResult.Value.Settings;
        selectSettings.Options.Should().BeEquivalentTo(selectOptions);
    }
}