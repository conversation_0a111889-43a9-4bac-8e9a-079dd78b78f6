﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.EditCreative;

public sealed class CreativeEditFunctionV2 : BaseFunction
{
    public CreativeEditFunctionV2 (
        IMediator mediator,
        IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Edit an existing creative v2", tags: new[] { "v2" },
        Deprecated = true,
        Summary = "Edit an existing creative v2")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeEditRequestV2),
        Description = "The creative to edit", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV2))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound, Description = "If the creative doesn't exist.")]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeEditFunctionV2))]
    public async Task<HttpResponseData> EditCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "v2/creatives/{id:long}")] HttpRequestData req,
        long id)
    {
        CreativeEditCommand creativeEditCommand = await FromRequest<CreativeEditRequestV2, CreativeEditCommand>(req, 
            new Dictionary<string, object>
        {
            { CreativeEditV2AutoMapperProfile.CreativeEditCommandIdParameterName, id }
        });

        Result<CreativeResult> result = await _mediator.Send(creativeEditCommand);

        HttpResponseData response =
            await FromResult<CreativeResult, CreativeResponseV2>(req, result);

        return response;
    }
}