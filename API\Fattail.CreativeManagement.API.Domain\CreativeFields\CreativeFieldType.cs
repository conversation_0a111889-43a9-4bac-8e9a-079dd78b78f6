using Ardalis.SmartEnum;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Types;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields;

public abstract class CreativeFieldType : SmartEnum<CreativeFieldType>
{
    public static readonly CreativeFieldType None = new NoneFieldType();
    public static readonly CreativeFieldType SingleLineText = new SingleLineTextFieldType();
    public static readonly CreativeFieldType MultiFileUpload = new MultiFileUploadFieldType();
    public static readonly CreativeFieldType FileUpload = new FileUploadFieldType();
    public static readonly CreativeFieldType MultiSelectOption = new MultiSelectOptionFieldType();
    public static readonly CreativeFieldType MultiLineText = new MultiLineTextFieldType();
    public static readonly CreativeFieldType SectionDivider = new SectionDividerFieldType();
    public static readonly CreativeFieldType SingleSelectOption = new SingleSelectOptionFieldType();

    protected CreativeFieldType (string name, int value) : base(name, value)
    {
        EnumType = (CreativeFieldTypeEnum)Value;
    }

    public CreativeFieldTypeEnum EnumType { get; }

    internal abstract Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings);
}