using MediatR;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates.GetPredefinedCreativeTemplateById;

internal sealed class PredefinedCreativeTemplateGetByIdQueryHandler : IRequestHandler<PredefinedCreativeTemplateGetByIdQuery, CreativeTemplateQueryResult?>
{
    private readonly PredefinedCreativeTemplateQueryService _predefinedCreativeTemplatesQueryService;

    public PredefinedCreativeTemplateGetByIdQueryHandler(PredefinedCreativeTemplateQueryService predefinedCreativeTemplatesQueryService)
    {
        _predefinedCreativeTemplatesQueryService = predefinedCreativeTemplatesQueryService;
    }
    
    public async Task<CreativeTemplateQueryResult?> Handle(PredefinedCreativeTemplateGetByIdQuery request, CancellationToken cancellationToken)
    {
        return await _predefinedCreativeTemplatesQueryService.FindByIdAsync<CreativeTemplateQueryResult>(request.Id);
    }
}
