﻿using FluentResults;
using MediatR;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeMigrate;

public sealed record CreativeMigrateCommand (
    long CreativeTemplateId,
    long CreativeFieldFileId,
    long AdBookAdId,
    long AdBookClientId,
    string Name,
    long CampaignId,
    HashSet<long> LineItemIds,
    IReadOnlyList<CreativeFieldDto> Fields,
    string UpdatedBy) : IRequest<Result<CreativeResult>>;