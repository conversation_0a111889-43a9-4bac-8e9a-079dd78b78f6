using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class FileUploadFieldType : CreativeFieldType
{
    internal FileUploadFieldType () : base(nameof(CreativeFieldTypeEnum.FileUpload), (int)CreativeFieldTypeEnum.FileUpload)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return DefaultCreativeFieldSettings.Instance;
    }
}
