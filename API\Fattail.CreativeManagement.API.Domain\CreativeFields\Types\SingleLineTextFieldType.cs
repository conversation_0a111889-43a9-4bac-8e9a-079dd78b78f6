using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Types;

public sealed class SingleLineTextFieldType : CreativeFieldType
{
    internal SingleLineTextFieldType () : base(nameof(CreativeFieldTypeEnum.SingleLineText), (int)CreativeFieldTypeEnum.SingleLineText)
    {
    }

    internal override Result<CreativeFieldSettings> CreateSettings (IReadOnlyDictionary<string, object> settings)
    {
        return DefaultCreativeFieldSettings.Instance;
    }
}
