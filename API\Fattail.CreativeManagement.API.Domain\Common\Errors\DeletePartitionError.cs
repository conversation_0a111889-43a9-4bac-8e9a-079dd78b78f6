﻿namespace Fattail.CreativeManagement.API.Domain.Common.Errors;

public class DeletePartitionError : ErrorBase
{
    public DeletePartitionError (string partitionKey, string entity) : base(
        $"Failed to delete partition with key '{partitionKey}' for entity '{entity}'.", ErrorType.DeletePartitionError)
    {
        Metadata.Add(nameof(partitionKey), partitionKey);
        Metadata.Add(nameof(entity), entity);
    }
}
