using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;

namespace Fattail.CreativeManagement.API.Application.CreativeTemplates;

public sealed record CreativeTemplateResult
{
    public long Id { get; init; }

    public string Name { get; init; }

    public CreativeType CreativeType { get; init; }

    public bool Archive { get; init; }

    public bool Predefined { get; init; }

    public long? ClonedFrom { get; init; }

    public IReadOnlyList<CreativeTemplateCreativeFieldResult> CreativeFields { get; init; }
}

public sealed record ValidationRuleResult
{
    public CreativeFieldValidationRuleType Type { get; init; }

    public IReadOnlyList<string> Options { get; init; }
};

public record CreativeTemplateCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    string? Tooltip
);

public record CreativeTemplateSelectOptionResult
(
    long Id,
    string Description
);

public record CreativeTemplateMultiSelectCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    IReadOnlyList<CreativeTemplateSelectOptionResult> Options,
    string? Tooltip
) : CreativeTemplateCreativeFieldResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip);

public record CreativeTemplateSingleSelectCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    IReadOnlyList<CreativeTemplateSelectOptionResult> Options,
    string? Tooltip
) : CreativeTemplateCreativeFieldResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip);

public record CreativeTemplateSectionDividerCreativeFieldResult
(
    long Id,
    string Name,
    int DisplayOrder,
    CreativeFieldTypeEnum Type,
    IReadOnlyList<ValidationRuleResult> ValidationRules,
    string? Tooltip,
    string? Content
) : CreativeTemplateCreativeFieldResult(Id, Name, DisplayOrder, Type, ValidationRules, Tooltip);