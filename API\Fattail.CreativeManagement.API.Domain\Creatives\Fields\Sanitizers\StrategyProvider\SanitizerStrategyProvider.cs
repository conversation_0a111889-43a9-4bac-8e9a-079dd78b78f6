﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;

//TODO: We need to remove the dependencies of these sanitizers and get the CreativeFiles entities from the Handler instead. That would make our Domain logic cleaner and pure and easier to test.
[ExcludeFromCodeCoverage]
internal sealed class SanitizerStrategyProvider : ISanitizerStrategyProvider
{
    private readonly IEnumerable<ISanitizer> _sanitizers;

    public SanitizerStrategyProvider (IEnumerable<ISanitizer> sanitizers)
    {
        _sanitizers = sanitizers;
    }

    public ISanitizer GetFrom (CreativeFieldType type)
    {
        return _sanitizers.Single(sanitizer => sanitizer.CreativeFieldType == type);
    }
}