using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFiles.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using Fattail.CreativeManagement.API.Azure.OpenApiExamples.Models;
using FluentResults;
using HttpMultipartParser;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFiles.v2.CreativeFilesUpload;

public class CreativeFilesUploadFunctionV2 : BaseFunction
{
    public CreativeFilesUploadFunctionV2 (
        IMediator mediator,
        IMapper mapper)
        : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Creative Files upload v2", tags: new[] { "v2" },
        Deprecated = true,
        Summary = "Creative Files Upload v2 [Limitation: request body up to 100mb per request]")]
    [OpenApiParameter(OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName, Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody("multipart/form-data", typeof(MultiPartFileModel),
        Description = "Creative files to upload. Can be multiple at once", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.MultiStatus, "application/json",
        typeof(List<ResultResponse<CreativeFilesUploadResponse>>))]
    [OpenApiResponseWithBody(HttpStatusCode.InternalServerError, "application/json", typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeFilesUploadFunctionV2))]
    public async Task<HttpResponseData> UploadCreativeFiles (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v2/creative-files")]
        HttpRequestData req)
    {
        MultipartFormDataParser form = await MultipartFormDataParser.ParseAsync(req.Body);

        var creativeFilesUploadRequest = new CreativeFilesUploadCommand
        (
            form.Files.Select(file => new CreativeFileToUpload
            (
                file.FileName,
                file.Data
            )).ToList()
        );

        Result<IReadOnlyList<Result<CreativeFilesUploadResult>>> result =
            await _mediator.Send(creativeFilesUploadRequest);

        return await
            FromResult<IReadOnlyList<Result<CreativeFilesUploadResult>>,
                IReadOnlyList<ResultResponse<CreativeFilesUploadResponse>>>(req, result, HttpStatusCode.MultiStatus);
    }
}