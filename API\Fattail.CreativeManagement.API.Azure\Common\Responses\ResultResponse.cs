﻿using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;

namespace Fattail.CreativeManagement.API.Azure.Common.Responses;

internal sealed record ResultResponse<TItem>(
    [property: OpenApiProperty(Description = "The value of the result in case the operation was successful.")]
    TItem? Value,
    [property: OpenApiProperty(Description = "The errors in case the operation was not successful.")]
    IReadOnlyCollection<ErrorInformation>? Errors = null,
    [property: OpenApiProperty(Description = "Indicates whether or not the operation was successful.")]
    bool IsSuccess = true);