﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives;

public class ApprovalInformation : ValueObject
{
    private ApprovalInformation(string approverName, DateTime approvalDateTime)
    {
        ApproverName = approverName;
        ApprovalDateTime = approvalDateTime;
    }

    public string ApproverName { get; }

    public DateTime ApprovalDateTime { get; }

    public static Result<ApprovalInformation> Create(string? approverName, DateTime? approvalDateTime)
    {
        var result = new Result();

        if (string.IsNullOrWhiteSpace(approverName))
        {
            result.WithError(new RequiredValueMissingError(nameof(approverName), nameof(Creative)));
        }

        if (approvalDateTime == null)
        {
            result.WithError(new RequiredValueMissingError(nameof(approvalDateTime), nameof(Creative)));
        }

        if(result.IsFailed)
        {
            return result;
        }

        return new ApprovalInformation(approverName!, approvalDateTime!.Value);
    }

    protected override IEnumerable<object?> GetEqualityComponents ()
    {
        yield return ApproverName;
        yield return ApprovalDateTime;
    }
}