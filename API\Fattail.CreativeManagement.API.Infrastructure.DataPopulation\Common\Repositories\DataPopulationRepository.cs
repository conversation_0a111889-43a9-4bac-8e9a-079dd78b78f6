﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories
{
    public abstract class DataPopulationRepository<TEntity> : IContainerContext, IDataPopulationRepository<TEntity> where TEntity : Entity
    {
        private readonly Container _container;
        protected readonly IOrganizationContext _organizationContext;

        public DataPopulationRepository (
            ICosmosDbContainerFactory cosmosDbContainerFactory,
            IOrganizationContext organizationContext)
        {
            _container = cosmosDbContainerFactory.GetContainer(ContainerName).Container;
            _organizationContext = organizationContext;
        }

        public abstract string ContainerName { get; }

        public abstract PartitionKey ResolvePartitionKey ();

        public async Task<ItemResponse<TEntity>> UpsertWithResponseAsync (TEntity entity, string partitionKey)
        {
            ItemResponse<TEntity> result = await _container.UpsertItemAsync(entity, new PartitionKey(partitionKey));

            return result;
        }
    }
}
