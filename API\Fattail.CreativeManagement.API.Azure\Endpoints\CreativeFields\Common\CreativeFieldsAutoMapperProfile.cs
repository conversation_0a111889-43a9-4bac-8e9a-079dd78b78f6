﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using DomainCreativeFieldType = Fattail.CreativeManagement.API.Domain.CreativeFields.CreativeFieldType;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.CreativeFields.Common;

internal sealed class CreativeFieldsAutoMapperProfile : Profile
{
    public CreativeFieldsAutoMapperProfile ()
    {
        CreateMap<DomainCreativeFieldType, CreativeFieldType>()
            .ConvertUsing(src => (CreativeFieldType)src.Value);

        CreateMap<CreativeFieldQueryResult, CreativeFieldResponse>()
            .Include(typeof(MultiSelectCreativeFieldQueryResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(SingleSelectCreativeFieldQueryResult), typeof(SingleSelectCreativeFieldResponse));

        CreateMap<SelectOptionQueryResult, SelectOptionResponse>();
        CreateMap<MultiSelectCreativeFieldQueryResult, MultiSelectCreativeFieldResponse>();
        CreateMap<SingleSelectCreativeFieldQueryResult, SingleSelectCreativeFieldResponse>();

        CreateMap<SelectOptionResult, SelectOptionResponse>();

        CreateMap<CreativeFieldResult, CreativeFieldResponse>()
            .Include(typeof(MultiSelectCreativeFieldResult), typeof(MultiSelectCreativeFieldResponse))
            .Include(typeof(SingleSelectCreativeFieldResult), typeof(SingleSelectCreativeFieldResponse));
        CreateMap<MultiSelectCreativeFieldResult, MultiSelectCreativeFieldResponse>();
        CreateMap<SingleSelectCreativeFieldResult, SingleSelectCreativeFieldResponse>();
    }
}