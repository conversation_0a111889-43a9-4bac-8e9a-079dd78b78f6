﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.Creatives;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
public class CreateApprovalInformationTests
{
    [TestCase(null)]
    [TestCase("")]
    [TestCase("    ")]
    public void Approval_information_cannot_be_created_without_approver_name (string? aprroverName)
    {
        Result<ApprovalInformation> lastApprovalResult = ApprovalInformation.Create(
            approverName: aprroverName,
            approvalDateTime: DateTime.UtcNow
        );

        lastApprovalResult.Should().BeFailure()
            .And.HaveReason<RequiredValueMissingError>(null);
    }

    [Test]
    public void Approval_information_cannot_be_created_without_approval_date_time ()
    {
        Result<ApprovalInformation> lastApprovalResult = ApprovalInformation.Create(
            approverName: "Approver Name",
            approvalDateTime: null
        );

        lastApprovalResult.Should().BeFailure()
            .And.HaveReason<RequiredValueMissingError>(null);
    }

    [Test]
    public void Approval_information_can_be_created ()
    {
        var approvalDateTime = DateTime.UtcNow;
        var approverName = "Approver Name";

        Result<ApprovalInformation> lastApprovalResult = ApprovalInformation.Create(
            approverName: approverName,
            approvalDateTime: approvalDateTime
        );

        lastApprovalResult.Should().BeSuccess();

        ApprovalInformation lastApproval = lastApprovalResult.Value;

        lastApproval.ApproverName.Should().Be(approverName);
        lastApproval.ApprovalDateTime.Should().Be(approvalDateTime);
    }
}