using AutoMapper;
using Fattail.CreativeManagement.API.Application.CreativeTemplates.PredefinedCreativeTemplateCreate;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.RunDataPopulation.v1;

public class RunDataPopulationFunction : BaseFunction
{
    public RunDataPopulationFunction (IMediator mediator, IMapper mapper) : base(mediator, mapper)
    { }

    [OpenApiOperation(
        "Run Data Population Function v1",
        new[] { "v1" },
        Summary = "Run Data Population Function v1 - Internal usage, no need to version it")]
    [OpenApiResponseWithoutBody(HttpStatusCode.OK)]
    [Function(nameof(RunDataPopulationFunction))]
    public async Task<HttpResponseData> Post (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v1/datapopulation/run")]
        HttpRequestData req)
    {
        var dataPopulationRequest = new DataPopulationCommand();

        Result<ICollection<DataPopulationResponseModel>> result =
            await _mediator.Send(dataPopulationRequest);

        var predefinedCreativeTemplateCreateCommand = new PredefinedCreativeTemplateCreateCommand();
        await _mediator.Send(predefinedCreativeTemplateCreateCommand);

        return await
            FromResult<ICollection<DataPopulationResponseModel>,
                ICollection<DataPopulationResponseModel>>(req, result);
    }
}