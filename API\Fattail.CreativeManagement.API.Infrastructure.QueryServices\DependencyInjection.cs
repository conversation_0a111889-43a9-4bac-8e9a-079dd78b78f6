﻿using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeFields;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Creatives;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices;

public static class DependencyInjection
{
    public static void AddQueryServices (this IServiceCollection services)
    {
        services.AddScoped<CreativeQueryService>();
        services.AddScoped<CreativeTemplateQueryService>();
        services.AddScoped<PredefinedCreativeTemplateQueryService>();
        services.AddScoped<CreativeFieldQueryService>();
        services.AddAutoMapper(typeof(DependencyInjection));
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(typeof(DependencyInjection).Assembly));
    }
}