﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeFields.Errors;

internal sealed class CreativeFieldNameInUseError : ErrorBase
{
    public CreativeFieldNameInUseError (string name, string type, string entity) : base(
        $"The creative field with name {name} and type {type} already exists.", ErrorType.DuplicateCreativeFieldName)
    {
        Metadata.Add(nameof(name), name);
        Metadata.Add(nameof(entity), entity);
    }
}