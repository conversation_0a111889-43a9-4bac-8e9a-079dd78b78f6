﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload.Exceptions;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using Moq;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeFilesUpload;

[TestFixture]
public class CreativeFilesUploadHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _creativeFilesUploadPolicyMock = new Mock<ICreativeFilesUploadPolicy>();
        _creativeFilesUploadPolicyMock
            .SetupGet(creativeFilesUploadPolicy => creativeFilesUploadPolicy.AllowedExtensions)
            .Returns(new HashSet<string> { AllowedExtension });
        _creativeFilesUploadPolicyMock
            .SetupGet(creativeFilesUploadPolicy => creativeFilesUploadPolicy.MaxSizeInMegabytesAllowed)
            .Returns(MaxSizeInMegabytesAllowed);

        _creativeFileStorageManagerMock = new Mock<ICreativeFileStorageManager>();
        _creativeFileRepositoryMock = new Mock<ICreativeFileRepository>();

        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Int(1));

        _creativeFileUploadHandler = new CreativeFilesUploadHandler(
            _idManagerMock.Object,
            _creativeFilesUploadPolicyMock.Object,
            _creativeFileStorageManagerMock.Object,
            _creativeFileRepositoryMock.Object);
    }

    private const int MaxSizeInMegabytesAllowed = 1;
    private const string AllowedExtension = ".jpg";
    private const string NotAllowedExtension = ".exe";

    private CreativeFilesUploadHandler _creativeFileUploadHandler = null!;

    private readonly Faker<CreativeFileToUpload> _creativeFileToUploadFaker = new AutoFaker<CreativeFileToUpload>()
        .RuleFor(creativeFileToUpload => creativeFileToUpload.FileName,
            fake => fake.System.CommonFileName(AllowedExtension))
        .RuleFor(creativeFileToUpload => creativeFileToUpload.Content,
            fake => new MemoryStream(fake.Random.Bytes(1024)));
    
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFilesUploadPolicy> _creativeFilesUploadPolicyMock = null!;
    private Mock<ICreativeFileStorageManager> _creativeFileStorageManagerMock = null!;
    private Mock<ICreativeFileRepository> _creativeFileRepositoryMock = null!;

    [Test]
    public async Task Cant_upload_more_than_200_files_at_once ()
    {
        var creativeFileUploadRequest = new CreativeFilesUploadCommand(_creativeFileToUploadFaker.Generate(201));
        await Invoking(() => _creativeFileUploadHandler.Handle(creativeFileUploadRequest, CancellationToken.None))
            .Should().ThrowExactlyAsync<CreativeFilesUploadFileLimitExceededException>();
    }

    [Test]
    public async Task Creative_file_is_stored ()
    {
        const int NumberOfCreativeFilesToUpload = 2;
        var creativeFileUploadRequest =
            new CreativeFilesUploadCommand(_creativeFileToUploadFaker.Generate(NumberOfCreativeFilesToUpload));

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok(AutoFaker.Generate<CreativeFileStorageMetadata>()));

        await _creativeFileUploadHandler.Handle(creativeFileUploadRequest, CancellationToken.None);

        _creativeFileStorageManagerMock.Verify(
            creativeFileManager =>
                creativeFileManager.StoreCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<Stream>()),
            Times.Exactly(NumberOfCreativeFilesToUpload));
    }

    [Test]
    public async Task Creative_file_returns_error_when_storing_fails ()
    {
        const int NumberOfCreativeFilesToUpload = 2;
        var creativeFileUploadRequest =
            new CreativeFilesUploadCommand(_creativeFileToUploadFaker.Generate(NumberOfCreativeFilesToUpload));

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Fail("Error"));

        Result<IReadOnlyList<Result<CreativeFilesUploadResult>>> result =
            await _creativeFileUploadHandler.Handle(creativeFileUploadRequest, CancellationToken.None);

        result.Value.Should().HaveCount(2)
            .And.AllSatisfy(multipleResultItem => multipleResultItem.IsSuccess.Should().BeFalse());
    }

    [Test]
    public async Task Creative_file_is_promoted_to_uploaded ()
    {
        CreativeFileToUpload creativeFileToUpload = _creativeFileToUploadFaker.Generate();
        var creativeFileUploadRequest = new CreativeFilesUploadCommand(new[] { creativeFileToUpload });

        CreativeFileStorageMetadata creativeFileStoredResult = AutoFaker.Generate<CreativeFileStorageMetadata>();

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok(creativeFileStoredResult));

        _creativeFileRepositoryMock.Setup(creativeFileRepository =>
                creativeFileRepository.CreateAsync<CreativeFilesUploadResult>(It.IsAny<CreativeFile>()))
            .ReturnsAsync(new CreativeFilesUploadResult(1, creativeFileToUpload.FileName, ".jpg", 
                creativeFileStoredResult.Location, 12345, new Dictionary<string, string>(), creativeFileStoredResult.UploadedDate));

        Result<IReadOnlyList<Result<CreativeFilesUploadResult>>> result =
            await _creativeFileUploadHandler.Handle(creativeFileUploadRequest, CancellationToken.None);

        result.Value.Should().SatisfyRespectively(multipleResultItem =>
        {
            multipleResultItem.Value.Name.Should().Be(creativeFileToUpload.FileName);
            multipleResultItem.Value.Location.Should().Be(creativeFileStoredResult.Location);
            multipleResultItem.Value.UploadedDate.Should().Be(creativeFileStoredResult.UploadedDate);
        });
    }

    [Test]
    public async Task Uploaded_creative_file_is_created_in_repository ()
    {
        const int NumberOfCreativeFilesToUpload = 2;
        var creativeFileUploadRequest =
            new CreativeFilesUploadCommand(_creativeFileToUploadFaker.Generate(NumberOfCreativeFilesToUpload));

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok(AutoFaker.Generate<CreativeFileStorageMetadata>()));

        _creativeFileRepositoryMock.Setup(creativeFileRepository =>
            creativeFileRepository.CreateAsync<CreativeFilesUploadResult>(It.IsAny<CreativeFile>()));

        await _creativeFileUploadHandler.Handle(creativeFileUploadRequest, CancellationToken.None);

        _creativeFileRepositoryMock.Verify(
            creativeFileRepository =>
                creativeFileRepository.CreateAsync<CreativeFilesUploadResult>(It.IsAny<CreativeFile>()),
            Times.Exactly(NumberOfCreativeFilesToUpload));
    }

    [Test]
    public async Task Uploading_creative_files_with_invalid_files_generates_independent_responses ()
    {
        const int NumberOfValidCreativeFilesToUpload = 2;
        List<CreativeFileToUpload> validCreativeFiles =
            _creativeFileToUploadFaker.Generate(NumberOfValidCreativeFilesToUpload);

        const int NumberOfInvalidCreativeFilesToUpload = 3;
        List<CreativeFileToUpload> invalidCreativeFileWithoutAllowedExtension = new AutoFaker<CreativeFileToUpload>()
            .RuleFor(creativeFileToUpload => creativeFileToUpload.FileName,
                fake => fake.System.CommonFileName(NotAllowedExtension))
            .RuleFor(creativeFileToUpload => creativeFileToUpload.Content,
                fake => new MemoryStream(fake.Random.Bytes(1024 * 1024 * (MaxSizeInMegabytesAllowed + 1))))
            .Generate(NumberOfInvalidCreativeFilesToUpload);

        var creativeFileUploadRequest =
            new CreativeFilesUploadCommand(validCreativeFiles.Concat(invalidCreativeFileWithoutAllowedExtension)
                .ToList());

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<Stream>()))
            .ReturnsAsync(Result.Ok(AutoFaker.Generate<CreativeFileStorageMetadata>()));

        _creativeFileRepositoryMock.Setup(creativeFileRepository =>
            creativeFileRepository.CreateAsync(It.IsAny<CreativeFile>()));

        Result<IReadOnlyList<Result<CreativeFilesUploadResult>>> result =
            await _creativeFileUploadHandler.Handle(creativeFileUploadRequest, CancellationToken.None);

        result.Value.Where(multipleResultItem => multipleResultItem.IsSuccess).Should()
            .HaveCount(NumberOfValidCreativeFilesToUpload);
        result.Value.Where(multipleResultItem => !multipleResultItem.IsSuccess).Should()
            .HaveCount(NumberOfInvalidCreativeFilesToUpload);
    }
}