﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v3.EditCreative;

internal sealed class CreativeEditV3AutoMapperProfile : Profile
{
    internal const string CreativeEditCommandIdParameterName = "Id";

    public CreativeEditV3AutoMapperProfile ()
    {
        CreateMap<CreativeEditRequestV3, CreativeEditCommand>()
            .ForCtorParam(CreativeEditCommandIdParameterName, paramOptions => paramOptions.MapFrom((src, context) => context.Items[CreativeEditCommandIdParameterName]));
    }
}