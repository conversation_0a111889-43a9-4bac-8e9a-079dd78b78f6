﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Migrations;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Serializers;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Fluent;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb;

public static class DependencyInjection
{
    public static void AddCosmosDb (this IServiceCollection services, CosmosDbSettings cosmosDbSettings)
    {
        services.AddOptions<ParallelExecutionSettings>()
            .Configure<IConfiguration>((settings, configuration) =>
                configuration.GetSection(nameof(ParallelExecutionSettings))
                    .Bind(settings));

        CosmosClient client = new CosmosClientBuilder(cosmosDbSettings.EndpointUrl, cosmosDbSettings.PrimaryKey)
            .WithCustomSerializer(new NewtonsoftSerializer())
            .WithBulkExecution(true)
            .WithThrottlingRetryOptions(TimeSpan.FromSeconds(5), 3)
            .Build();

        var cosmosDbClientFactory =
            new CosmosDbContainerFactory(client, cosmosDbSettings.DatabaseName, cosmosDbSettings.Containers);
        services.AddSingleton<ICosmosDbContainerFactory>(cosmosDbClientFactory);

        services.AddAutoMapper(typeof(DependencyInjection));
        services.AddScoped<ICreativeFileRepository, CreativeFileRepository>();
        services.AddScoped<ICreativeTemplateRepository, CreativeTemplateRepository>();
        services.AddScoped<IPredefinedCreativeTemplateRepository, PredefinedCreativeTemplateRepository>();
        services.AddScoped<ICreativeRepository, CreativeRepository>();
        services.AddScoped<ICreativeFieldRepository, CreativeFieldRepository>();
        services.AddScoped<IPredefinedCreativeFieldRepository, PredefinedCreativeFieldRepository>();

        services.AddScoped<ICreativeTemplatesDataMigration, CreativeTemplatesDataMigration>();
    }
}