﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates;

[TestFixture]
public class CloneCreativeTemplateTests
{
    private CreativeFieldUniqueNameRequirement _creativeFieldUniqueNameRequirement = null!;

    [SetUp]
    public void SetUp ()
    {
        _creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
    }

    [Test]
    public void Creative_template_clone_creates_new_template_with_different_id_and_name ()
    {
        CreativeTemplate originalTemplate = CreateTestTemplate(123, "Original Template", CreativeType.Image, true);
        var newId = new CreativeTemplateId(456);
        CreativeTemplateName newName = CreativeTemplateName.Create("Cloned Template", new CreativeTemplateUniqueNameRequirement(true)).Value;
        var cloneRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(true);

        Result<CreativeTemplate> result = originalTemplate.Clone(newId, newName, cloneRequirement);

        result.Should().BeSuccess();
        CreativeTemplate clonedTemplate = result.Value;
        clonedTemplate.Id.Should().Be(newId);
        clonedTemplate.Name.Should().Be(newName);
        clonedTemplate.CreativeType.Should().Be(originalTemplate.CreativeType);
        clonedTemplate.Archived.Should().BeFalse();
        clonedTemplate.CreativeFields.Should().HaveCount(originalTemplate.CreativeFields.Count);
    }

    [Test]
    public void Creative_template_clone_preserves_creative_fields ()
    {
        CreativeTemplate originalTemplate = CreateTestTemplateWithFields(123, "Original Template", CreativeType.Image, true);
        var newId = new CreativeTemplateId(456);
        CreativeTemplateName newName = CreativeTemplateName.Create("Cloned Template", new CreativeTemplateUniqueNameRequirement(true)).Value;
        var cloneRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(true);

        Result<CreativeTemplate> result = originalTemplate.Clone(newId, newName, cloneRequirement);

        result.Should().BeSuccess();
        CreativeTemplate clonedTemplate = result.Value;
        clonedTemplate.CreativeFields.Should().HaveCount(2);

        var originalFieldIds = originalTemplate.CreativeFields.Select(f => f.Id).ToList();
        var clonedFieldIds = clonedTemplate.CreativeFields.Select(f => f.Id).ToList();

        clonedFieldIds.Should().BeEquivalentTo(originalFieldIds);
    }

    [Test]
    public void Creative_template_clone_starts_unarchived ()
    {
        CreativeTemplate originalTemplate = CreateTestTemplate(123, "Original Template", CreativeType.Image, true);

        var newId = new CreativeTemplateId(456);
        CreativeTemplateName newName = CreativeTemplateName.Create("Cloned Template", new CreativeTemplateUniqueNameRequirement(true)).Value;
        var cloneRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(true);

        Result<CreativeTemplate> result = originalTemplate.Clone(newId, newName, cloneRequirement);

        result.Should().BeSuccess();
        CreativeTemplate clonedTemplate = result.Value;
        clonedTemplate.Archived.Should().BeFalse();
    }

    [Test]
    public void Creative_template_cannot_be_cloned_when_predefined_template_already_cloned ()
    {
        CreativeTemplate predefinedTemplate = CreateTestTemplate(123, "Predefined Template", CreativeType.Image, true);
        var newId = new CreativeTemplateId(456);
        CreativeTemplateName newName = CreativeTemplateName.Create("Cloned Template", new CreativeTemplateUniqueNameRequirement(true)).Value;
        var cloneRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(false);

        Result<CreativeTemplate> result = predefinedTemplate.Clone(newId, newName, cloneRequirement);

        result.Should().BeFailure()
            .And.HaveReason<PredefinedCreativeTemplateAlreadyExistsError>(null);
    }

    [Test]
    public void Creative_template_can_be_cloned_when_non_predefined_template_and_requirement_not_satisfied ()
    {
        CreativeTemplate nonPredefinedTemplate = CreateTestTemplate(123, "Non-Predefined Template", CreativeType.Image, false);
        var newId = new CreativeTemplateId(456);
        CreativeTemplateName newName = CreativeTemplateName.Create("Cloned Template", new CreativeTemplateUniqueNameRequirement(true)).Value;
        var cloneRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(false);

        Result<CreativeTemplate> result = nonPredefinedTemplate.Clone(newId, newName, cloneRequirement);

        result.Should().BeSuccess();
        CreativeTemplate clonedTemplate = result.Value;
        clonedTemplate.Id.Should().Be(newId);
        clonedTemplate.Name.Should().Be(newName);
    }

    [Test]
    public void Creative_template_clone_sets_cloned_from_property ()
    {
        var originalTemplateId = new CreativeTemplateId(123);
        CreativeTemplate originalTemplate = CreateTestTemplate(123, "Original Template", CreativeType.Image, true);
        var newId = new CreativeTemplateId(456);
        CreativeTemplateName newName = CreativeTemplateName.Create("Cloned Template", new CreativeTemplateUniqueNameRequirement(true)).Value;
        var cloneRequirement = new PredefinedCreativeTemplateUniqueTypeRequirement(true);

        Result<CreativeTemplate> result = originalTemplate.Clone(newId, newName, cloneRequirement);

        result.Should().BeSuccess();
        CreativeTemplate clonedTemplate = result.Value;
        clonedTemplate.ClonedFrom.Should().Be(originalTemplateId);
    }

    private CreativeTemplate CreateTestTemplate (long id, string name, CreativeType type, bool predefined)
    {
        CreativeTemplateName templateName = CreativeTemplateName.Create(name, new CreativeTemplateUniqueNameRequirement(true)).Value;

        var creativeFieldUniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), "Test Field", CreativeFieldType.SingleLineText, creativeFieldUniqueNameRequirement, false, null, null).Value;
        var creativeFields = new HashSet<CreativeField> { creativeField };
        var displayOrderFields = new List<DisplayOrderCreativeField> { new DisplayOrderCreativeField(new CreativeFieldId(1), 1) };

        var createRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            templateName,
            type,
            displayOrderFields,
            creativeFields,
            predefined);

        return CreativeTemplateFactory.Create(createRequest).Value;
    }

    private CreativeTemplate CreateTestTemplateWithFields (long id, string name, CreativeType type, bool predefined)
    {
        CreativeTemplateName templateName = CreativeTemplateName.Create(name, new CreativeTemplateUniqueNameRequirement(true)).Value;

        CreativeField field1 = CreativeField.Create(new CreativeFieldId(1), "Field 1", CreativeFieldType.SingleLineText, _creativeFieldUniqueNameRequirement, predefined, null, null).Value;
        CreativeField field2 = CreativeField.Create(new CreativeFieldId(2), "Field 2", CreativeFieldType.FileUpload, _creativeFieldUniqueNameRequirement, predefined, null, null).Value;

        var createRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(id),
            templateName,
            type,
            new List<DisplayOrderCreativeField>
            {
                new(new CreativeFieldId(1), 1),
                new(new CreativeFieldId(2), 2)
            },
            new HashSet<CreativeField> { field1, field2 },
            predefined);

        return CreativeTemplateFactory.Create(createRequest).Value;
    }
}

