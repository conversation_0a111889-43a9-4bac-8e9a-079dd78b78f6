﻿using Bogus;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using Moq;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields.Validators;

[TestFixture]
public class FileSizeValidatorTests
{
    private static readonly List<string> _validationOptions = new() { "30" };

    private readonly string _allowedFileSize = _validationOptions.First();
    
    private CreativeFieldIdentifier _creativeFieldIdentifier;

    private readonly IEnumerable<ValidationRule> _validationRule = new List<ValidationRule>
    {
        ValidationRule.Create(CreativeFieldValidationRuleType.FileSize, _validationOptions, new CreativeFieldId(1234)).Value
    };

    protected FileSizeRuleValidator _fileSizeRuleValidator = null!;
    private Mock<ICreativeFilesUploadPolicy> _creativeFilesUploadPolicyMock = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFilesManager> _creativeFilesManager = null!;

    [SetUp]
    public void SetUp ()
    {
        var creativeFieldId = new CreativeFieldId(123456);
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;
        _creativeFieldIdentifier = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);
        
        _creativeFilesUploadPolicyMock = new Mock<ICreativeFilesUploadPolicy>();
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.MaxSizeInMegabytesAllowed).Returns(1000);
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.AllowedExtensions).Returns(
            new HashSet<string>(StringComparer.FromComparison(StringComparison.OrdinalIgnoreCase))
            {
                ".txt", ".pdf", ".jpg"
            });

        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));
    }

    [Test]
    public async Task Single_file_with_valid_size_is_valid ()
    {
        _creativeFilesManager = new Mock<ICreativeFilesManager>();
        _creativeFilesManager.Setup(cfm => cfm.GetFileSizeInMegabytes(It.IsAny<CreativeFile>())).ReturnsAsync(20);
        _fileSizeRuleValidator = new FileSizeRuleValidator(_validationRule, _creativeFilesManager.Object, _creativeFieldIdentifier);
        IReadOnlyList<CreativeFile?> creativeFiles = new List<CreativeFile?> { GetCreativeFile(1234, "file1.txt") };
        
        Result result = await _fileSizeRuleValidator.IsValid(creativeFiles);
        
        result.Should().BeSuccess();
    }

    [Test]
    public async Task Single_file_with_invalid_size_is_invalid ()
    {
        _creativeFilesManager = new Mock<ICreativeFilesManager>();
        _creativeFilesManager.Setup(cfm => cfm.GetFileSizeInMegabytes(It.IsAny<CreativeFile>())).ReturnsAsync(50);
        _fileSizeRuleValidator = new FileSizeRuleValidator(_validationRule, _creativeFilesManager.Object, _creativeFieldIdentifier);
        IReadOnlyList<CreativeFile?> creativeFiles = new List<CreativeFile?> { GetCreativeFile(1234, "file1.txt") };
        
        Result result = await _fileSizeRuleValidator.IsValid(creativeFiles);

        result.Should().BeFailure().And
            .HaveReason(new FileSizeValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldIdentifier.Id.ToString(), _allowedFileSize));
    }

    [Test]
    public async Task Multiple_files_with_valid_size_is_valid ()
    {
        _creativeFilesManager = new Mock<ICreativeFilesManager>();
        _creativeFilesManager.Setup(cfm => cfm.GetFileSizeInMegabytes(It.IsAny<CreativeFile>())).ReturnsAsync(20);
        _fileSizeRuleValidator = new FileSizeRuleValidator(_validationRule, _creativeFilesManager.Object, _creativeFieldIdentifier);
        IReadOnlyList<CreativeFile?> creativeFiles =
            new List<CreativeFile?> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(2342, "file2.txt") };
        
        Result result = await _fileSizeRuleValidator.IsValid(creativeFiles);
        
        result.Should().BeSuccess();
    }

    [Test]
    public async Task Multiple_files_with_all_invalid_sizes_is_invalid ()
    {
        _creativeFilesManager = new Mock<ICreativeFilesManager>();
        _creativeFilesManager.Setup(cfm => cfm.GetFileSizeInMegabytes(It.IsAny<CreativeFile>())).ReturnsAsync(50);
        _fileSizeRuleValidator = new FileSizeRuleValidator(_validationRule, _creativeFilesManager.Object, _creativeFieldIdentifier);
        IReadOnlyList<CreativeFile?> creativeFiles =
            new List<CreativeFile?> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(2342, "file2.txt") };
        
        Result result = await _fileSizeRuleValidator.IsValid(creativeFiles);
        
        result.Should().BeFailure().And
            .HaveReason(new FileSizeValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldIdentifier.Id.ToString(), _allowedFileSize));
        result.Errors.Should().ContainSingle();
    }

    [Test]
    public async Task Multiple_files_with_one_invalid_size_is_invalid ()
    {
        _creativeFilesManager = new Mock<ICreativeFilesManager>();
        _creativeFilesManager.Setup(cfm => cfm.GetFileSizeInMegabytes(It.IsAny<CreativeFile>())).Returns<CreativeFile>(
            (creativeFile) => Task.FromResult(creativeFile.Id == 1234 ? 10d : 50d));
        _fileSizeRuleValidator = new FileSizeRuleValidator(_validationRule, _creativeFilesManager.Object, _creativeFieldIdentifier);
        IReadOnlyList<CreativeFile?> creativeFiles =
            new List<CreativeFile?> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(2342, "file2.txt") };
        
        Result result = await _fileSizeRuleValidator.IsValid(creativeFiles);
        
        result.Should().BeFailure().And
            .HaveReason(new FileSizeValidationError(nameof(CreativeFieldValue), nameof(Creative), _creativeFieldIdentifier.Id.ToString(), _allowedFileSize));
    }

    private CreativeFile GetCreativeFile (long id, string fileName, long fileSize = 20)
    {
        var creativeFileName = CreativeFileName.From(fileName);

        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(id);

        return CreativeFile.PrepareToUpload(_creativeFilesUploadPolicyMock.Object,
            _idManagerMock.Object, creativeFileName, Stream.Null).Value;
    }
}