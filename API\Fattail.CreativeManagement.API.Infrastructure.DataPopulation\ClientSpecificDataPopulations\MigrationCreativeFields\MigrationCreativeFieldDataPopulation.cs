﻿using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Common.Repositories;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Utilities;
using Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ViewModels;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Infrastructure.DataPopulation.ClientSpecificDataPopulations.MigrationCreativeFields;

[ExcludeFromCodeCoverage]
public class MigrationCreativeFieldDataPopulation
{
    private static readonly List<string> _listOrgid = new() { "100687" };
    private static readonly MigrationCreativeFieldList _migrationCreativeFieldList = new(_listOrgid);


    public async static Task<ICollection<DataPopulationResponseModel>> RunAsync (
        IDataPopulationRepository<CreativeField> creativeFieldItemResponseRepository)
    {
        var results = new List<DataPopulationResponseModel>();

        foreach (CreativeField creativeField in _migrationCreativeFieldList.CreativeFields)
        {
            results.Add(
                await DataPopulationUtilities.ExecuteUpsertAsync(
                    creativeFieldItemResponseRepository, creativeField, creativeField.OrgId)
            );
        }

        return results;
    }
}