﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Configuration.AutoMapper.CustomTypeConverters;

internal sealed class CreativeFileIdTypeConverter :
    ITypeConverter<IEnumerable<CreativeFileId>, CreativeFileValue[]>,
    ITypeConverter<CreativeFileId?, CreativeFileValue?>
{
    private readonly ICreativeFileRepository _creativeFileRepository;

    public CreativeFileIdTypeConverter (ICreativeFileRepository creativeFileRepository)
    {
        _creativeFileRepository = creativeFileRepository;
    }

    public CreativeFileValue? Convert (CreativeFileId? source, CreativeFileValue? destination, ResolutionContext context)
    {
        if (source is null)
        {
            return null;
        }

        Task<CreativeFileValue?> creativeFilesFindByIdATask =
            _creativeFileRepository.FindByIdAsync<CreativeFileValue>(source);
        creativeFilesFindByIdATask.Wait();

        return creativeFilesFindByIdATask.Result!;
    }

    public CreativeFileValue[] Convert (IEnumerable<CreativeFileId> source, CreativeFileValue[] destination,
        ResolutionContext context)
    {
        Task<IReadOnlyList<CreativeFileValue>> creativeFilesFindManyByIdTask =
            _creativeFileRepository.FindManyByIdAsync<CreativeFileValue>(source.ToArray());
        creativeFilesFindManyByIdTask.Wait();

        IReadOnlyList<CreativeFileValue> creativeFiles = creativeFilesFindManyByIdTask.Result;

        return creativeFiles.ToArray();
    }
}