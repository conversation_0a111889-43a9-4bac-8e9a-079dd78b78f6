using Fattail.CreativeManagement.API.Domain.Common.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;

public class LongRequiredRuleValidator : IRuleValidator<long?>
{
    private readonly string _creativeFieldId;

    public LongRequiredRuleValidator(CreativeFieldIdentifier creativeFieldIdentifier)
    {
        _creativeFieldId = creativeFieldIdentifier.Id.ToString();
    }

    public Task<Result> IsValid(long? fieldValue)
    {
        var result = Result.Ok();

        if (fieldValue is null || fieldValue <= 0)
        {
            result = Result.Fail(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldId));
        }

        return Task.FromResult(result);
    }
}
