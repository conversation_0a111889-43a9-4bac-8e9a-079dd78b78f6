namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow.Models;

public class CampaignCreativeWorkflow
{
    public CampaignCreativeWorkflow ()
    {
        CampaignCreatives = new List<CampaignCreative>();
    }

    public List<CampaignCreative> CampaignCreatives { get; set; }
    public Trigger Trigger { get; set; }
}

public class CampaignCreative
{
    public CampaignCreative (long creativeId, long campaignId)
    {
        CreativeId = creativeId;
        CampaignId = campaignId;
    }

    public long CreativeId { get; }
    public long CampaignId { get; }
}

public class Trigger
{
    public string TriggerType { get; set; }
}