﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;

internal sealed class CreativeFileExtensionNotAllowedError : ErrorBase
{
    public CreativeFileExtensionNotAllowedError (string creativeFileName, IEnumerable<string> allowedExtensions)
        : base(
            $"Incorrect extension for file: '{creativeFileName}'. Allowed file types/extensions: {string.Join(", ", allowedExtensions)}",
            ErrorType.CreativeFileExtensionNotAllowed)
    {
        Metadata.Add(nameof(creativeFileName), creativeFileName);
        Metadata.Add(nameof(allowedExtensions), string.Join(", ", allowedExtensions));
    }
}