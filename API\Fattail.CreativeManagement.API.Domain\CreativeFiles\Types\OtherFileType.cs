﻿namespace Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;

public sealed class OtherFileType : CreativeFileType
{
    public OtherFileType() : base(CreativeFileTypeEnum.Other.ToString(), (int)CreativeFileTypeEnum.Other)
    {
    }

    protected override bool MatchesExtension (FileExtension extension)
    {
        return false;
    }

    public override CreativeFileMetadata GenerateMetadata (Stream creativeFileStream)
    {
        return CreativeFileMetadata.Empty;
    }
}