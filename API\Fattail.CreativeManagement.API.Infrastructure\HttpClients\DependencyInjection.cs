﻿using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.AdBookWorkflow;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Authentication;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Handlers;
using Fattail.CreativeManagement.API.Infrastructure.HttpClients.Utils;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;

namespace Fattail.CreativeManagement.API.Infrastructure.HttpClients;

internal static class DependencyInjection
{
    internal static void AddHttpClients (this IServiceCollection services)
    {
        services.AddOptions<AuthenticationHttpClientConfiguration>()
            .Configure<IConfiguration>((settings, configuration) =>
                configuration.GetSection(AuthenticationHttpClientConfiguration.AuthenticationHttpClient)
                    .Bind(settings));

        services.AddOptions<AdBookWorkflowHttpClientConfiguration>()
            .Configure<IConfiguration>((settings, configuration) =>
            {
                settings.AdBookWorkflowBaseUri =
                    configuration.GetValue<string>(nameof(settings.AdBookWorkflowBaseUri));
                settings.AdBookEwScrippsWorkflowBaseUri =
                    configuration.GetValue<string>(nameof(settings.AdBookEwScrippsWorkflowBaseUri));
            });
        services.AddTransient<AuthenticationDelegatingHandler>();

        services.AddHttpClient<IAuthenticationHttpClient, AuthenticationHttpClient>()
            .AddTransientHttpErrorPolicy(builder =>
                builder.WaitAndRetryAsync(new[]
                {
                    TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(3), TimeSpan.FromSeconds(5)
                }));

        services.AddSingleton<AuthenticationManager>();

        services.AddHttpClient<IAdBookWorkflowHttpClient, AdBookWorkflowHttpClient>()
            .AddHttpMessageHandler<AuthenticationDelegatingHandler>()
            .AddPolicyHandler(GetWaitAndRetryPolicy);
    }

    private static IAsyncPolicy<HttpResponseMessage> GetWaitAndRetryPolicy (IServiceProvider services, HttpRequestMessage request)
    {
        return HttpPolicyExtensions.HandleTransientHttpError()
            .WaitAndRetryAsync(new[] { TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(3), TimeSpan.FromSeconds(4) },
                onRetryAsync: async (responseMessage, _, retryAttempt, _) =>
                {
                    //TODO: Verify if this is what we want to log while retrying, because the final exception will be thrown anyway and can be handled in the middleware or in the trigger.
                    if (responseMessage?.Result is not null)
                    {
                        string responseContent = await responseMessage.Result.Content.ReadAsStringAsync();
                        services.GetService<ILogger<AdBookWorkflowHttpClient>>()?
                            .LogWarning("Retry attempt: {retryAttempt} Request: {requestUri} Status Code: {statusCode} Message: {responseContent}",
                                retryAttempt,
                                request.RequestUri?.AbsoluteUri,
                                responseMessage.Result.StatusCode,
                                responseContent);
                    }
                }
            );
    }
}