﻿using Fattail.CreativeManagement.API.Domain.Common;
using FluentResults;
using System.Linq.Expressions;

namespace Fattail.CreativeManagement.API.Domain.Repositories;

public interface IRepository<TDomainEntity, in TId>
    where TDomainEntity : Entity<TId>
{
    Task<TDomainEntity?> FindByIdAsync (TId id);
    Task<TResult?> FindByIdAsync<TResult> (TId id);
    Task<TDomainEntity?> FindAsync (Expression<Func<TDomainEntity, bool>> predicate);
    Task<TDomainEntity?> FindAsync (Specification<TDomainEntity> specification);
    Task<IReadOnlyList<TDomainEntity>> FindManyAsync (Expression<Func<TDomainEntity, bool>> predicate);
    Task<IReadOnlyList<TResult>> FindManyByIdAsync<TResult> (IEnumerable<TId> ids);
    Task CreateAsync (TDomainEntity entity);
    Task<TResult> CreateAsync<TResult> (TDomainEntity entity);
    Task<TResult> MigrateAsync<TResult> (TDomainEntity entity);
    Task<TResult> UpdateAsync<TResult> (TDomainEntity entity);
    Task<Result> DeleteAsync (TId id);
}