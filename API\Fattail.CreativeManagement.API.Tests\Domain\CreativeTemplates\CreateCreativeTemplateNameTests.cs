using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeTemplates;

[TestFixture]
public class CreateCreativeTemplateNameTests
{
    [TestCase("Valid Name")]
    public void Creative_template_name_can_be_created(string name)
    {
        var requirement = new CreativeTemplateUniqueNameRequirement(true);
        Result<CreativeTemplateName> result = CreativeTemplateName.Create(name, requirement);

        result.Should().BeSuccess();
        result.Value.ToString().Should().Be(name);
    }

    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public void Creative_template_name_cannot_be_empty_or_null(string? name)
    {
        var requirement = new CreativeTemplateUniqueNameRequirement(true);
        Result<CreativeTemplateName> result = CreativeTemplateName.Create(name, requirement);

        result.Should().BeFailure()
            .And.HaveReason(new RequiredValueMissingError("creative template name", nameof(CreativeTemplate)));
    }

    [Test]
    public async Task Creative_template_cannot_be_created_when_unique_name_requirement_is_not_met ()
    {
        string creativeTemplateName = "Creative template name";
        var requirement = new CreativeTemplateUniqueNameRequirement(false);

        Result<CreativeTemplateName> result = CreativeTemplateName.Create(creativeTemplateName, requirement);

        result.Should().BeFailure()
            .And.HaveReason(new CreativeTemplateNameInUseError("creative template name", nameof(CreativeTemplate)));
    }

    [Test]
    public void Creative_template_name_value_is_trimmed()
    {
        var requirement = new CreativeTemplateUniqueNameRequirement(true);
        string name = "   My Template   ";
        Result<CreativeTemplateName> result = CreativeTemplateName.Create(name, requirement);

        result.Should().BeSuccess();
        result.Value.ToString().Should().Be("My Template");
    }

    [Test]
    public void Creative_template_name_equality_works()
    {
        var requirement = new CreativeTemplateUniqueNameRequirement(true);
        CreativeTemplateName name1 = CreativeTemplateName.Create("Test", requirement).Value;
        CreativeTemplateName name2 = CreativeTemplateName.Create("Test", requirement).Value;
        CreativeTemplateName name3 = CreativeTemplateName.Create("Other", requirement).Value;

        name1.Should().Be(name2);
        name1.Should().NotBe(name3);
    }
}
