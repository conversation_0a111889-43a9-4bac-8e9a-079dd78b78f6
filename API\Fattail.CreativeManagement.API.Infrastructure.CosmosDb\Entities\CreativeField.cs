using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;

public class CreativeField : Entity
{
    public string Name { get; set; }

    public CreativeFieldTypeEnum Type { get; set; }

    public bool Predefined { get; set; } = false;

    public string? OmsExternalIdentifier { get; set; }

    public CreativeFieldSettings Settings { get; set; }

    public SelectOption[]? Options { get; set; }
}

public abstract class CreativeFieldSettings
{
}

public class DefaultCreativeFieldSettings : CreativeFieldSettings
{
}

public class SelectCreativeFieldSettings : CreativeFieldSettings
{
    public SelectOption[] Options { get; set; } = [];
}

public class SelectOption
{
    public string Id { get; set; } = null!;
    public string Description { get; set; } = null!;
};