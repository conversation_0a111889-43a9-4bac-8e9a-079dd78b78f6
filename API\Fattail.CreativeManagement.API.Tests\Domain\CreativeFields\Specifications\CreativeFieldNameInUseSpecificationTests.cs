using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Settings;
using Fattail.CreativeManagement.API.Domain.CreativeFields.Specifications;
using FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.CreativeFields.Specifications;

[TestFixture]
public class CreativeFieldNameInUseSpecificationTests
{
    [Test]
    public void Creative_field_name_in_use_when_creative_field_name_and_type_are_already_in_use ()
    {
        string name = "Test Field";
        CreativeFieldType type = CreativeFieldType.SingleLineText;
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), name, type, new CreativeFieldUniqueNameRequirement(true), false, null, null).Value;
        var spec = new CreativeFieldNameInUseSpecification(name, type);

        bool result = spec.IsSatisfiedBy(creativeField);

        result.Should().BeTrue();
    }

    [Test]
    public void Creative_field_name_not_in_use_when_creative_field_name_is_not_in_use_for_given_type ()
    {
        string name = "Test Field";
        CreativeFieldType type = CreativeFieldType.SingleLineText;
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), "Other Field", type, new CreativeFieldUniqueNameRequirement(true), false, null, null).Value;
        var spec = new CreativeFieldNameInUseSpecification(name, type);

        bool result = spec.IsSatisfiedBy(creativeField);

        result.Should().BeFalse();
    }

    [Test]
    public void Creative_field_name_not_in_use_when_creative_field_type_is_not_in_use_for_given_name ()
    {
        string name = "Test Field";
        CreativeFieldType type = CreativeFieldType.SingleLineText;
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), name, CreativeFieldType.FileUpload, new CreativeFieldUniqueNameRequirement(true), false, null, null).Value;
        var spec = new CreativeFieldNameInUseSpecification(name, type);

        bool result = spec.IsSatisfiedBy(creativeField);

        result.Should().BeFalse();
    }

    [Test]
    public void Creative_field_name_not_in_use_when_creative_field_name_and_type_are_not_in_use ()
    {
        string name = "Test Field";
        CreativeFieldType type = CreativeFieldType.SingleLineText;
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), "Other Field", CreativeFieldType.FileUpload, new CreativeFieldUniqueNameRequirement(true), false, null, null).Value;
        var spec = new CreativeFieldNameInUseSpecification(name, type);

        bool result = spec.IsSatisfiedBy(creativeField);

        result.Should().BeFalse();
    }

    [Test]
    public void Creative_field_name_not_in_use_when_creative_field_type_is_null_even_if_name_is_in_use ()
    {
        string name = "Test Field";
        CreativeFieldType? type = null;
        CreativeField creativeField = CreativeField.Create(new CreativeFieldId(1), name, CreativeFieldType.SingleLineText, new CreativeFieldUniqueNameRequirement(true), false, null, null).Value;
        var spec = new CreativeFieldNameInUseSpecification(name, type);

        bool result = spec.IsSatisfiedBy(creativeField);

        result.Should().BeFalse();
    }
}
