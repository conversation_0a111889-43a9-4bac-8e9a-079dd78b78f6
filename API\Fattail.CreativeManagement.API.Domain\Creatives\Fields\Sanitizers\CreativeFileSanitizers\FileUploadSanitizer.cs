﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.CreativeFileSanitizers;

public class FileUploadSanitizer : ISanitizer
{
    private readonly ICreativeFilesVerifier _creativeFilesVerifier;

    public FileUploadSanitizer (ICreativeFilesVerifier creativeFilesVerifier)
    {
        _creativeFilesVerifier = creativeFilesVerifier;
    }

    public CreativeFieldType CreativeFieldType => CreativeFieldType.FileUpload;

    public async Task<T?> Sanitize<T> (object? value) where T : class
    {
        return value is not CreativeFileId creativeFile
            ? throw new ArgumentException($"{nameof(value)} must be a type compatible with {typeof(CreativeFileId)}")
            : (await _creativeFilesVerifier.GetExistingCreativeFilesFrom(creativeFile)).SingleOrDefault() as T;
    }
}