﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v2.AddCreative;

public sealed class CreativeAddFunctionV2 : BaseFunction
{
    public CreativeAddFunctionV2 (
        IMediator mediator,
        IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Add creative and assign it to a campaign v2", tags: new[] { "v2" },
        Deprecated = true,
        Summary = "Add a creative and assign it to a campaign v2")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeAddRequestV2),
        Description = "The creative to add and assign to the campaign", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.Created, "application/json", typeof(CreativeResponseV2))]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeAddFunctionV2))]
    public async Task<HttpResponseData> AddCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "v2/creatives")]
        HttpRequestData req)
    {
        CreativeAddCommand creativeAddCommand = await FromRequest<CreativeAddRequestV2, CreativeAddCommand>(req);

        Result<CreativeResult> result = await _mediator.Send(creativeAddCommand);

        HttpResponseData response =
            await FromResultWithLocation<CreativeResult, CreativeResponseV2>(req, result, result.ValueOrDefault?.Id);

        return response;
    }
}