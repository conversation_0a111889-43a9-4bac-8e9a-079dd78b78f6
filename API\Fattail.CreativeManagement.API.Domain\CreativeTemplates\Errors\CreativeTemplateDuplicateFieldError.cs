﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateDuplicateFieldError : ErrorBase
{
    public CreativeTemplateDuplicateFieldError (CreativeFieldId creativeFieldId, string entity)
        : base($"The creative field with id {creativeFieldId} has been added to the template more than once.",
            ErrorType.DuplicateCreativeTemplateField)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
    }
}