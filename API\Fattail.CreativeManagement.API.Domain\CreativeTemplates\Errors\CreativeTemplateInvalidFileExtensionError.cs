﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateInvalidFileExtensionError : ErrorBase
{
    public CreativeTemplateInvalidFileExtensionError (string creativeFieldId, string entity, string validationRuleType)
        : base(
            $"The creative field with id {creativeFieldId} has invalid file format extension for validation type {validationRuleType}. The format should be: .[fileformat].",
            ErrorType.CreativeTemplateFieldInvalidFileExtension)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(validationRuleType), validationRuleType);
    }
}