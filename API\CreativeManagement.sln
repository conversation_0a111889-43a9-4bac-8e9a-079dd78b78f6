﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32519.379
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{CE786CE2-9CAA-4AE1-9E5F-F97C264D3AFA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Azure", "Fattail.CreativeManagement.API.Azure\Fattail.CreativeManagement.API.Azure.csproj", "{F2A1392A-A7C1-43DE-AFCC-25463D6F68F3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure.DataPopulation", "Fattail.CreativeManagement.API.Infrastructure.DataPopulation\Fattail.CreativeManagement.API.Infrastructure.DataPopulation.csproj", "{52C9D512-9D78-462E-A33D-F06759929E0A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Tests", "Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Tests\Fattail.CreativeManagement.API.Infrastructure.DataPopulation.Tests.csproj", "{3DC4B0F1-5450-46CC-A403-E2E4E3675699}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure.Tests", "Fattail.CreativeManagement.API.Infrastructure.Tests\Fattail.CreativeManagement.API.Infrastructure.Tests.csproj", "{8DCBEEDD-9FEB-4049-A66E-71B57C190DA2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure", "Fattail.CreativeManagement.API.Infrastructure\Fattail.CreativeManagement.API.Infrastructure.csproj", "{CE9479D3-92C6-4406-94A6-726381EAD1F7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Application", "Fattail.CreativeManagement.API.Application\Fattail.CreativeManagement.API.Application.csproj", "{BC1B6835-A221-4566-8E22-BBC91F7C94E7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Domain", "Fattail.CreativeManagement.API.Domain\Fattail.CreativeManagement.API.Domain.csproj", "{447C9EA2-1F5F-49DF-B948-54D687389EC9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Tests", "Fattail.CreativeManagement.API.Tests\Fattail.CreativeManagement.API.Tests.csproj", "{9ECB458C-2A16-4D6C-9CED-48D2B950AD21}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Clean Architecture", "Clean Architecture", "{B7459788-A24F-4972-BDCA-7EF8843E1FF9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure.CosmosDb", "Fattail.CreativeManagement.API.Infrastructure.CosmosDb\Fattail.CreativeManagement.API.Infrastructure.CosmosDb.csproj", "{5466C0D9-51EA-45BB-818C-7C22F07B5157}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure.QueryServices", "Fattail.CreativeManagement.API.Infrastructure.QueryServices\Fattail.CreativeManagement.API.Infrastructure.QueryServices.csproj", "{13ED82C8-C446-4516-B7D1-************}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "02 Application Business Rules (Use cases)", "02 Application Business Rules (Use cases)", "{93DE2554-599D-42B7-8FF7-AEAF2848DBCD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "01 Enterprise Business Rules (Entities)", "01 Enterprise Business Rules (Entities)", "{676B6FBB-F40D-4976-AE4F-231A8D375E39}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "03 Interface Adapters", "03 Interface Adapters", "{DC611429-1CB0-48F7-8E15-5B9C435FC02E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Fattail.CreativeManagement.API.Infrastructure.TriggerServices", "Fattail.CreativeManagement.API.Infrastructure.TriggerServices\Fattail.CreativeManagement.API.Infrastructure.TriggerServices.csproj", "{43F39FC1-BCC4-4037-852F-EEC382DFE5C0}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F2A1392A-A7C1-43DE-AFCC-25463D6F68F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2A1392A-A7C1-43DE-AFCC-25463D6F68F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2A1392A-A7C1-43DE-AFCC-25463D6F68F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2A1392A-A7C1-43DE-AFCC-25463D6F68F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{52C9D512-9D78-462E-A33D-F06759929E0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{52C9D512-9D78-462E-A33D-F06759929E0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{52C9D512-9D78-462E-A33D-F06759929E0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{52C9D512-9D78-462E-A33D-F06759929E0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3DC4B0F1-5450-46CC-A403-E2E4E3675699}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3DC4B0F1-5450-46CC-A403-E2E4E3675699}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3DC4B0F1-5450-46CC-A403-E2E4E3675699}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3DC4B0F1-5450-46CC-A403-E2E4E3675699}.Release|Any CPU.Build.0 = Release|Any CPU
		{8DCBEEDD-9FEB-4049-A66E-71B57C190DA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8DCBEEDD-9FEB-4049-A66E-71B57C190DA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8DCBEEDD-9FEB-4049-A66E-71B57C190DA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8DCBEEDD-9FEB-4049-A66E-71B57C190DA2}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE9479D3-92C6-4406-94A6-726381EAD1F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE9479D3-92C6-4406-94A6-726381EAD1F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE9479D3-92C6-4406-94A6-726381EAD1F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE9479D3-92C6-4406-94A6-726381EAD1F7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BC1B6835-A221-4566-8E22-BBC91F7C94E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BC1B6835-A221-4566-8E22-BBC91F7C94E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BC1B6835-A221-4566-8E22-BBC91F7C94E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BC1B6835-A221-4566-8E22-BBC91F7C94E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{447C9EA2-1F5F-49DF-B948-54D687389EC9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{447C9EA2-1F5F-49DF-B948-54D687389EC9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{447C9EA2-1F5F-49DF-B948-54D687389EC9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{447C9EA2-1F5F-49DF-B948-54D687389EC9}.Release|Any CPU.Build.0 = Release|Any CPU
		{9ECB458C-2A16-4D6C-9CED-48D2B950AD21}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9ECB458C-2A16-4D6C-9CED-48D2B950AD21}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9ECB458C-2A16-4D6C-9CED-48D2B950AD21}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9ECB458C-2A16-4D6C-9CED-48D2B950AD21}.Release|Any CPU.Build.0 = Release|Any CPU
		{5466C0D9-51EA-45BB-818C-7C22F07B5157}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5466C0D9-51EA-45BB-818C-7C22F07B5157}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5466C0D9-51EA-45BB-818C-7C22F07B5157}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5466C0D9-51EA-45BB-818C-7C22F07B5157}.Release|Any CPU.Build.0 = Release|Any CPU
		{13ED82C8-C446-4516-B7D1-************}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{13ED82C8-C446-4516-B7D1-************}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{13ED82C8-C446-4516-B7D1-************}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{13ED82C8-C446-4516-B7D1-************}.Release|Any CPU.Build.0 = Release|Any CPU
		{43F39FC1-BCC4-4037-852F-EEC382DFE5C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{43F39FC1-BCC4-4037-852F-EEC382DFE5C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{43F39FC1-BCC4-4037-852F-EEC382DFE5C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{43F39FC1-BCC4-4037-852F-EEC382DFE5C0}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F2A1392A-A7C1-43DE-AFCC-25463D6F68F3} = {DC611429-1CB0-48F7-8E15-5B9C435FC02E}
		{52C9D512-9D78-462E-A33D-F06759929E0A} = {DC611429-1CB0-48F7-8E15-5B9C435FC02E}
		{3DC4B0F1-5450-46CC-A403-E2E4E3675699} = {CE786CE2-9CAA-4AE1-9E5F-F97C264D3AFA}
		{8DCBEEDD-9FEB-4049-A66E-71B57C190DA2} = {CE786CE2-9CAA-4AE1-9E5F-F97C264D3AFA}
		{CE9479D3-92C6-4406-94A6-726381EAD1F7} = {DC611429-1CB0-48F7-8E15-5B9C435FC02E}
		{BC1B6835-A221-4566-8E22-BBC91F7C94E7} = {93DE2554-599D-42B7-8FF7-AEAF2848DBCD}
		{447C9EA2-1F5F-49DF-B948-54D687389EC9} = {676B6FBB-F40D-4976-AE4F-231A8D375E39}
		{9ECB458C-2A16-4D6C-9CED-48D2B950AD21} = {B7459788-A24F-4972-BDCA-7EF8843E1FF9}
		{5466C0D9-51EA-45BB-818C-7C22F07B5157} = {DC611429-1CB0-48F7-8E15-5B9C435FC02E}
		{13ED82C8-C446-4516-B7D1-************} = {DC611429-1CB0-48F7-8E15-5B9C435FC02E}
		{93DE2554-599D-42B7-8FF7-AEAF2848DBCD} = {B7459788-A24F-4972-BDCA-7EF8843E1FF9}
		{676B6FBB-F40D-4976-AE4F-231A8D375E39} = {B7459788-A24F-4972-BDCA-7EF8843E1FF9}
		{DC611429-1CB0-48F7-8E15-5B9C435FC02E} = {B7459788-A24F-4972-BDCA-7EF8843E1FF9}
		{43F39FC1-BCC4-4037-852F-EEC382DFE5C0} = {DC611429-1CB0-48F7-8E15-5B9C435FC02E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6D6E398D-7DB0-4CF6-BB6A-032AC5B850F3}
	EndGlobalSection
EndGlobal
