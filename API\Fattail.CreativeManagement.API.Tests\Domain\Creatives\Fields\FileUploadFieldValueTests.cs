﻿using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using Moq;
using NUnit.Framework;
using static FluentAssertions.FluentActions;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields;

[TestFixture]
public class FileUploadFieldValueTests
{
    [SetUp]
    public void SetUp ()
    {
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider => sanitizerStrategyProvider.GetFrom(CreativeFieldType.FileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeFilesUploadPolicyMock = new Mock<ICreativeFilesUploadPolicy>();
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.MaxSizeInMegabytesAllowed).Returns(1000);
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.AllowedExtensions).Returns(new HashSet<string>(StringComparer.FromComparison(StringComparison.OrdinalIgnoreCase)) { ".txt", ".pdf" });

        _idManagerMock = new Mock<IIdManager>();
    }

    private Mock<ICreativeFilesUploadPolicy> _creativeFilesUploadPolicyMock = null!;
    private Mock<IIdManager> _idManagerMock = null!;

    private static readonly CreativeFieldType[] _invalidCreativeFieldTypes =
        CreativeFieldType.List
        .Where(creativeFieldType => creativeFieldType != CreativeFieldType.FileUpload)
        .ToArray();

    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    private Mock<ISanitizer> _sanitizerMock = null!;

    [Test]
    public async Task File_upload_field_value_can_be_created ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.FileUpload);
        var creativeFile = new CreativeFileId(1234);

        CreativeFile fileValue = GetCreativeFile(1234, "file1.txt");

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<CreativeFile>(It.IsAny<object?>()))
            .ReturnsAsync(fileValue);

        FileUploadFieldValue fileUploadFieldValue =
            (await FileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, creativeFile, new List<ValidationRule>(), null)).Value;

        fileUploadFieldValue.CreativeFieldIdentifier.Should().Be(creativeField);
        fileUploadFieldValue.Value.Should().BeEquivalentTo(creativeFile);
    }

    [Test]
    public async Task File_upload_field_value_can_be_created_with_custom_validation ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.FileUpload);
        var creativeFile = new CreativeFileId(1234);

        CreativeFile fileValue = GetCreativeFile(1234, "file1.txt");
        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.FileUploadExtensions, new List<string> {".txt"}, creativeField.Id).Value
        };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<CreativeFile>(It.IsAny<object?>()))
            .ReturnsAsync(fileValue);

        FileUploadFieldValue fileUploadFieldValue =
            (await FileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, creativeFile, validationRules, null)).Value;

        fileUploadFieldValue.CreativeFieldIdentifier.Should().Be(creativeField);
        fileUploadFieldValue.Value.Should().BeEquivalentTo(creativeFile);
    }

    [Test]
    public async Task File_upload_field_value_cant_be_created_with_custom_validation_failed ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.FileUpload);
        var creativeFile = new CreativeFileId(1234);

        CreativeFile fileValue = GetCreativeFile(1234, "file1.txt");
        var validationRules = new List<ValidationRule>
        {
            ValidationRule.Create(CreativeFieldValidationRuleType.FileUploadExtensions, new List<string> {".pdf"}, creativeField.Id).Value
        };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<CreativeFile>(It.IsAny<object?>()))
            .ReturnsAsync(fileValue);

        FluentResults.Result<FileUploadFieldValue> fileUploadFieldValue =
            await FileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, creativeFile, validationRules, null);

        fileUploadFieldValue.IsFailed.Should().BeTrue();
    }

    [Test]
    public async Task File_upload_field_value_can_be_created_without_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.FileUpload);

        var fileUploadFieldValueWithoutValue =
            (FileUploadFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        fileUploadFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);
        fileUploadFieldValueWithoutValue.Value.Should().BeNull();
    }

    [Test]
    public async Task File_upload_field_value_creation_with_invalid_value_type_fails ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.FileUpload);

        await Invoking(() =>
                FileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField, "invalid value", null, null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [TestCaseSource(nameof(_invalidCreativeFieldTypes))]
    public async Task File_upload_field_value_creation_with_invalid_creative_field_type_fails (
        CreativeFieldType invalidCreativeFieldType)
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345), invalidCreativeFieldType);

        await Invoking(() =>
                FileUploadFieldValue.Create(_sanitizerStrategyProviderMock.Object, creativeField,
                    new CreativeFileId(12345), null, null))
            .Should().ThrowExactlyAsync<ArgumentException>();
    }

    [Test]
    public async Task File_upload_field_value_generates_new_value ()
    {
        var creativeField = CreativeFieldIdentifier.Create(new CreativeFieldId(12345),
            CreativeFieldType.FileUpload);

        var fileUploadFieldValueWithoutValue =
            (FileUploadFieldValue)(await CreativeFieldValue.CreateWithoutValue(creativeField, new List<ValidationRule>())).Value;

        var creativeFile = new CreativeFileId(1234);

        CreativeFile fileValue = GetCreativeFile(1234, "file1.txt");

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<CreativeFile>(It.IsAny<object?>()))
            .ReturnsAsync(fileValue);

        var fileUploadFieldValueWithNewValue =
            (FileUploadFieldValue)(await fileUploadFieldValueWithoutValue.GenerateNewValue(creativeFile, new List<ValidationRule>(), _sanitizerStrategyProviderMock.Object)).Value;

        fileUploadFieldValueWithoutValue.Value.Should().BeNull();
        fileUploadFieldValueWithoutValue.CreativeFieldIdentifier.Should().Be(creativeField);

        fileUploadFieldValueWithNewValue.CreativeFieldIdentifier.Should().Be(creativeField);
        fileUploadFieldValueWithNewValue.Value.Should().Be(creativeFile);
    }

    private CreativeFile GetCreativeFile (long id, string fileName)
    {
        var creativeFileName = CreativeFileName.From(fileName);

        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(id);

        return CreativeFile.PrepareToUpload(_creativeFilesUploadPolicyMock.Object,
            _idManagerMock.Object, creativeFileName, Stream.Null).Value;
    }
}