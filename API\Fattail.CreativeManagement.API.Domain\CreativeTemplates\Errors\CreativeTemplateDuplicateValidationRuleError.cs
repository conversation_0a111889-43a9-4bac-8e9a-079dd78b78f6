﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;

public class CreativeTemplateDuplicateValidationRuleError : ErrorBase
{
    public CreativeTemplateDuplicateValidationRuleError (CreativeFieldId creativeFieldId, string entity, CreativeFieldValidationRuleType validationRuleType)
        : base($"The creative field with id {creativeFieldId} has more than one custom validation rule of the same type {validationRuleType}.",
            ErrorType.DuplicateCreativeTemplateValidationRule)
    {
        Metadata.Add(nameof(entity), entity);
        Metadata.Add(nameof(creativeFieldId), creativeFieldId);
        Metadata.Add(nameof(validationRuleType), validationRuleType);
    }
}