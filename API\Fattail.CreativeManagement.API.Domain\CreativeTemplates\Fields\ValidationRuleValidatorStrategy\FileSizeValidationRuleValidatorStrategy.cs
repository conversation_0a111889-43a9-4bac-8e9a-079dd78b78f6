﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Errors;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields.ValidationRuleValidatorStrategy;

public class FileSizeValidationRuleValidatorStrategy : IValidationRuleValidatorStrategy
{
    public Result IsValid (CreativeFieldId creativeFieldId, CreativeFieldValidationRuleType type, IReadOnlyList<string> options)
    {
        var result = Result.Ok();

        if (!options.Any())
        {
            return result;
        }

        if (!double.TryParse(options.Single(), out double _))
        {
            result = Result.Merge(result, Result.Fail(new CreativeTemplateInvalidFileSizeError(
                creativeFieldId.ToString(),
                nameof(CreativeField), CreativeFieldValidationRuleType.FileSize.ToString())));
        }

        return result;
    }
}