﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesMigrate;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeFilesMigrate;

[TestFixture]
public class CreativeFilesMigrateHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _creativeFileStorageManagerMock = new Mock<ICreativeFileStorageManager>();
        _creativeFileRepositoryMock = new Mock<ICreativeFileRepository>();

        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Int(1));

        _creativeFilesMigrateHandler = new CreativeFilesMigrateHandler(
            _idManagerMock.Object,
            _creativeFileStorageManagerMock.Object,
            _creativeFileRepositoryMock.Object);
    }

    private const int MaxSizeInMegabytesAllowed = 1;
    private const string AllowedExtension = ".jpg";

    private CreativeFilesMigrateHandler _creativeFilesMigrateHandler = null!;

    private readonly Faker<CreativeFileToMigrate> _creativeFileToMigrateFaker = new AutoFaker<CreativeFileToMigrate>()
        .RuleFor(creativeFileToUpload => creativeFileToUpload.FileName,
            fake => fake.System.CommonFileName(AllowedExtension))
        .RuleFor(creativeFileToUpload => creativeFileToUpload.FileLocation,
            fake => fake.Internet.Url());
    
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFileStorageManager> _creativeFileStorageManagerMock = null!;
    private Mock<ICreativeFileRepository> _creativeFileRepositoryMock = null!;

    [Test]
    public async Task Creative_file_is_stored ()
    {
        var creativeFileMigrateRequest =
            new CreativeFilesMigrateCommand(_creativeFileToMigrateFaker.Generate());
    
        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreAbOmsCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok(new Tuple<CreativeFileStorageMetadata, Stream>(AutoFaker.Generate<CreativeFileStorageMetadata>(), new MemoryStream())));
    
        await _creativeFilesMigrateHandler.Handle(creativeFileMigrateRequest, CancellationToken.None);
    
        _creativeFileStorageManagerMock.Verify(
            creativeFileManager =>
                creativeFileManager.StoreAbOmsCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<string>()),
            Times.Exactly(1));
    }

    [Test]
    public async Task Creative_file_returns_error_when_storing_fails ()
    {
        var creativeFileUploadRequest =
            new CreativeFilesMigrateCommand(_creativeFileToMigrateFaker.Generate());

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreAbOmsCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Fail("Error"));
        
        Result<CreativeFilesMigrateResult> migrateResult =
            await _creativeFilesMigrateHandler.Handle(creativeFileUploadRequest, CancellationToken.None);
        
        migrateResult.IsFailed.Should().BeTrue();
    }

    [Test]
    public async Task Creative_file_is_promoted_to_uploaded ()
    {
        CreativeFileToMigrate creativeFileToMigrate = _creativeFileToMigrateFaker.Generate();
        var creativeFileMigrateRequest = new CreativeFilesMigrateCommand(creativeFileToMigrate);
    
        CreativeFileStorageMetadata creativeFileStoredResult = AutoFaker.Generate<CreativeFileStorageMetadata>();
    
        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreAbOmsCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok(new Tuple<CreativeFileStorageMetadata, Stream>(creativeFileStoredResult, new MemoryStream())));
    
        _creativeFileRepositoryMock.Setup(creativeFileRepository =>
                creativeFileRepository.MigrateAsync<CreativeFilesMigrateResult>(It.IsAny<CreativeFile>()))
            .ReturnsAsync(new CreativeFilesMigrateResult(1, creativeFileToMigrate.FileName,
                creativeFileStoredResult.Location, creativeFileStoredResult.UploadedDate));
                
    
        Result<CreativeFilesMigrateResult> migrateResult =
            await _creativeFilesMigrateHandler.Handle(creativeFileMigrateRequest, CancellationToken.None);
    
        migrateResult.Value.Name.Should().Be(creativeFileToMigrate.FileName);
        migrateResult.Value.Location.Should().Be(creativeFileStoredResult.Location);
        migrateResult.Value.UploadedDate.Should().Be(creativeFileStoredResult.UploadedDate);
    }

    [Test]
    public async Task creative_file_is_created_in_repository ()
    {
        var creativeFileMigrateRequest =
            new CreativeFilesMigrateCommand(_creativeFileToMigrateFaker.Generate());

        _creativeFileStorageManagerMock.Setup(creativeFileManager =>
                creativeFileManager.StoreAbOmsCreativeFile(It.IsAny<CreativeFileName>(), It.IsAny<string>()))
            .ReturnsAsync(Result.Ok(
                new Tuple<CreativeFileStorageMetadata, Stream>(AutoFaker.Generate<CreativeFileStorageMetadata>(),
                    new MemoryStream())));

        _creativeFileRepositoryMock.Setup(creativeFileRepository =>
            creativeFileRepository.CreateAsync<CreativeFilesMigrateResult>(It.IsAny<CreativeFile>()));

        await _creativeFilesMigrateHandler.Handle(creativeFileMigrateRequest, CancellationToken.None);

        _creativeFileRepositoryMock.Verify(
            creativeFileRepository =>
                creativeFileRepository.MigrateAsync<CreativeFilesMigrateResult>(It.IsAny<CreativeFile>()),
            Times.Exactly(1));
    }
}