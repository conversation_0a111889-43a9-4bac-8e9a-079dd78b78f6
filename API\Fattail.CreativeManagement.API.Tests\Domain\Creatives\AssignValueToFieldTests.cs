﻿using Bogus;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Statuses;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentAssertions;
using FluentResults;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives;

[TestFixture]
public class AssignValueToFieldTests : CreativeTestsBase
{

    [SetUp]
    public void SetUp ()
    {
        _creativeFilesUploadPolicyMock = new Mock<ICreativeFilesUploadPolicy>();
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.MaxSizeInMegabytesAllowed).Returns(1000);
        _creativeFilesUploadPolicyMock.SetupGet(oap => oap.AllowedExtensions).Returns(new HashSet<string>(StringComparer.FromComparison(StringComparison.OrdinalIgnoreCase)) { ".txt" });

        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(new Faker().Random.Long(1));

        _updateInformation = UpdateInformation.Create("Pepe Grillo", DateTime.Now).Value;
    }

    private Mock<ICreativeFilesUploadPolicy> _creativeFilesUploadPolicyMock = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private readonly string _fieldName = "fieldName";
    private UpdateInformation _updateInformation = null!;

    [Test]
    public async Task Creative_assigns_value_to_field ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;

        CreativeFieldIdentifier creativeFieldIdentifier = creative.Fields.First().CreativeFieldIdentifier;

        var fileValues = new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.txt") };

        var fieldValue = new List<CreativeFileId> { new(1234), new(5678) };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object?>()))
            .ReturnsAsync(fileValues);

        var creativeField =
            CreativeFieldIdentifier.Create(creativeFieldIdentifier.Id, creativeFieldIdentifier.Type);

        await creative.AssignValueToField(creativeField, fieldValue, _updateInformation, new List<ValidationRule>(), _sanitizerStrategyProviderMock.Object);

        creative.Fields.Should()
            .Contain(creativeFieldValue => creativeFieldValue.CreativeFieldIdentifier == creativeField)
            .Which.Should().BeAssignableTo<MultiFileUploadFieldValue>()
            .Which.Value.Should().BeEquivalentTo(fieldValue);
    }

    [Test]
    public async Task Creative_assigns_no_value_to_field ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;

        CreativeFieldIdentifier creativeFieldIdentifier = creative.Fields.First().CreativeFieldIdentifier;

        var creativeField =
            CreativeFieldIdentifier.Create(creativeFieldIdentifier.Id, creativeFieldIdentifier.Type);

        var originalFieldValue = new List<CreativeFileId> { new(1234) };
        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IEnumerable<CreativeFileId>>(It.IsAny<object?>()))
            .ReturnsAsync(originalFieldValue);

        await creative.AssignValueToField(creativeField, originalFieldValue, _updateInformation, new List<ValidationRule>(), _sanitizerStrategyProviderMock.Object);

        await creative.AssignValueToField(creativeField, null, _updateInformation, new List<ValidationRule>());

        creative.Fields.Should()
            .Contain(creativeFieldValue => creativeFieldValue.CreativeFieldIdentifier == creativeField)
            .Which.Should().BeAssignableTo<MultiFileUploadFieldValue>()
            .Which.Value.Should().BeEmpty();
    }

    [Test]
    public async Task Assign_value_to_field_transition_creative_status_to_pending_approval ()
    {
        (DateTime _, CreativeTemplate _, string _, long _, long _, string _, Result<Creative> creativeResult) = await CreateCreative();

        Creative creative = creativeResult.Value;

        creative.Approve(ApprovalInformation.Create("Approver Name", DateTime.Now).Value);

        CreativeFieldIdentifier creativeFieldIdentifier = creative.Fields.First().CreativeFieldIdentifier;

        var fileValues = new List<CreativeFile> { GetCreativeFile(1234, "file1.txt"), GetCreativeFile(5678, "file2.txt") };

        var fieldValue = new List<CreativeFileId> { new(1234), new(5678) };

        _sanitizerMock.Setup(sanitizer => sanitizer.Sanitize<IReadOnlyList<CreativeFile>>(It.IsAny<object?>()))
            .ReturnsAsync(fileValues);

        var creativeField =
            CreativeFieldIdentifier.Create(creativeFieldIdentifier.Id, creativeFieldIdentifier.Type);

        await creative.AssignValueToField(creativeField, fieldValue, _updateInformation, new List<ValidationRule>(), _sanitizerStrategyProviderMock.Object);

        creative.Status.Should().Be(CreativeStatus.PendingApproval);
    }

    private CreativeFile GetCreativeFile(long id, string fileName)
    {
        var creativeFileName = CreativeFileName.From(fileName);

        _idManagerMock.Setup(idManager => idManager.GetId()).Returns(id);

        return CreativeFile.PrepareToUpload(_creativeFilesUploadPolicyMock.Object,
            _idManagerMock.Object, creativeFileName, Stream.Null).Value;
    }
}