﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeEdit;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.EditCreative;

public sealed class CreativeEditFunctionV4 : BaseFunction
{
    public CreativeEditFunctionV4 (
        IMediator mediator,
        IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Edit an existing creative v4", tags: new[] { "v4" },
        Summary = "Edit an existing creative v4")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeEditRequestV4),
        Description = "The creative to edit", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV4))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound, Description = "If the creative doesn't exist.")]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeEditFunctionV4))]
    public async Task<HttpResponseData?> EditCreative (
        [HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "v4/creatives/{id:long}")]
        HttpRequestData req,
        long id)
    {
        CreativeEditCommand creativeEditCommand = await FromRequest<CreativeEditRequestV4, CreativeEditCommand>(req,
            new Dictionary<string, object>
            {
                { CreativeEditV4AutoMapperProfile.CreativeEditCommandIdParameterName, id }
            });
        
        Result<CreativeResult> result = await _mediator.Send(creativeEditCommand);
        
        HttpResponseData response =
            await FromResult<CreativeResult, CreativeResponseV4>(req, result);
        
        return response;
    }
}