﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeSetLineItems;
using Fattail.CreativeManagement.API.Azure.Common;
using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.Common;
using Fattail.CreativeManagement.API.Azure.Middlewares;
using FluentResults;
using MediatR;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.OpenApi.Models;
using System.Net;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.v4.SetLineItems;

public sealed class CreativeSetLineItemsFunctionV4 : BaseFunction
{
    public CreativeSetLineItemsFunctionV4 (
        IMediator mediator,
        IMapper mapper) : base(mediator, mapper)
    {
    }

    [OpenApiOperation(
        "Creative set Line Items", tags: new[] { "v4" },
        Summary = "Creative set Line Items")]
    [OpenApiParameter(
        OrganizationContextHttpTriggerMiddleware.OrganizationIdHeaderName,
        Required = true,
        In = ParameterLocation.Header, Type = typeof(long))]
    [OpenApiParameter("id", Required = true, Type = typeof(long))]
    [OpenApiRequestBody(
        "application/json",
        typeof(CreativeSetLineItemsRequest),
        Description = "The creative to set line items", Required = true)]
    [OpenApiResponseWithBody(HttpStatusCode.OK, "application/json", typeof(CreativeResponseV4))]
    [OpenApiResponseWithoutBody(HttpStatusCode.NotFound, Description = "If the creative doesn't exist.")]
    [OpenApiResponseWithBody(
        HttpStatusCode.BadRequest,
        "application/json",
        typeof(List<ErrorInformation>))]
    [OpenApiResponseWithBody(
        HttpStatusCode.InternalServerError,
        "application/json",
        typeof(ErrorInformation))]
    [OpenApiResponseWithoutBody(HttpStatusCode.Unauthorized)]
    [Function(nameof(CreativeSetLineItemsFunctionV4))]
    public async Task<HttpResponseData?> AssignCreativeToLineItemsV4(
        [HttpTrigger(AuthorizationLevel.Anonymous, "patch", Route = "v4/creatives/{id:long}/set-line-items")]
        HttpRequestData req,
        long id)
    {
        CreativeSetLineItemsCommand creativeSetLineItemsCommand =
            await FromRequest<CreativeSetLineItemsRequest, CreativeSetLineItemsCommand>(req,
                new Dictionary<string, object>
                {
                    {
                        CreativeSetLineItemsV4AutoMapperProfile.CreativeSetLineItemsCommandIdParameterName, id
                    }
                });

        Result<CreativeResult> result = await _mediator.Send(creativeSetLineItemsCommand);

        HttpResponseData response =
            await FromResult<CreativeResult, CreativeResponseV4>(req, result);

        return response;
    }
}