﻿using AutoMapper;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Fattail.CreativeManagement.API.Infrastructure.QueryServices.Common;
using Microsoft.Azure.Cosmos;

namespace Fattail.CreativeManagement.API.Infrastructure.QueryServices.CreativeTemplates;
internal sealed class PredefinedCreativeTemplateQueryService (
    ICosmosDbContainerFactory cosmosDbContainerFactory,
    IConfigurationProvider configurationProvider,
    IOrganizationContext organizationContext,
    IMapper mapper)
    : QueryService<CreativeTemplate>(cosmosDbContainerFactory, configurationProvider, organizationContext, mapper)
{
    public override string ContainerName => "CreativeTemplates";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey(PredefinedPartitions.Shared.ToString());
    }
}
