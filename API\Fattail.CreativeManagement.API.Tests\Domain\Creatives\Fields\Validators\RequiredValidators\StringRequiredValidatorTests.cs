﻿using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators.RequiredValidators;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Domain.Creatives.Fields.Validators.RequiredValidators;

[TestFixture]
public class StringRequiredValidatorTests
{
    private CreativeFieldIdentifier _creativeFieldIdentifier = null!;
    private StringRequiredRuleValidator _stringRequiredRuleValidator = null!;

    [SetUp]
    public void SetUp ()
    {
        var creativeFieldId = new CreativeFieldId(123456);
        CreativeFieldType creativeFieldType = CreativeFieldType.MultiFileUpload;
        _creativeFieldIdentifier = CreativeFieldIdentifier.Create(creativeFieldId, creativeFieldType);
    }

    [Test]
    public async Task String_field_value_with_valid_value_is_valid ()
    {
        string fieldValue = "nOt Empty Field VAlue 909";
        _stringRequiredRuleValidator = new StringRequiredRuleValidator(_creativeFieldIdentifier);

        Result result = await _stringRequiredRuleValidator.IsValid(fieldValue);

        result.Should().BeSuccess();
    }

    [TestCase("")]
    [TestCase(" ")]
    [TestCase(null)]
    public async Task String_field_value_with_invalid_value_is_invalid (string invalidInput)
    {
        _stringRequiredRuleValidator = new StringRequiredRuleValidator(_creativeFieldIdentifier);

        Result result = await _stringRequiredRuleValidator.IsValid(invalidInput);

        result.Should().BeFailure().And
            .HaveReason(new RequiredValidationError(nameof(CreativeFieldValue), nameof(Creative),
                _creativeFieldIdentifier.Id.ToString()));
    }
}