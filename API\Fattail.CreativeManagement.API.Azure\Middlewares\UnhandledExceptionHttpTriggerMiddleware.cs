﻿using Fattail.CreativeManagement.API.Azure.Common.Responses;
using Fattail.CreativeManagement.API.Azure.Middlewares.Errors;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.Functions.Worker.Middleware;
using Microsoft.Extensions.Logging;
using System.Net;


namespace Fattail.CreativeManagement.API.Azure.Middlewares;

internal sealed class UnhandledExceptionHttpTriggerMiddleware : IFunctionsWorkerMiddleware
{
    private readonly ILogger _logger;

    public UnhandledExceptionHttpTriggerMiddleware (ILogger<UnhandledExceptionHttpTriggerMiddleware> logger)
    {
        _logger = logger;
    }

    public async Task Invoke (FunctionContext context, FunctionExecutionDelegate next)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,  $" {context.FunctionDefinition.Name} - Unhandled exception occurred.");
            HttpRequestData request = (await context.GetHttpRequestDataAsync())!;

            await request.AssignErrorResponse(new ErrorInformation(
                "An unexpected error occurred. Please try again later or contact customer support for assistance.",
                (int)ApiErrorType.UnhandledException,
                ApiErrorType.UnhandledException.ToString(),
                new Dictionary<string, string>()), HttpStatusCode.InternalServerError);
        }
    }
}