﻿using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Microsoft.OpenApi.Models;

namespace Fattail.CreativeManagement.API.Azure.Endpoints.Creatives.Common.OpenApiModel;
public class PolymorphicCreativeFieldRequestOpenApiModel : OpenApiSchema
{
    public PolymorphicCreativeFieldRequestOpenApiModel()
    {
        AnyOf = new List<OpenApiSchema>
        {
            new MultiFileUploadFieldValueOpenApiModel(),
            new FileUploadFieldValueOpenApiModel(),
            new SingleLineTextFieldValueOpenApiModel(),
            new MultiSelectOptionFieldValueOpenApiModel(),
            new SingleSelectOptionFieldValueOpenApiModel(),
            new MultiLineTextFieldValueOpenApiModel()
        };
    }
}

