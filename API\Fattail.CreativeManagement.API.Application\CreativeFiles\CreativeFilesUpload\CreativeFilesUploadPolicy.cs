﻿using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using System.Diagnostics.CodeAnalysis;

namespace Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;

[ExcludeFromCodeCoverage]
internal class CreativeFilesUploadPolicy : ICreativeFilesUploadPolicy
{
    public long MaxSizeInMegabytesAllowed => 50;

    public IReadOnlySet<string> AllowedExtensions => new HashSet<string>(StringComparer.FromComparison(StringComparison.OrdinalIgnoreCase))
    {
        ".txt",
        ".csv",
        ".htm",
        ".html",
        ".pdf",
        ".gif",
        ".jpg",
        ".jpeg",
        ".tif",
        ".tiff",
        ".xlsx",
        ".png",
        ".xls",
        ".jpg",
        ".jpeg",
        ".gif",
        ".png",
        ".wbmp",
        ".avi",
        ".flv",
        ".mov",
        ".mp4",
        ".m4v",
        ".mpeg",
        ".mpg",
        ".oga",
        ".ogg",
        ".ogv",
        ".webm",
        ".wmv",
        ".htm",
        ".html",
        ".swf",
        ".doc",
        ".docx",
        ".psd",
        ".zip",
        ".svg",
        ".ppt",
        ".pptx"
    };
}